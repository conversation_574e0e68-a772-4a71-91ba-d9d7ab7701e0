<?php
// Test filter functionality
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Filter Debug</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2>Test Filter Debug</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Filters</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Status Filter</label>
                            <select id="statusFilter" class="form-select">
                                <option value="">ทั้งหมด</option>
                                <option value="pending">Pending</option>
                                <option value="sent_to_inventory">Sent to Inventory</option>
                                <option value="sent_to_mail">Sent to Mail</option>
                                <option value="sent_to_warehouse">Sent to Warehouse</option>
                                <option value="sent_to_event_inventory">Sent to Event Inventory</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Send Method Filter</label>
                            <select id="methodFilter" class="form-select">
                                <option value="">ทั้งหมด</option>
                                <option value="inventory">Inventory</option>
                                <option value="mail">Mail</option>
                                <option value="warehouse">Warehouse</option>
                                <option value="event_inventory">Event Inventory</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">User ID</label>
                            <input type="text" id="adminFilter" class="form-control" placeholder="ID ผู้ใช้งาน">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Player Username</label>
                            <input type="text" id="playerFilter" class="form-control" placeholder="ชื่อผู้เล่น">
                        </div>
                        
                        <button id="testFilter" class="btn btn-primary">Test Filter</button>
                        <button id="clearTest" class="btn btn-secondary">Clear</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Output</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugOutput" class="bg-dark text-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: monospace;">
                            <!-- Debug output will appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>API Response</h5>
                    </div>
                    <div class="card-body">
                        <pre id="apiResponse" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function addToDebug(message, type = 'info') {
            const debug = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            debug.innerHTML += `<div style="color: ${colors[type] || '#ffffff'};">[${timestamp}] ${message}</div>`;
            debug.scrollTop = debug.scrollHeight;
        }

        $(document).ready(function() {
            addToDebug('Filter test initialized', 'info');
            
            $('#testFilter').click(async function() {
                addToDebug('Starting filter test...', 'info');
                
                const filters = {
                    limit: 50,
                    status: $('#statusFilter').val(),
                    send_method: $('#methodFilter').val(),
                    admin_username: $('#adminFilter').val(),
                    player_username: $('#playerFilter').val()
                };
                
                // Remove empty filters
                Object.keys(filters).forEach(key => {
                    if (!filters[key]) delete filters[key];
                });
                
                addToDebug('Filters to send: ' + JSON.stringify(filters), 'info');
                
                const queryString = new URLSearchParams(filters).toString();
                addToDebug('Query string: ' + queryString, 'info');
                
                const url = `files/game_systems/get_item_history.php?${queryString}`;
                addToDebug('Request URL: ' + url, 'info');
                
                try {
                    const response = await fetch(url);
                    addToDebug('Response status: ' + response.status, response.ok ? 'success' : 'error');
                    
                    const data = await response.json();
                    addToDebug('Response received', 'success');
                    
                    // Display API response
                    document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                    
                    if (data.success) {
                        addToDebug(`Success! Found ${data.count} records`, 'success');
                        
                        // Show sample data
                        if (data.history && data.history.length > 0) {
                            addToDebug('Sample record:', 'info');
                            addToDebug(JSON.stringify(data.history[0], null, 2), 'info');
                        }
                        
                        // Check if filters worked
                        if (filters.status) {
                            const filteredCount = data.history.filter(item =>
                                (item.status || item.dbStatus) === filters.status
                            ).length;
                            addToDebug(`Records matching status '${filters.status}': ${filteredCount}`, 'info');
                        }

                        if (filters.send_method) {
                            const filteredCount = data.history.filter(item =>
                                (item.sendMethod || item.send_method) === filters.send_method
                            ).length;
                            addToDebug(`Records matching send_method '${filters.send_method}': ${filteredCount}`, 'info');
                        }
                        
                    } else {
                        addToDebug('API Error: ' + data.error, 'error');
                    }
                    
                } catch (error) {
                    addToDebug('Fetch Error: ' + error.message, 'error');
                    document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                }
            });
            
            $('#clearTest').click(function() {
                $('#statusFilter').val('');
                $('#methodFilter').val('');
                $('#adminFilter').val('');
                $('#playerFilter').val('');
                document.getElementById('debugOutput').innerHTML = '';
                document.getElementById('apiResponse').textContent = '';
                addToDebug('Test cleared', 'info');
            });
        });
    </script>
</body>
</html>
