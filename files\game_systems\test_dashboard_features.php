<?php
// Test Dashboard Features
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Features</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-dashboard me-2"></i>Test Dashboard Features</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Features Added</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Enhanced Dashboard Features:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-bolt text-primary me-2"></i>
                                Quick Actions Panel with search
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-chart-bar text-success me-2"></i>
                                Real-time Statistics from database
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-stream text-info me-2"></i>
                                Recent Activity Feed (live updates)
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-sync text-warning me-2"></i>
                                Auto-refresh functionality
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-search text-secondary me-2"></i>
                                Search functionality
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-bell text-primary me-2"></i>
                                Toast notifications
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Features</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="testStatsAPI" class="btn btn-primary">
                                <i class="fas fa-chart-line me-2"></i>Test Stats API
                            </button>
                            
                            <button id="testActivityAPI" class="btn btn-success">
                                <i class="fas fa-stream me-2"></i>Test Activity API
                            </button>
                            
                            <button id="testNotifications" class="btn btn-info">
                                <i class="fas fa-bell me-2"></i>Test Notifications
                            </button>
                            
                            <a href="?url=game_systems/index" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Enhanced Dashboard
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-list me-2"></i>New Features Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">🚀 Quick Actions Panel:</h6>
                                <ul class="small">
                                    <li>One-click access to main systems</li>
                                    <li>Search functionality across systems</li>
                                    <li>Refresh stats button</li>
                                    <li>Responsive design</li>
                                </ul>
                                
                                <h6 class="text-success mt-3">📊 Real-time Statistics:</h6>
                                <ul class="small">
                                    <li>Total items sent from database</li>
                                    <li>Total admin logs count</li>
                                    <li>System status indicator</li>
                                    <li>Last activity timestamp</li>
                                    <li>Auto-refresh every 30 seconds</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">📱 Recent Activity Feed:</h6>
                                <ul class="small">
                                    <li>Live feed of recent admin actions</li>
                                    <li>Shows admin, action, target player</li>
                                    <li>Auto-refresh every 15 seconds</li>
                                    <li>Scrollable sidebar layout</li>
                                </ul>
                                
                                <h6 class="text-warning mt-3">⚡ Interactive Features:</h6>
                                <ul class="small">
                                    <li>Toast notifications for feedback</li>
                                    <li>Search highlighting</li>
                                    <li>Smooth animations</li>
                                    <li>Error handling with fallbacks</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-code me-2"></i>API Endpoints</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📡 Statistics API:</h6>
                                <div class="bg-light p-3 rounded">
                                    <code>GET /files/game_systems/api/get_stats.php</code>
                                    <br><small class="text-muted">Returns real-time dashboard statistics</small>
                                </div>
                                
                                <h6 class="mt-3">📋 Activity API:</h6>
                                <div class="bg-light p-3 rounded">
                                    <code>GET /files/game_systems/log_advanced_editor.php?limit=5</code>
                                    <br><small class="text-muted">Returns recent admin activities</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📊 Response Format:</h6>
                                <pre class="bg-dark text-light p-3 rounded small">{
  "success": true,
  "stats": {
    "total_items_sent": 123,
    "total_admin_logs": 456,
    "system_status": "online",
    "last_activity": "2024-01-01 12:00:00",
    "health_score": 95
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>How to Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Testing Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Open Enhanced Dashboard:</strong> Click "Open Enhanced Dashboard" button above</li>
                                <li><strong>Test Quick Actions:</strong> Try the quick action buttons in the top panel</li>
                                <li><strong>Test Search:</strong> Use the search box to find systems</li>
                                <li><strong>Check Statistics:</strong> Verify real-time stats are displayed</li>
                                <li><strong>Watch Activity Feed:</strong> Check the sidebar for recent activities</li>
                                <li><strong>Test Auto-refresh:</strong> Wait 30 seconds to see stats refresh</li>
                                <li><strong>Test Notifications:</strong> Look for toast notifications</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6>Expected Behavior:</h6>
                            <ul class="mb-0">
                                <li>✅ Statistics show real numbers from database</li>
                                <li>✅ Activity feed shows recent admin actions</li>
                                <li>✅ Quick actions open systems in new tabs</li>
                                <li>✅ Search highlights matching cards</li>
                                <li>✅ Auto-refresh updates data periodically</li>
                                <li>✅ Notifications appear for user feedback</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
        
        $(document).ready(function() {
            $('#testStatsAPI').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing Stats API...</div>');
                
                try {
                    const response = await fetch('api/get_stats.php');
                    const data = await response.json();
                    
                    if (data.success) {
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Stats API Test Successful!</h6>
                                <p><strong>Items Sent:</strong> ${data.stats.total_items_sent}</p>
                                <p><strong>Admin Logs:</strong> ${data.stats.total_admin_logs}</p>
                                <p><strong>System Status:</strong> ${data.stats.system_status}</p>
                                <p class="mb-0"><strong>Health Score:</strong> ${data.stats.health_score}%</p>
                            </div>
                        `);
                        showNotification('Stats API working correctly!', 'success');
                    } else {
                        throw new Error(data.error || 'Unknown error');
                    }
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>Stats API Test Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                    showNotification('Stats API test failed', 'error');
                }
            });
            
            $('#testActivityAPI').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing Activity API...</div>');
                
                try {
                    const response = await fetch('log_advanced_editor.php?limit=3');
                    const data = await response.json();
                    
                    if (data.success) {
                        const activities = data.logs.slice(0, 3);
                        let activitiesHtml = '<ul class="mb-0">';
                        activities.forEach(activity => {
                            activitiesHtml += `<li>${activity.action} by ${activity.admin_username}</li>`;
                        });
                        activitiesHtml += '</ul>';
                        
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Activity API Test Successful!</h6>
                                <p><strong>Recent Activities:</strong></p>
                                ${activitiesHtml}
                            </div>
                        `);
                        showNotification('Activity API working correctly!', 'success');
                    } else {
                        throw new Error(data.error || 'Unknown error');
                    }
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>Activity API Test Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                    showNotification('Activity API test failed', 'error');
                }
            });
            
            $('#testNotifications').click(function() {
                showNotification('This is a test notification!', 'info');
                setTimeout(() => showNotification('Success notification test', 'success'), 1000);
                setTimeout(() => showNotification('Warning notification test', 'warning'), 2000);
                setTimeout(() => showNotification('Error notification test', 'error'), 3000);
            });
            
            // Auto-run stats test
            setTimeout(() => {
                $('#testStatsAPI').click();
            }, 1000);
        });
    </script>
</body>
</html>
