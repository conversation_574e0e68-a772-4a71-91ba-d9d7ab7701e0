<?php
// Test CORS fix for DataTables
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CORS Fix - DataTables</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2>Test CORS Fix - DataTables Thai Language</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Table</h5>
            </div>
            <div class="card-body">
                <table id="testTable" class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>Test Item 1</td>
                            <td><span class="badge bg-success">Active</span></td>
                            <td>2024-01-01</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Test Item 2</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                            <td>2024-01-02</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Test Item 3</td>
                            <td><span class="badge bg-danger">Failed</span></td>
                            <td>2024-01-03</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="mt-4">
            <h5>Test Results:</h5>
            <div id="testResults" class="alert alert-info">
                กำลังทดสอบ...
            </div>
        </div>
        
        <div class="mt-4">
            <h5>Console Output:</h5>
            <div id="consoleOutput" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace;">
                <!-- Console messages will appear here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        function addToConsole(message, type = 'info') {
            const console = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#17a2b8',
                'success': '#28a745',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            console.innerHTML += `<div style="color: ${colors[type] || '#ffffff'};">[${timestamp}] ${message}</div>`;
            console.scrollTop = console.scrollHeight;
        }

        $(document).ready(function() {
            addToConsole('Starting DataTables test...', 'info');
            
            try {
                // Test with inline Thai language (no CORS issue)
                const table = $('#testTable').DataTable({
                    order: [[0, 'desc']],
                    pageLength: 10,
                    responsive: true,
                    language: {
                        "decimal": "",
                        "emptyTable": "ไม่มีข้อมูลในตาราง",
                        "info": "แสดง _START_ ถึง _END_ จาก _TOTAL_ รายการ",
                        "infoEmpty": "แสดง 0 ถึง 0 จาก 0 รายการ",
                        "infoFiltered": "(กรองจาก _MAX_ รายการทั้งหมด)",
                        "infoPostFix": "",
                        "thousands": ",",
                        "lengthMenu": "แสดง _MENU_ รายการ",
                        "loadingRecords": "กำลังโหลด...",
                        "processing": "กำลังประมวลผล...",
                        "search": "ค้นหา:",
                        "zeroRecords": "ไม่พบข้อมูลที่ตรงกัน",
                        "paginate": {
                            "first": "หน้าแรก",
                            "last": "หน้าสุดท้าย",
                            "next": "ถัดไป",
                            "previous": "ก่อนหน้า"
                        },
                        "aria": {
                            "sortAscending": ": เปิดใช้งานการเรียงลำดับจากน้อยไปมาก",
                            "sortDescending": ": เปิดใช้งานการเรียงลำดับจากมากไปน้อย"
                        }
                    }
                });
                
                addToConsole('✅ DataTables initialized successfully with Thai language', 'success');
                
                // Test adding data
                table.row.add([
                    '4',
                    'Dynamic Item',
                    '<span class="badge bg-info">New</span>',
                    new Date().toLocaleDateString('th-TH')
                ]).draw();
                
                addToConsole('✅ Dynamic row added successfully', 'success');
                
                // Update test results
                document.getElementById('testResults').innerHTML = `
                    <h6>✅ Test Passed!</h6>
                    <ul>
                        <li>DataTables initialized without CORS errors</li>
                        <li>Thai language working properly</li>
                        <li>Dynamic row addition working</li>
                        <li>No external language file needed</li>
                    </ul>
                `;
                document.getElementById('testResults').className = 'alert alert-success';
                
            } catch (error) {
                addToConsole('❌ Error: ' + error.message, 'error');
                
                document.getElementById('testResults').innerHTML = `
                    <h6>❌ Test Failed!</h6>
                    <p>Error: ${error.message}</p>
                `;
                document.getElementById('testResults').className = 'alert alert-danger';
            }
        });
        
        // Override console methods to capture errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            addToConsole('Console Error: ' + args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };
        
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            addToConsole('Console Warning: ' + args.join(' '), 'warning');
            originalConsoleWarn.apply(console, args);
        };
        
        // Capture network errors
        window.addEventListener('error', function(e) {
            if (e.message.includes('CORS') || e.message.includes('Access-Control')) {
                addToConsole('CORS Error detected: ' + e.message, 'error');
            }
        });
    </script>
</body>
</html>
