<?php $user->restrictionUser(true, $conn); ?>
<div class="subheader">
    <h1 class="subheader-title">
        <i class="subheader-icon fal fa-window"></i> Guild
        <small> ระบบตรวจสอบGuild</small>
    </h1>
</div>

<div class="row">
    <div class="col-xl-12">
        <div id="panel-1" class="panel">
            <div class="panel-hdr">
                <h2>MyCashItem <span class="fw-300"><i>ตารางแอดไอเท็ม</i></span> </h2>
                <div class="panel-toolbar">
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-collapse"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-fullscreen"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
                    <button class="btn btn-panel waves-effect waves-themed" data-action="panel-close"
                        data-toggle="tooltip" data-offset="0,10" data-original-title="Close"></button>
                </div>
            </div>
            <div class="panel-container show">
                <div class="panel-content">
                    <table id="dt-iteminvent" class="table table-sm table-bordered w-100">
                        <thead>
                            <tr>
                                <th>ClanID</th>
                                <th>ชื่อกิลล์</th>
                                <th>กิลล์เลเวล</th>
								<th>ประกาศกิลล์</th>
                                <th>กิลล์ พ้อย</th>
                                <th>วันที่สร้าง</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            function getPage($stmt, $pageNum, $rowsPerPage) {
                                $offset = ($pageNum - 1) * $rowsPerPage;
                                $rows = array();
                                $i = 0;
                                while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                    array_push($rows, $row);
                                    $i++;
                                }
                                return $rows;
                            }

                        $rowsPerPage = 1000;
                        $sql = "SELECT * FROM ".DATABASE_SV.".dbo.Guild";
                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                        } else {
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            $selectOwner = "SELECT * FROM ".DATABASE_SV.".dbo.Guild WHERE GuildNo = '$row[0]'";
                            $selectOwnerParam = array();
                            $selectOwnerQuery = sqlsrv_query($conn, $selectOwner, $selectOwnerParam);
                            $selectOwnerFetch = sqlsrv_fetch_array($selectOwnerQuery, SQLSRV_FETCH_ASSOC);
                            ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td><?php echo $userLogin->thaitrans($row[2]); ?></td>
                                <td><?php echo $row[10]; ?></td>
                                <td>
                                <?php
                                    echo is_null($row[7]) ? '' : $userLogin->thaitrans($row[7]);
                                ?>
                                </td>
                                <td><?php echo $row[11]; ?></td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[4])); ?></td>

                            </tr>
                            <?php
                        }
                        ?>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>


</div>