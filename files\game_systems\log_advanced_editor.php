<?php
// Check session and permissions
require_once '../../_app/dbinfo.inc.php';

// You might want to add permission checks here
// require_once '../../_app/php/zpanel.class.php';
// $zpanel = new zpanel();
// $zpanel->checkSession(true);

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session to get admin info
session_start();

/**
 * Advanced Editor Logging API
 * Reads existing logs from database (no longer creates new logs)
 * Item sending activities are automatically logged by the item system
 */

function logToDatabase($conn, $logData) {
    // No longer inserting logs - item sending system handles logging automatically
    // This function is kept for compatibility but does nothing
    // All item send activities are already logged in the item_sends table
    return true;
}

function getRecentLogs($conn, $limit = 100, $filters = []) {
    try {
        // First check what tables exist
        $tablesQuery = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
        $tablesResult = sqlsrv_query($conn, $tablesQuery);
        $availableTables = [];

        if ($tablesResult) {
            while ($table = sqlsrv_fetch_array($tablesResult, SQLSRV_FETCH_ASSOC)) {
                $availableTables[] = $table['TABLE_NAME'];
            }
        }

        error_log("Available tables: " . implode(', ', $availableTables));

        // Try different table options in order of preference
        $tableOptions = [
            'admin_logs' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    id, admin_username, action, target_player, details,
                    ip_address, user_agent, created_at
                    FROM admin_logs",
                'filter_condition' => null  // Remove filter to show all admin_logs
            ],
            'item_sends' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    id, admin_username, 'ITEM_SEND' as action, player_username as target_player,
                    CONCAT('Sent ', quantity, 'x ', item_name, ' to ', player_username) as message,
                    created_at
                    FROM item_sends",
                'filter_condition' => null
            ],
            'WEB_Log_gm' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    LogID as id, 'Admin' as admin_username, action,
                    '' as target_player, msg as message, '' as details,
                    '' as ip_address, '' as user_agent, '' as created_at
                    FROM WEB_Log_gm",
                'filter_condition' => null
            ]
        ];

        $selectedTable = null;
        $selectedConfig = null;

        // Find the first available table
        foreach ($tableOptions as $tableName => $config) {
            if (in_array($tableName, $availableTables)) {
                $selectedTable = $tableName;
                $selectedConfig = $config;
                break;
            }
        }

        if (!$selectedTable) {
            // Return mock data if no tables available
            return [
                [
                    'id' => 1,
                    'session_id' => 'demo_session',
                    'admin_username' => 'Demo',
                    'log_level' => 'info',
                    'action' => 'SYSTEM_INFO',
                    'message' => 'No logging tables found in database. Please run database setup.',
                    'target_player' => null,
                    'item_id' => null,
                    'item_code' => null,
                    'options_code' => null,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];
        }

        $sql = $selectedConfig['sql'];
        $whereConditions = [];
        $params = [];

        // Add table-specific filter
        if ($selectedConfig['filter_condition']) {
            $whereConditions[] = $selectedConfig['filter_condition'];
        }

        // Add user filters
        if (!empty($filters['admin_username'])) {
            $whereConditions[] = "admin_username = ?";
            $params[] = $filters['admin_username'];
        }

        if (!empty($filters['log_level']) && $selectedTable === 'admin_logs') {
            $whereConditions[] = "action LIKE ?";
            $params[] = '[' . $filters['log_level'] . ']%';
        }

        if (!empty($filters['action'])) {
            $whereConditions[] = "action LIKE ?";
            $params[] = '%' . $filters['action'] . '%';
        }

        if (!empty($filters['target_player'])) {
            $whereConditions[] = "target_player = ?";
            $params[] = $filters['target_player'];
        }

        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        // Add ORDER BY for proper sorting (most recent first)
        if ($selectedTable === 'admin_logs') {
            $sql .= " ORDER BY id DESC, created_at DESC";
        } else {
            $sql .= " ORDER BY id DESC";
        }

        error_log("Using table: $selectedTable");
        error_log("SQL: " . $sql);
        error_log("Params: " . print_r($params, true));

        if (empty($params)) {
            $stmt = sqlsrv_query($conn, $sql);
        } else {
            $stmt = sqlsrv_prepare($conn, $sql, $params);
            if ($stmt === false) {
                throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
            }
            $stmt = sqlsrv_execute($stmt) ? $stmt : false;
        }

        if ($stmt === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }

        $logs = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            if ($selectedTable === 'admin_logs') {
                // Parse admin_logs format according to actual table structure
                $logEntry = [
                    'id' => $row['id'],
                    'admin_username' => $row['admin_username'],
                    'action' => $row['action'],
                    'target_player' => $row['target_player'],
                    'details' => $row['details'],
                    'ip_address' => $row['ip_address'],
                    'user_agent' => $row['user_agent'],
                    'created_at' => is_object($row['created_at']) ?
                        $row['created_at']->format('Y-m-d H:i:s') : $row['created_at']
                ];
            } else {
                // Parse other table formats
                $logEntry = [
                    'id' => $row['id'],
                    'session_id' => '',
                    'admin_username' => $row['admin_username'] ?? 'System',
                    'log_level' => 'info',
                    'action' => $row['action'] ?? 'UNKNOWN',
                    'message' => $row['message'] ?? '',
                    'target_player' => $row['target_player'] ?? null,
                    'item_id' => null,
                    'item_code' => null,
                    'options_code' => null,
                    'created_at' => is_object($row['created_at']) ?
                        $row['created_at']->format('Y-m-d H:i:s') : ($row['created_at'] ?? date('Y-m-d H:i:s'))
                ];
            }

            $logs[] = $logEntry;
        }

        error_log("Retrieved " . count($logs) . " records from $selectedTable");
        return $logs;

    } catch (Exception $e) {
        error_log("Get Recent Logs Error: " . $e->getMessage());

        // Return error log as mock data
        return [
            [
                'id' => 0,
                'session_id' => 'error_session',
                'admin_username' => 'System',
                'log_level' => 'error',
                'action' => 'DATABASE_ERROR',
                'message' => 'Failed to retrieve logs: ' . $e->getMessage(),
                'target_player' => null,
                'item_id' => null,
                'item_code' => null,
                'options_code' => null,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
    }
}

// Main API handler
try {
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        // No longer logging to database - item sending system handles this automatically
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('Invalid JSON input');
        }

        // Return success without logging (item sends are logged by the item system)
        echo json_encode([
            'success' => true,
            'message' => 'Log request received - item sends are logged automatically by the system',
            'log_id' => 'handled_by_item_system'
        ]);
        
    } elseif ($method === 'GET') {
        // Get recent logs
        $limit = intval($_GET['limit'] ?? 100);
        $filters = [
            'admin_username' => $_GET['admin_username'] ?? null,
            'log_level' => $_GET['log_level'] ?? null,
            'action' => $_GET['action'] ?? null,
            'target_player' => $_GET['target_player'] ?? null,
            'date_from' => $_GET['date_from'] ?? null,
            'date_to' => $_GET['date_to'] ?? null
        ];

        // Remove empty filters
        $filters = array_filter($filters, function($value) {
            return !empty($value);
        });

        error_log("Advanced Editor Logs GET Request - Limit: $limit, Filters: " . print_r($filters, true));

        $logs = getRecentLogs($conn, $limit, $filters);

        echo json_encode([
            'success' => true,
            'logs' => $logs,
            'count' => count($logs),
            'debug' => [
                'limit' => $limit,
                'filters' => $filters,
                'request_time' => date('Y-m-d H:i:s')
            ]
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>
