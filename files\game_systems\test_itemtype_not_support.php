<?php
// Test itemType NOT support
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ItemType NOT Support</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-cube me-2"></i>Test ItemType "NOT" Support</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Changes Made</h5>
                    </div>
                    <div class="card-body">
                        <h6>ItemManager.js Updates:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added "NOT" to itemMapName with {"0": "NOT"}
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added "NOT" pattern to itemTypePatterns
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-edit text-info me-2"></i>
                                Updated error message to warning for missing itemType
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-primary me-2"></i>
                                Added fallback to 'NOT' when itemType is undefined
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test ItemType NOT</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า itemType "NOT" ทำงานได้โดยไม่แสดง error</p>
                        </div>
                        
                        <button id="testItemTypeNOT" class="btn btn-primary me-2">
                            <i class="fas fa-play me-2"></i>Test ItemType NOT
                        </button>
                        
                        <button id="testAdvancedEditor" class="btn btn-success">
                            <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                        </button>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">1. Added to itemMapName:</h6>
                                <pre class="bg-light p-3 rounded small">this.itemMapName = {
    "NOT": [
        {"0": "NOT"}
    ],
    "Helm": [
        // existing helm options...
    ],
    // other item types...
};</pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">2. Added to itemTypePatterns:</h6>
                                <pre class="bg-light p-3 rounded small">this.itemTypePatterns = {
    "NOT": /^NOT$|^not$|^default$/i,
    "Helm": /helm|hat|cap|crown|circlet|headband/i,
    // other patterns...
};</pre>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">3. Updated Error Handling:</h6>
                                <pre class="bg-light p-3 rounded small">// Before:
console.log(`❌ No itemMapName data found for itemType: ${finalItemType}`);

// After:
console.log(`⚠️ No itemMapName data found for itemType: ${finalItemType}, using default NOT behavior`);

// Added fallback:
itemType: finalItemType || 'NOT'</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Expected Behavior</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>When itemType is "NOT":</h6>
                            <ul class="mb-0">
                                <li>✅ No error messages in console</li>
                                <li>✅ Options code calculation returns 0</li>
                                <li>✅ All slot values default to "NOT" (hex: "0")</li>
                                <li>✅ Item sending works normally</li>
                                <li>✅ Admin logs are created successfully</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6>How to Verify:</h6>
                            <ol class="mb-0">
                                <li>เปิด Advanced Editor</li>
                                <li>ตรวจสอบว่า Item Type เป็น "NOT" (default)</li>
                                <li>กรอกข้อมูลไอเทมและส่ง</li>
                                <li>ดู Console ว่าไม่มี error "No itemMapName data found"</li>
                                <li>ตรวจสอบ admin_logs ว่ามีการบันทึก</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-bug me-2"></i>Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo">
                            <h6>Browser Console Messages to Look For:</h6>
                            <div class="bg-dark text-light p-3 rounded">
                                <div class="text-success">✅ Good Messages:</div>
                                <div class="font-monospace small">
                                    🔧 Item Type set to default: NOT<br>
                                    🔍 calculateAdvancedOptionsCode: itemName="", itemType="NOT"<br>
                                    🎯 Final optionsCode: 0 (Hex: 0)<br>
                                    ✅ Using advanced calculation for item type: "NOT"
                                </div>
                                
                                <div class="text-danger mt-3">❌ Bad Messages (should not appear):</div>
                                <div class="font-monospace small">
                                    ❌ No itemMapName data found for itemType: NOT<br>
                                    Failed to load resource: 404 (Not Found)<br>
                                    TypeError: Cannot read properties of undefined
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load ItemManager for testing -->
    <script src="js/item-manager.js"></script>

    <script>
        $(document).ready(function() {
            $('#testItemTypeNOT').click(function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing ItemType NOT...</div>');
                
                try {
                    // Wait for ItemManager to load
                    setTimeout(() => {
                        if (window.itemManager) {
                            // Test calculateAdvancedOptionsCode with itemType NOT
                            const testData = {
                                itemName: '',
                                itemType: 'NOT',
                                slot1: 'NOT',
                                slot2: 'NOT',
                                slot3: 'NOT',
                                craftOption: 'NOT',
                                craftHeight: 0
                            };
                            
                            console.log('🧪 Testing ItemType NOT with data:', testData);
                            const result = window.itemManager.calculateAdvancedOptionsCode(testData);
                            console.log('🧪 Test result:', result);
                            
                            resultsDiv.html(`
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check me-2"></i>Test Successful!</h6>
                                    <p><strong>ItemType:</strong> ${result.itemType}</p>
                                    <p><strong>Options Code:</strong> ${result.optionsCode}</p>
                                    <p><strong>Slot Details:</strong></p>
                                    <ul class="mb-0">
                                        <li>Slot1: ${result.slotDetails.slot1.name} (hex: ${result.slotDetails.slot1.hex})</li>
                                        <li>Slot2: ${result.slotDetails.slot2.name} (hex: ${result.slotDetails.slot2.hex})</li>
                                        <li>Slot3: ${result.slotDetails.slot3.name} (hex: ${result.slotDetails.slot3.hex})</li>
                                    </ul>
                                </div>
                            `);
                        } else {
                            resultsDiv.html(`
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>ItemManager Not Loaded</h6>
                                    <p>ItemManager is not available. Please check the console for errors.</p>
                                </div>
                            `);
                        }
                    }, 1000);
                    
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>Test Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                }
            });
            
            $('#testAdvancedEditor').click(function() {
                window.open('advanced-editor.php', '_blank');
            });
            
            // Auto-run test after page load
            setTimeout(() => {
                $('#testItemTypeNOT').click();
            }, 1500);
        });
    </script>
</body>
</html>
