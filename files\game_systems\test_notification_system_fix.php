<?php
// Test Notification System Fix
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification System Fix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-bell-slash me-2"></i>Test Notification System Fix</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Fatal Error Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>🚨 Error Details:</h6>
                        <div class="bg-light p-3 rounded">
                            <pre class="small text-danger mb-0">Fatal error: Uncaught Error: 
Call to undefined function getDbConnection() 
in notification_system.php:197

Stack trace:
#0 {main}
  thrown in notification_system.php on line 197</pre>
                        </div>
                        
                        <h6 class="mt-3">✅ Fix Applied:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added <code>getDbConnection()</code> function
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-info me-2"></i>
                                Enhanced error handling
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-database text-primary me-2"></i>
                                Proper database connection management
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Notification System</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า notification system ทำงานได้หลังแก้ไข</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testNotificationSystem" class="btn btn-primary">
                                <i class="fas fa-bell me-2"></i>Test Notification System
                            </button>
                            
                            <button id="testDbConnection" class="btn btn-success">
                                <i class="fas fa-database me-2"></i>Test DB Connection
                            </button>
                            
                            <a href="advanced-editor.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Test Advanced Editor
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-code me-2"></i>Fix Implementation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔧 Added getDbConnection() Function:</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">/**
 * Get database connection
 */
function getDbConnection() {
    try {
        if (function_exists('db_connect')) {
            $conn = db_connect();
            if (!$conn) {
                throw new Exception('Database connection failed');
            }
            return $conn;
        } else {
            throw new Exception('db_connect function not available');
        }
    } catch (Exception $e) {
        error_log("notification_system.php database connection error: " . $e->getMessage());
        throw $e;
    }
}</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>⚡ Fixed Function Call:</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Handle different request methods
try {
    $conn = getDbConnection();  // ← Fixed: Added this line

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create notification
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        // ... rest of the code
    }
} catch (Exception $e) {
    // Error handling
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-flow-chart me-2"></i>Notification System Flow</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📋 Before Fix (Broken):</h6>
                                <div class="bg-light p-3 rounded">
                                    <ol class="small mb-0">
                                        <li>Include dbinfo.inc.php ✅</li>
                                        <li>Set headers ✅</li>
                                        <li>Handle requests ✅</li>
                                        <li><span class="text-danger">Call getDbConnection() ❌</span></li>
                                        <li><span class="text-danger">Fatal Error! 💥</span></li>
                                    </ol>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📋 After Fix (Working):</h6>
                                <div class="bg-light p-3 rounded">
                                    <ol class="small mb-0">
                                        <li>Include dbinfo.inc.php ✅</li>
                                        <li>Set headers ✅</li>
                                        <li><span class="text-success">Define getDbConnection() ✅</span></li>
                                        <li>Handle requests ✅</li>
                                        <li><span class="text-success">Call getDbConnection() ✅</span></li>
                                        <li><span class="text-success">Process notifications ✅</span></li>
                                        <li>Close connection ✅</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Expected Results After Fix:</h6>
                            <ul class="mb-0">
                                <li>✅ No more fatal errors</li>
                                <li>✅ Notification system responds properly</li>
                                <li>✅ Database connections work</li>
                                <li>✅ Advanced Editor notifications function</li>
                                <li>✅ JSON responses returned correctly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>How to Verify Fix</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Verification Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Test Notification System:</strong> Click "Test Notification System" above</li>
                                <li><strong>Test DB Connection:</strong> Click "Test DB Connection" to verify database access</li>
                                <li><strong>Open Advanced Editor:</strong> Should load without fatal errors</li>
                                <li><strong>Send Test Item:</strong> Try sending an item to test notifications</li>
                                <li><strong>Check Browser Console:</strong> Should see no fatal error messages</li>
                                <li><strong>Check Server Logs:</strong> No PHP fatal errors should appear</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Expected Behavior:</h6>
                            <ul class="mb-0">
                                <li>🔄 <strong>notification_system.php:</strong> Returns JSON responses</li>
                                <li>📱 <strong>Advanced Editor:</strong> Loads completely without errors</li>
                                <li>🔔 <strong>Notifications:</strong> Work when sending items</li>
                                <li>💾 <strong>Database:</strong> Connections established successfully</li>
                                <li>📊 <strong>Error Logs:</strong> Clean, no fatal errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testNotificationSystem').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const response = await fetch('notification_system.php', {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('Notification System Test Success', `
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Response Type:</strong> ${response.headers.get('content-type')}</p>
                        <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
                        <p class="mb-0"><strong>Result:</strong> Notification system is working properly!</p>
                    `, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('Notification System Test Failed', `
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>The notification system may still have issues.</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        $('#testDbConnection').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                // Test with a simple POST request to trigger getDbConnection()
                const response = await fetch('notification_system.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'test',
                        test: true
                    })
                });
                
                const text = await response.text();
                
                if (text.includes('Fatal error') || text.includes('getDbConnection')) {
                    showResult('DB Connection Test Failed', `
                        <p><strong>Error:</strong> Still getting fatal errors</p>
                        <p><strong>Response:</strong></p>
                        <pre class="bg-light p-2 rounded small">${text.substring(0, 300)}...</pre>
                    `, 'error');
                } else {
                    try {
                        const data = JSON.parse(text);
                        showResult('DB Connection Test Success', `
                            <p><strong>Status:</strong> No fatal errors detected</p>
                            <p><strong>Response:</strong> Valid JSON received</p>
                            <p class="mb-0"><strong>Result:</strong> getDbConnection() function is working!</p>
                        `, 'success');
                    } catch (e) {
                        showResult('DB Connection Test Partial Success', `
                            <p><strong>Status:</strong> No fatal errors, but response format may need adjustment</p>
                            <p><strong>Response:</strong> ${text.substring(0, 100)}...</p>
                        `, 'warning');
                    }
                }
            } catch (error) {
                showResult('DB Connection Test Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        // Auto-run notification system test
        setTimeout(() => {
            $('#testNotificationSystem').click();
        }, 1000);
    </script>
</body>
</html>
