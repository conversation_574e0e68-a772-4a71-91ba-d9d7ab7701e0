<?php
// Simple Notifications API
// Avoid session conflicts by checking first
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set headers immediately
header('Content-Type: application/json; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Try to find database config
$dbinfo_found = false;
$possible_paths = [
    __DIR__ . '/../../_app/dbinfo.inc.php',
    __DIR__ . '/../../../_app/dbinfo.inc.php',
    dirname(__DIR__, 2) . '/_app/dbinfo.inc.php'
];

foreach ($possible_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $dbinfo_found = true;
        break;
    }
}

// Simple response function
function sendResponse($success, $data = null, $error = null) {
    $response = ['success' => $success];
    if ($data !== null) $response['data'] = $data;
    if ($error !== null) $response['error'] = $error;
    echo json_encode($response);
    exit;
}

// Check if we can proceed
if (!$dbinfo_found) {
    sendResponse(false, null, 'Database configuration not found');
}

if (!function_exists('db_connect')) {
    sendResponse(false, null, 'Database connection function not available');
}

// Get database connection
try {
    $conn = db_connect();
    if (!$conn) {
        sendResponse(false, null, 'Failed to connect to database');
    }
} catch (Exception $e) {
    sendResponse(false, null, 'Database connection error: ' . $e->getMessage());
}

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Get notifications
            $user = $_GET['user'] ?? 'all';
            $limit = (int)($_GET['limit'] ?? 10);
            
            // Simple query for testing
            $sql = "SELECT TOP ? 'Test Notification' as title, 'This is a test message' as message, 
                    'info' as type, GETDATE() as created_at";
            $stmt = sqlsrv_prepare($conn, $sql, [$limit]);
            
            if ($stmt && sqlsrv_execute($stmt)) {
                $notifications = [];
                while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
                    $notifications[] = [
                        'title' => $row['title'],
                        'message' => $row['message'],
                        'type' => $row['type'],
                        'created_at' => $row['created_at']->format('Y-m-d H:i:s')
                    ];
                }
                sendResponse(true, [
                    'notifications' => $notifications,
                    'count' => count($notifications),
                    'user' => $user
                ]);
            } else {
                sendResponse(false, null, 'Failed to fetch notifications');
            }
            break;
            
        case 'POST':
            // Create notification
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            if (!$data) {
                sendResponse(false, null, 'Invalid JSON data');
            }
            
            // For testing, just return success
            sendResponse(true, [
                'message' => 'Notification created successfully',
                'notification_id' => rand(1000, 9999),
                'data' => $data
            ]);
            break;
            
        case 'PUT':
            // Update notification
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            sendResponse(true, [
                'message' => 'Notification updated successfully',
                'data' => $data
            ]);
            break;
            
        case 'DELETE':
            // Delete notification
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            
            sendResponse(true, [
                'message' => 'Notification deleted successfully',
                'data' => $data
            ]);
            break;
            
        default:
            sendResponse(false, null, 'Method not allowed');
    }
    
} catch (Exception $e) {
    sendResponse(false, null, 'Server error: ' . $e->getMessage());
} finally {
    if (isset($conn) && $conn) {
        sqlsrv_close($conn);
    }
}
?>
