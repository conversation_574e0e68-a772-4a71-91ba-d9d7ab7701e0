/* Notification Styles for Advanced Editor */

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 16px;
    border-left: 4px solid #007bff;
    animation: slideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.info {
    border-left-color: #17a2b8;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin: 0;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #666;
}

.notification-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin: 0;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 123, 255, 0.3);
    animation: progress 5s linear forwards;
}

.notification.success .notification-progress {
    background: rgba(40, 167, 69, 0.3);
}

.notification.warning .notification-progress {
    background: rgba(255, 193, 7, 0.3);
}

.notification.error .notification-progress {
    background: rgba(220, 53, 69, 0.3);
}

.notification.info .notification-progress {
    background: rgba(23, 162, 184, 0.3);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes progress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

.notification.removing {
    animation: slideOut 0.3s ease-in forwards;
}

/* Toast notification styles */
.toast-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 10000;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        padding: 12px;
    }
    
    .notification-title {
        font-size: 13px;
    }
    
    .notification-message {
        font-size: 12px;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .notification {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .notification-title {
        color: #f7fafc;
    }
    
    .notification-message {
        color: #cbd5e0;
    }
    
    .notification-close {
        color: #a0aec0;
    }
    
    .notification-close:hover {
        color: #e2e8f0;
    }
}
