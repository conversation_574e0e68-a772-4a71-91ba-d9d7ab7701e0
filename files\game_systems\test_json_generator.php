<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ JSON Generator</title>
    <link href="../../assets/vendor/bootstrap/css/bootstrap.css" rel="stylesheet">
    <link href="../../assets/vendor/font-awesome/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .json-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .step-card {
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-file-code"></i> ทดสอบ JSON Generator</h2>
        <p class="text-muted">ทดสอบการสร้างไฟล์ JSON โครงสร้าง Option Codes</p>

        <!-- ขั้นตอนการทดสอบ -->
        <div class="row">
            <div class="col-md-6">
                <div class="card step-card">
                    <div class="card-header">
                        <h5><i class="fas fa-play"></i> ขั้นตอนที่ 1: ทดสอบ API</h5>
                    </div>
                    <div class="card-body">
                        <p>ทดสอบการเรียก API สร้างไฟล์ JSON</p>
                        <button class="btn btn-primary" onclick="testJSONAPI()">
                            <i class="fas fa-cogs"></i> ทดสอบ API
                        </button>
                        <div id="apiTestResult" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card step-card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye"></i> ขั้นตอนที่ 2: ตรวจสอบไฟล์</h5>
                    </div>
                    <div class="card-body">
                        <p>ตรวจสอบไฟล์ JSON ที่สร้างขึ้น</p>
                        <button class="btn btn-info" onclick="checkJSONFile()">
                            <i class="fas fa-search"></i> ตรวจสอบไฟล์
                        </button>
                        <div id="fileCheckResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- แสดงตัวอย่าง JSON -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-code"></i> ตัวอย่างโครงสร้าง JSON ที่ต้องการ</h5>
            </div>
            <div class="card-body">
                <div class="json-preview">{
    "Helm": [
        {"1": "MP"},
        {"2": "DEF"},
        {"3": "DEF RATE"},
        {"4": "CRIT DAMAGE"},
        {"5": "CRIT RATE"},
        {"6": "2SLOT DROP"},
        {"7": "SKILL EXP"},
        {"8": "SWORD SKILL AMP"},
        {"9": "MAGIC SKILL AMP"},
        {"A": "ALL ATTACK UP"},
        {"B": "MAX HP STEAL"},
        {"C": "MAX MP STEAL"},
        {"D": "ALZ DROP AMMOUNT"},
        {"E": "1 SLOT ITEM DROP"},
        {"F": "ALL SKILL AMP"}
    ],
    "Suit": [
        {"1": "HP"},
        {"2": "DEF"},
        {"3": "DEF RATE"},
        {"4": "MP"},
        {"5": "HP AUTO HEAL"},
        {"6": "2 SLOTS ITEM DROP"},
        {"7": "SKILL EXP"},
        {"8": "SWORD SKILL AMP"},
        {"9": "MAGIC SKILL AMP"},
        {"A": "ALL ATTACK UP"},
        {"B": "MP AUTO HEAL"},
        {"C": "MAX CRIT RATE"},
        {"D": "ALZ DROP AMOUNT"},
        {"E": "FLEE RATE"},
        {"F": "ALL SKILL AMP"}
    ]
}</div>
            </div>
        </div>

        <!-- ผลลัพธ์ JSON ที่สร้าง -->
        <div class="card mt-3" id="generatedJSONCard" style="display: none;">
            <div class="card-header">
                <h5><i class="fas fa-file-alt"></i> ไฟล์ JSON ที่สร้างขึ้น</h5>
                <div class="float-right">
                    <button class="btn btn-sm btn-success" onclick="downloadJSON()">
                        <i class="fas fa-download"></i> ดาวน์โหลด
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="generatedJSONContent" class="json-preview"></div>
            </div>
        </div>

        <!-- เครื่องมือ -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> เครื่องมือ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="optioncode_manager.php" class="btn btn-primary btn-block">
                            <i class="fas fa-cogs"></i> Option Code Manager
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success btn-block" onclick="runFullTest()">
                            <i class="fas fa-play"></i> ทดสอบทั้งหมด
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning btn-block" onclick="clearResults()">
                            <i class="fas fa-trash"></i> ล้างผลลัพธ์
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="optioncode_tools_index.php" class="btn btn-info btn-block">
                            <i class="fas fa-home"></i> กลับหน้าหลัก
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/vendor/jquery/jquery.js"></script>
    <script src="../../assets/vendor/bootstrap/js/bootstrap.js"></script>
    <script>
        let generatedFileName = '';

        function testJSONAPI() {
            $('#apiTestResult').html('<i class="fas fa-spinner fa-spin"></i> กำลังทดสอบ API...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'generate_json' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        generatedFileName = response.filename;
                        $('#apiTestResult').html(`
                            <div class="test-result success">
                                <i class="fas fa-check"></i> ✅ API ทำงานสำเร็จ<br>
                                📁 ไฟล์: ${response.filename}<br>
                                📏 ขนาด: ${response.size} bytes<br>
                                📊 ประเภทข้อมูล: ${Object.keys(response.preview).length} ประเภท
                            </div>
                        `);
                        
                        // แสดงตัวอย่าง JSON
                        $('#generatedJSONContent').text(JSON.stringify(response.preview, null, 2));
                        $('#generatedJSONCard').show();
                        
                    } else {
                        $('#apiTestResult').html(`
                            <div class="test-result error">
                                <i class="fas fa-times"></i> ❌ API Error: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    $('#apiTestResult').html(`
                        <div class="test-result error">
                            <i class="fas fa-times"></i> ❌ ไม่สามารถเรียก API ได้: ${error}
                        </div>
                    `);
                }
            });
        }

        function checkJSONFile() {
            if (!generatedFileName) {
                $('#fileCheckResult').html('<div class="test-result warning">กรุณาทดสอบ API ก่อน</div>');
                return;
            }
            
            $('#fileCheckResult').html('<i class="fas fa-spinner fa-spin"></i> กำลังตรวจสอบไฟล์...');
            
            $.get(generatedFileName)
                .done(function(data) {
                    try {
                        const jsonData = typeof data === 'string' ? JSON.parse(data) : data;
                        const itemTypes = Object.keys(jsonData);
                        let totalOptions = 0;
                        
                        itemTypes.forEach(type => {
                            totalOptions += jsonData[type].length;
                        });
                        
                        $('#fileCheckResult').html(`
                            <div class="test-result success">
                                <i class="fas fa-check"></i> ✅ ไฟล์ JSON ถูกต้อง<br>
                                📊 ประเภทไอเท็ม: ${itemTypes.length} ประเภท<br>
                                🔢 Options ทั้งหมด: ${totalOptions} รายการ<br>
                                📋 ประเภท: ${itemTypes.join(', ')}
                            </div>
                        `);
                        
                        // แสดงไฟล์เต็ม
                        $('#generatedJSONContent').text(JSON.stringify(jsonData, null, 2));
                        
                    } catch (e) {
                        $('#fileCheckResult').html(`
                            <div class="test-result error">
                                <i class="fas fa-times"></i> ❌ ไฟล์ JSON ไม่ถูกต้อง: ${e.message}
                            </div>
                        `);
                    }
                })
                .fail(function() {
                    $('#fileCheckResult').html(`
                        <div class="test-result error">
                            <i class="fas fa-times"></i> ❌ ไม่พบไฟล์ ${generatedFileName}
                        </div>
                    `);
                });
        }

        function downloadJSON() {
            if (generatedFileName) {
                window.open(generatedFileName, '_blank');
            } else {
                alert('กรุณาสร้างไฟล์ JSON ก่อน');
            }
        }

        function runFullTest() {
            testJSONAPI();
            setTimeout(() => checkJSONFile(), 2000);
        }

        function clearResults() {
            $('#apiTestResult').empty();
            $('#fileCheckResult').empty();
            $('#generatedJSONCard').hide();
            generatedFileName = '';
        }
    </script>
</body>
</html>
