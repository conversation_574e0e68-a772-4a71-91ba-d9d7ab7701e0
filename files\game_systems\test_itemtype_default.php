<?php
// Test itemType default value
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ItemType Default Value</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-cube me-2"></i>Test ItemType Default Value</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle me-2"></i>ItemType Default Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า itemType ใน Advanced Editor มีค่าเริ่มต้นเป็น "NOT" หรือไม่</p>
                        </div>
                        
                        <h6>Test ItemType Select:</h6>
                        <select id="testItemType" class="form-select mb-3">
                            <option value="NOT" selected>NOT</option>
                            <option value="Weapon">Weapon</option>
                            <option value="Helm">Helm</option>
                            <option value="Suit">Suit</option>
                            <option value="Glove">Glove</option>
                            <option value="Boot">Boot</option>
                            <option value="Epaulet">Epaulet</option>
                            <option value="Amulet">Amulet</option>
                            <option value="Ring">Ring</option>
                            <option value="Bracelet">Bracelet</option>
                            <option value="Charm">Charm</option>
                            <option value="Board">Board</option>
                            <option value="Bike">Bike</option>
                            <option value="Pet">Pet</option>
                            <option value="Costume">Costume</option>
                            <option value="Effector">Effector</option>
                        </select>
                        
                        <button id="testDefault" class="btn btn-primary me-2">
                            <i class="fas fa-play me-2"></i>Test Default Value
                        </button>
                        
                        <button id="testReset" class="btn btn-secondary">
                            <i class="fas fa-undo me-2"></i>Reset to Default
                        </button>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Expected Behavior</h5>
                    </div>
                    <div class="card-body">
                        <h6>Advanced Editor ItemType Default:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                HTML select has <code>selected</code> on "NOT" option
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                DOMContentLoaded sets value to "NOT"
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                loadSampleData() sets itemType to "NOT"
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                resetForm sets itemType to "NOT"
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                clearSavedData sets itemType to "NOT"
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                loadFormData fallback uses "NOT"
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes Made</h5>
                    </div>
                    <div class="card-body">
                        <h6>Files Modified:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">HTML Select Element:</h6>
                                <pre class="bg-light p-2 rounded small">&lt;option value="NOT" selected&gt;NOT&lt;/option&gt;</pre>
                                
                                <h6 class="text-primary mt-3">DOMContentLoaded:</h6>
                                <pre class="bg-light p-2 rounded small">// Set default itemType to NOT
const itemTypeSelect = document.getElementById('itemType');
if (itemTypeSelect) {
    itemTypeSelect.value = 'NOT';
}</pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">loadSampleData():</h6>
                                <pre class="bg-light p-2 rounded small">document.getElementById('itemType').value = 'NOT';</pre>
                                
                                <h6 class="text-primary mt-3">resetForm log:</h6>
                                <pre class="bg-light p-2 rounded small">resetValues: {
    itemType: 'NOT',
    // ...
}</pre>
                                
                                <h6 class="text-primary mt-3">clearSavedData():</h6>
                                <pre class="bg-light p-2 rounded small">document.getElementById('itemType').value = 'NOT';</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-link me-2"></i>Test Advanced Editor</h5>
                    </div>
                    <div class="card-body">
                        <p>คลิกลิงก์ด้านล่างเพื่อทดสอบ Advanced Editor และตรวจสอบว่า ItemType เริ่มต้นเป็น "NOT"</p>
                        
                        <a href="advanced-editor.php" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                        </a>
                        
                        <div class="alert alert-success mt-3">
                            <h6><i class="fas fa-clipboard-check me-2"></i>How to Verify:</h6>
                            <ol class="mb-0">
                                <li>เปิด Advanced Editor</li>
                                <li>ตรวจสอบว่า Item Type dropdown แสดง "NOT"</li>
                                <li>กดปุ่ม "Reset" และตรวจสอบว่ายังคงเป็น "NOT"</li>
                                <li>กดปุ่ม "Clear Saved Data" และตรวจสอบว่ายังคงเป็น "NOT"</li>
                                <li>ดู Console logs ว่ามีข้อความ "Item Type set to default: NOT"</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#testDefault').click(function() {
                const currentValue = $('#testItemType').val();
                const isDefault = currentValue === 'NOT';
                
                let html = `
                    <div class="alert alert-${isDefault ? 'success' : 'warning'}">
                        <h6><i class="fas fa-${isDefault ? 'check' : 'exclamation-triangle'} me-2"></i>Test Result</h6>
                        <p><strong>Current Value:</strong> <code>${currentValue}</code></p>
                        <p><strong>Expected:</strong> <code>NOT</code></p>
                        <p><strong>Status:</strong> ${isDefault ? '✅ PASS' : '⚠️ FAIL'}</p>
                    </div>
                `;
                
                $('#testResults').html(html);
            });
            
            $('#testReset').click(function() {
                $('#testItemType').val('NOT');
                $('#testResults').html(`
                    <div class="alert alert-info">
                        <h6><i class="fas fa-undo me-2"></i>Reset Complete</h6>
                        <p>ItemType has been reset to default value: <code>NOT</code></p>
                    </div>
                `);
            });
            
            // Test on page load
            setTimeout(function() {
                $('#testDefault').click();
            }, 500);
        });
    </script>
</body>
</html>
