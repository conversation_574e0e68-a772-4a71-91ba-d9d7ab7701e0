<?php
// Test that logging is disabled
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logging Disabled</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-ban me-2"></i>Test Logging Disabled</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-info-circle me-2"></i>Logging Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Advanced Editor Logging Disabled</h6>
                            <p>การ log ของ Advanced Editor ถูกปิดแล้ว เพราะระบบส่งไอเทมมีการบันทึกอยู่แล้ว</p>
                        </div>
                        
                        <h6>Changes Made:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                <code>logToDatabase()</code> function disabled
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                POST method returns success without logging
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                AdvancedLogger.log() only logs to console
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                No INSERT INTO admin_logs
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-database me-2"></i>Where Logs Are Stored</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check me-2"></i>Item System Handles Logging</h6>
                            <p>การส่งไอเทมจะถูกบันทึกโดยระบบส่งไอเทมโดยอัตโนมัติ</p>
                        </div>
                        
                        <h6>Logging Tables:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-table text-primary me-2"></i>
                                <strong>item_sends</strong> - บันทึกการส่งไอเทม
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-table text-info me-2"></i>
                                <strong>admin_logs</strong> - บันทึกการกระทำของ admin (อื่นๆ)
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-table text-warning me-2"></i>
                                <strong>notifications</strong> - บันทึกการแจ้งเตือน
                            </li>
                        </ul>
                        
                        <?php
                        try {
                            $conn = db_connect();
                            if ($conn) {
                                // Check recent item_sends
                                $itemQuery = "SELECT TOP 3 id, player_username, item_name, status, created_at FROM item_sends ORDER BY created_at DESC";
                                $itemResult = sqlsrv_query($conn, $itemQuery);
                                
                                if ($itemResult && sqlsrv_has_rows($itemResult)) {
                                    echo '<h6 class="mt-3">Recent Item Sends:</h6>';
                                    echo '<div class="table-responsive">';
                                    echo '<table class="table table-sm">';
                                    echo '<tr><th>ID</th><th>Player</th><th>Item</th><th>Status</th></tr>';
                                    
                                    while ($row = sqlsrv_fetch_array($itemResult, SQLSRV_FETCH_ASSOC)) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['player_username']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['item_name']) . '</td>';
                                        echo '<td><span class="badge bg-info">' . htmlspecialchars($row['status']) . '</span></td>';
                                        echo '</tr>';
                                    }
                                    echo '</table>';
                                    echo '</div>';
                                } else {
                                    echo '<p class="text-muted mt-3">No recent item sends found</p>';
                                }
                                
                                sqlsrv_close($conn);
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger mt-3">Database Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Logging API</h5>
                    </div>
                    <div class="card-body">
                        <p>ทดสอบว่า Logging API ยังทำงานได้แต่ไม่บันทึกลงฐานข้อมูล</p>
                        
                        <button id="testLogging" class="btn btn-primary me-2">
                            <i class="fas fa-play me-2"></i>Test Logging API
                        </button>
                        
                        <button id="clearResults" class="btn btn-secondary">
                            <i class="fas fa-trash me-2"></i>Clear Results
                        </button>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-link me-2"></i>Related Pages</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="advanced-editor.php" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Advanced Editor
                            </a>
                            <a href="item_send_history.php" class="btn btn-outline-success">
                                <i class="fas fa-history me-2"></i>Item Send History
                            </a>
                            <a href="advanced_editor_logs_viewer.php" class="btn btn-outline-info">
                                <i class="fas fa-list me-2"></i>Activity Logs Viewer
                            </a>
                            <a href="log_advanced_editor.php" class="btn btn-outline-warning">
                                <i class="fas fa-api me-2"></i>Logging API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#testLogging').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing logging API...</div>');
                
                try {
                    // Test POST request to logging API
                    const response = await fetch('files/game_systems/log_advanced_editor.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            log_level: 'info',
                            action: 'TEST_LOGGING_DISABLED',
                            message: 'Testing that logging is disabled',
                            target_player: 'TestPlayer',
                            item_id: 1001
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Test Successful!</h6>
                                <p><strong>Message:</strong> ${data.message}</p>
                                <p><strong>Log ID:</strong> ${data.log_id}</p>
                                <p class="mb-0"><small>API responds successfully but no data is inserted into database.</small></p>
                            </div>
                        `);
                    } else {
                        resultsDiv.html(`
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times me-2"></i>Test Failed!</h6>
                                <p>Error: ${data.error || 'Unknown error'}</p>
                            </div>
                        `);
                    }
                    
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Request Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                }
            });
            
            $('#clearResults').click(function() {
                $('#testResults').html('');
            });
        });
    </script>
</body>
</html>
