<?php
// Test Missing Elements in Advanced Editor
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Missing Elements</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-search me-2"></i>Test Missing Elements</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Missing Element Warning</h5>
                    </div>
                    <div class="card-body">
                        <h6>🚨 Console Warning:</h6>
                        <div class="bg-light p-3 rounded">
                            <pre class="small text-warning mb-0">⚠️ Advanced Editor button not found
at home.php?url=game_systems/advanced-editor:1490</pre>
                        </div>
                        
                        <h6 class="mt-3">🔍 Possible Causes:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-search text-danger me-2"></i>
                                JavaScript trying to find non-existent element
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-clock text-warning me-2"></i>
                                Element not loaded yet when script runs
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-code text-info me-2"></i>
                                Typo in element ID or selector
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Element Detection Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ตรวจสอบ elements ที่หายไปใน Advanced Editor</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testAdvancedEditor" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Test Advanced Editor Elements
                            </button>
                            
                            <button id="checkCommonElements" class="btn btn-success">
                                <i class="fas fa-list me-2"></i>Check Common Elements
                            </button>
                            
                            <a href="advanced-editor.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-list me-2"></i>Common Elements to Check</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🎯 Form Elements:</h6>
                                <div class="bg-light p-3 rounded">
                                    <ul class="small mb-0">
                                        <li><code>itemSearch</code> - Item search input</li>
                                        <li><code>itemId</code> - Item ID input</li>
                                        <li><code>itemType</code> - Item type select</li>
                                        <li><code>playerUsername</code> - Player username input</li>
                                        <li><code>sendItemBtn</code> - Send item button</li>
                                        <li><code>calculateFromFormBtn</code> - Calculate button</li>
                                        <li><code>resetFormBtn</code> - Reset form button</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>⚙️ Control Elements:</h6>
                                <div class="bg-light p-3 rounded">
                                    <ul class="small mb-0">
                                        <li><code>slot1</code>, <code>slot2</code>, <code>slot3</code> - Slot selects</li>
                                        <li><code>craftOption</code> - Craft option select</li>
                                        <li><code>craftHeight</code> - Craft height input</li>
                                        <li><code>optionsCode</code> - Options code input</li>
                                        <li><code>debugConsole</code> - Debug console div</li>
                                        <li><code>resultsDisplay</code> - Results display div</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <h6>💡 Detection Strategy:</h6>
                            <p class="mb-0">ตรวจสอบว่า elements เหล่านี้มีอยู่ในหน้า Advanced Editor หรือไม่ และหา elements ที่หายไปที่อาจทำให้เกิด warning</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-tools me-2"></i>Debugging Tools</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>🔧 Manual Debugging Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Open Advanced Editor:</strong> Click "Open Advanced Editor" above</li>
                                <li><strong>Open Browser Console:</strong> Press F12 → Console tab</li>
                                <li><strong>Look for Warnings:</strong> Find the "Advanced Editor button not found" warning</li>
                                <li><strong>Check Element:</strong> Use <code>document.getElementById('elementName')</code> to test</li>
                                <li><strong>Verify HTML:</strong> Check if the element exists in the page source</li>
                                <li><strong>Check Timing:</strong> Ensure script runs after DOM is loaded</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6>🎯 Quick Fix Commands:</h6>
                            <p>Run these in browser console to test elements:</p>
                            <div class="bg-dark text-light p-3 rounded font-monospace small">
                                // Test if element exists<br>
                                console.log('sendItemBtn:', document.getElementById('sendItemBtn'));<br>
                                console.log('calculateFromFormBtn:', document.getElementById('calculateFromFormBtn'));<br>
                                console.log('resetFormBtn:', document.getElementById('resetFormBtn'));<br><br>
                                
                                // List all buttons<br>
                                console.log('All buttons:', document.querySelectorAll('button'));<br><br>
                                
                                // Check for missing IDs<br>
                                const buttons = document.querySelectorAll('button');<br>
                                buttons.forEach(btn => console.log('Button ID:', btn.id || 'NO ID'));
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testAdvancedEditor').click(function() {
            // Test if we can access Advanced Editor elements from this page
            // (This won't work since we're on a different page, but demonstrates the concept)
            
            showResult('Advanced Editor Element Test', `
                <p>⚠️ Cannot test Advanced Editor elements from this page directly.</p>
                <p><strong>To test elements:</strong></p>
                <ol class="small mb-0">
                    <li>Open Advanced Editor in new tab</li>
                    <li>Open browser console (F12)</li>
                    <li>Run element detection commands</li>
                    <li>Look for missing elements that cause warnings</li>
                </ol>
                <div class="mt-2 p-2 bg-warning rounded">
                    <small><strong>Common Issue:</strong> JavaScript trying to access elements before DOM is fully loaded</small>
                </div>
            `, 'warning');
        });

        $('#checkCommonElements').click(function() {
            // List of elements that should exist in Advanced Editor
            const requiredElements = [
                'itemSearch', 'itemId', 'itemType', 'playerUsername',
                'sendItemBtn', 'calculateFromFormBtn', 'resetFormBtn',
                'slot1', 'slot2', 'slot3', 'craftOption', 'craftHeight',
                'optionsCode', 'debugConsole', 'resultsDisplay',
                'copyOptionsCodeBtn', 'applyOptionsCodeBtn', 'clearOptionsCodeBtn',
                'bindingCodeBtn', 'converterBtn', 'databaseLoadBtn'
            ];
            
            let elementList = '<p><strong>Elements that should exist in Advanced Editor:</strong></p><ul class="small">';
            requiredElements.forEach(elementId => {
                elementList += `<li><code>${elementId}</code> - Should be present</li>`;
            });
            elementList += '</ul>';
            
            elementList += `
                <div class="mt-3 p-3 bg-light rounded">
                    <h6 class="small">🔍 Detection Script for Advanced Editor:</h6>
                    <pre class="small mb-0">// Run this in Advanced Editor console
const missing = [];
const elements = ${JSON.stringify(requiredElements)};
elements.forEach(id => {
    if (!document.getElementById(id)) {
        missing.push(id);
    }
});
console.log('Missing elements:', missing);</pre>
                </div>
            `;
            
            showResult('Common Elements Checklist', elementList, 'info');
        });

        // Show initial guidance
        setTimeout(() => {
            showResult('Element Detection Guide', `
                <p>🎯 To find the missing "Advanced Editor button":</p>
                <ol class="small mb-0">
                    <li>Open Advanced Editor in a new tab</li>
                    <li>Check browser console for the exact warning</li>
                    <li>Use element detection scripts to find missing elements</li>
                    <li>Verify that all required elements have proper IDs</li>
                    <li>Ensure scripts run after DOM is loaded</li>
                </ol>
                <div class="mt-2 p-2 bg-info rounded text-white">
                    <small><strong>Tip:</strong> The warning might be from a script trying to access an element that doesn't exist or hasn't loaded yet</small>
                </div>
            `, 'info');
        }, 1000);
    </script>
</body>
</html>
