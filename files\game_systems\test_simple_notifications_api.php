<?php
// Test Simple Notifications API
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Notifications API</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-database me-2"></i>Test Simple Notifications API</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Database Schema Fix</h5>
                    </div>
                    <div class="card-body">
                        <h6>🚨 Original Problems:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                Invalid column 'is_active'
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                Invalid column 'target_user'
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                Invalid column 'target_role'
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                Invalid column 'created_by'
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                Invalid column 'metadata'
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">✅ Solution Applied:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                Use existing <code>admin_logs</code> table
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-info me-2"></i>
                                Map notification data to admin_logs columns
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-primary me-2"></i>
                                Safe DateTime handling
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Simple API</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบ Simple Notifications API ที่ใช้ admin_logs table</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testGetNotifications" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>Test Get Notifications
                            </button>
                            
                            <button id="testCreateNotification" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Test Create Notification
                            </button>
                            
                            <a href="simple_notifications_api.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Test Direct Access
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-table me-2"></i>Database Schema Mapping</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>❌ Original (notifications table - doesn't exist):</h6>
                                <div class="bg-light p-3 rounded">
                                    <table class="table table-sm mb-0">
                                        <thead>
                                            <tr>
                                                <th>Column</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>title</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>message</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>target_user</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>target_role</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>is_active</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>created_by</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                            <tr>
                                                <td>metadata</td>
                                                <td><span class="badge bg-danger">Missing</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>✅ New (admin_logs table - exists):</h6>
                                <div class="bg-light p-3 rounded">
                                    <table class="table table-sm mb-0">
                                        <thead>
                                            <tr>
                                                <th>Column</th>
                                                <th>Mapped From</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>admin_username</td>
                                                <td>created_by</td>
                                            </tr>
                                            <tr>
                                                <td>action</td>
                                                <td>title</td>
                                            </tr>
                                            <tr>
                                                <td>target_player</td>
                                                <td>target_user</td>
                                            </tr>
                                            <tr>
                                                <td>details</td>
                                                <td>message</td>
                                            </tr>
                                            <tr>
                                                <td>ip_address</td>
                                                <td>auto-detected</td>
                                            </tr>
                                            <tr>
                                                <td>created_at</td>
                                                <td>GETDATE()</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Benefits of Using admin_logs Table:</h6>
                            <ul class="mb-0">
                                <li>✅ <strong>Existing Schema:</strong> No need to create new tables</li>
                                <li>✅ <strong>Proven Structure:</strong> Already used throughout the system</li>
                                <li>✅ <strong>Consistent Data:</strong> All admin actions in one place</li>
                                <li>✅ <strong>No Schema Conflicts:</strong> Uses existing columns</li>
                                <li>✅ <strong>Audit Trail:</strong> Maintains proper logging</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>API Testing Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Testing Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Test Get Notifications:</strong> Click "Test Get Notifications" to retrieve recent admin logs</li>
                                <li><strong>Test Create Notification:</strong> Click "Test Create Notification" to add a new log entry</li>
                                <li><strong>Direct Access:</strong> Click "Test Direct Access" to see raw API response</li>
                                <li><strong>Check for Errors:</strong> Should see no SQL column errors</li>
                                <li><strong>Verify Data:</strong> Should return valid JSON with admin log data</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Expected Results:</h6>
                            <ul class="mb-0">
                                <li>🔄 <strong>GET Request:</strong> Returns recent admin logs as notifications</li>
                                <li>📝 <strong>POST Request:</strong> Creates new admin log entry</li>
                                <li>📊 <strong>JSON Response:</strong> Valid format with success/error status</li>
                                <li>🚫 <strong>No SQL Errors:</strong> No "Invalid column name" errors</li>
                                <li>⚡ <strong>Fast Response:</strong> Quick database queries</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testGetNotifications').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const response = await fetch('simple_notifications_api.php?limit=5');
                const text = await response.text();
                
                if (text.includes('Invalid column name')) {
                    showResult('Get Notifications Test Failed', `
                        <p><strong>Error:</strong> Still getting SQL column errors</p>
                        <pre class="bg-light p-2 rounded small">${text.substring(0, 300)}...</pre>
                    `, 'error');
                } else {
                    try {
                        const data = JSON.parse(text);
                        showResult('Get Notifications Test Success', `
                            <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
                            <p><strong>Count:</strong> ${data.count || 0} notifications</p>
                            <p><strong>Response:</strong> Valid JSON received</p>
                            <p class="mb-0"><strong>Result:</strong> API is working with admin_logs table!</p>
                        `, 'success');
                    } catch (e) {
                        showResult('Get Notifications Test Partial', `
                            <p><strong>Status:</strong> No SQL errors, but JSON parsing failed</p>
                            <p><strong>Response:</strong> ${text.substring(0, 200)}...</p>
                        `, 'warning');
                    }
                }
            } catch (error) {
                showResult('Get Notifications Test Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        $('#testCreateNotification').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const testData = {
                    admin_username: 'TestUser',
                    action: 'Test Notification',
                    target_player: 'TestPlayer',
                    message: 'This is a test notification from the API test'
                };
                
                const response = await fetch('simple_notifications_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const text = await response.text();
                
                if (text.includes('Invalid column name')) {
                    showResult('Create Notification Test Failed', `
                        <p><strong>Error:</strong> Still getting SQL column errors</p>
                        <pre class="bg-light p-2 rounded small">${text.substring(0, 300)}...</pre>
                    `, 'error');
                } else {
                    try {
                        const data = JSON.parse(text);
                        showResult('Create Notification Test Success', `
                            <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
                            <p><strong>Message:</strong> ${data.message || 'No message'}</p>
                            <p class="mb-0"><strong>Result:</strong> Notification created in admin_logs table!</p>
                        `, 'success');
                    } catch (e) {
                        showResult('Create Notification Test Partial', `
                            <p><strong>Status:</strong> No SQL errors, but JSON parsing failed</p>
                            <p><strong>Response:</strong> ${text.substring(0, 200)}...</p>
                        `, 'warning');
                    }
                }
            } catch (error) {
                showResult('Create Notification Test Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        // Auto-run get notifications test
        setTimeout(() => {
            $('#testGetNotifications').click();
        }, 1000);
    </script>
</body>
</html>
