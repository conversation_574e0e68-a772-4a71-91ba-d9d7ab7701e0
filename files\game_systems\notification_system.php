<?php
/**
 * Notification System for Item Sending
 * Handles real-time notifications and notification history
 */

// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

/**
 * Create notification for item send
 */
function createNotification($conn, $data) {
    try {
        $notification_id = uniqid('notif_', true);
        $title = "Item Sent Successfully";
        $message = "Item sent to {$data['player_username']} via {$data['send_method']}";
        $type = 'item_send';
        $priority = 'normal';
        $created_at = date('Y-m-d H:i:s');
        $is_read = 0;
        $admin_username = $data['admin_username'] ?? 'System';
        
        $details = json_encode([
            'send_id' => $data['send_id'],
            'player_username' => $data['player_username'],
            'item_id' => $data['item_id'],
            'item_code' => $data['item_code'],
            'options_code' => $data['options_code'],
            'quantity' => $data['quantity'],
            'duration' => $data['duration'],
            'send_method' => $data['send_method'],
            'status' => $data['status']
        ]);
        
        $sql = "INSERT INTO notifications (
            notification_id, title, message, type, priority, details,
            admin_username, created_at, is_read, expires_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, DATEADD(day, 7, GETDATE()))";
        
        $params = array(
            &$notification_id, &$title, &$message, &$type, &$priority, &$details,
            &$admin_username, &$created_at, &$is_read
        );
        
        $stmt = sqlsrv_prepare($conn, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            $errors = sqlsrv_errors();
            $errorMessage = "Failed to create notification: ";
            foreach ($errors as $error) {
                $errorMessage .= $error['message'] . " ";
            }
            throw new Exception($errorMessage);
        }
        
        return $notification_id;
        
    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get notifications for admin
 */
function getNotifications($conn, $admin_username = null, $limit = 50, $unread_only = false) {
    try {
        $sql = "SELECT TOP (?) 
                    notification_id, title, message, type, priority, details,
                    admin_username, created_at, is_read, read_at, expires_at
                FROM notifications 
                WHERE expires_at > GETDATE()";
        
        $params = array(&$limit);
        
        if ($admin_username) {
            $sql .= " AND admin_username = ?";
            $params[] = &$admin_username;
        }
        
        if ($unread_only) {
            $sql .= " AND is_read = 0";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to get notifications");
        }
        
        $notifications = array();
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // Convert datetime objects to strings safely
            if ($row['created_at']) {
                if (is_object($row['created_at']) && method_exists($row['created_at'], 'format')) {
                    $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
                } else {
                    $row['created_at'] = (string)$row['created_at'];
                }
            }
            if ($row['read_at']) {
                if (is_object($row['read_at']) && method_exists($row['read_at'], 'format')) {
                    $row['read_at'] = $row['read_at']->format('Y-m-d H:i:s');
                } else {
                    $row['read_at'] = (string)$row['read_at'];
                }
            }
            if ($row['expires_at']) {
                if (is_object($row['expires_at']) && method_exists($row['expires_at'], 'format')) {
                    $row['expires_at'] = $row['expires_at']->format('Y-m-d H:i:s');
                } else {
                    $row['expires_at'] = (string)$row['expires_at'];
                }
            }
            
            // Parse details JSON
            if ($row['details']) {
                $row['details'] = json_decode($row['details'], true);
            }
            
            $notifications[] = $row;
        }
        
        return $notifications;
        
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Mark notification as read
 */
function markAsRead($conn, $notification_id) {
    try {
        $read_at = date('Y-m-d H:i:s');
        
        $sql = "UPDATE notifications SET is_read = 1, read_at = ? WHERE notification_id = ?";
        $params = array(&$read_at, &$notification_id);
        
        $stmt = sqlsrv_prepare($conn, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to mark notification as read");
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Get notification statistics
 */
function getNotificationStats($conn, $admin_username = null) {
    try {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN type = 'item_send' THEN 1 ELSE 0 END) as item_sends,
                    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority
                FROM notifications 
                WHERE expires_at > GETDATE()";
        
        $params = array();
        
        if ($admin_username) {
            $sql .= " AND admin_username = ?";
            $params[] = &$admin_username;
        }
        
        $stmt = sqlsrv_prepare($conn, $sql, $params);
        
        if ($stmt === false || !sqlsrv_execute($stmt)) {
            throw new Exception("Failed to get notification statistics");
        }
        
        $stats = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting notification statistics: " . $e->getMessage());
        throw $e;
    }
}

// Handle different request methods
try {
    // Get database connection
    $conn = db_connect();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create notification
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $action = $data['action'] ?? '';
        
        switch ($action) {
            case 'create':
                $notification_id = createNotification($conn, $data);
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification created successfully',
                    'notification_id' => $notification_id
                ]);
                break;
                
            case 'mark_read':
                if (!isset($data['notification_id'])) {
                    throw new Exception('Missing notification_id');
                }
                markAsRead($conn, $data['notification_id']);
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification marked as read'
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get notifications
        $admin_username = $_GET['admin'] ?? null;
        $limit = (int)($_GET['limit'] ?? 50);
        $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
        $get_stats = isset($_GET['stats']) && $_GET['stats'] === 'true';
        
        if ($get_stats) {
            $stats = getNotificationStats($conn, $admin_username);
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
        } else {
            // Validate database connection
            if (!$conn) {
                throw new Exception("Database connection not available");
            }

            // Validate limit
            if ($limit < 1 || $limit > 1000) {
                $limit = 50;
            }

            // Check if notifications table exists
            $table_check = sqlsrv_query($conn, "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'notifications'");
            if ($table_check === false) {
                // Table check failed, try to get notifications anyway
                error_log("Warning: Unable to check notifications table existence");
            } else {
                $table_exists = sqlsrv_fetch_array($table_check, SQLSRV_FETCH_ASSOC);
                if ($table_exists['count'] == 0) {
                    // Table doesn't exist, return empty result
                    echo json_encode([
                        'success' => true,
                        'notifications' => [],
                        'count' => 0,
                        'message' => 'Notifications table not found. Please create the notifications table.',
                        'admin' => $admin_username,
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);
                    return;
                }
            }

            $notifications = getNotifications($conn, $admin_username, $limit, $unread_only);
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications),
                'admin' => $admin_username,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    error_log("Notification system error: " . $e->getMessage());

    // Set appropriate HTTP status code
    if (strpos($e->getMessage(), 'not found') !== false) {
        http_response_code(404);
    } elseif (strpos($e->getMessage(), 'connection') !== false) {
        http_response_code(503);
    } else {
        http_response_code(400);
    }

    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug_info' => [
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'admin' => $_GET['admin'] ?? $_POST['admin'] ?? 'not provided',
            'timestamp' => date('Y-m-d H:i:s'),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]
    ]);
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>
