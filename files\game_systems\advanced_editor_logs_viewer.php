<?php 
$zpanel->checkSession(true);
?>

    <style>
        .log-level-info { color: #0dcaf0; }
        .log-level-success { color: #198754; }
        .log-level-warning { color: #ffc107; }
        .log-level-error { color: #dc3545; }
        .log-level-debug { color: #6c757d; }
        
        .log-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        
        .log-card.info { border-left-color: #0dcaf0; }
        .log-card.success { border-left-color: #198754; }
        .log-card.warning { border-left-color: #ffc107; }
        .log-card.error { border-left-color: #dc3545; }
        .log-card.debug { border-left-color: #6c757d; }
        
        .log-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .badge-level {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .btn-refresh {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        
        .btn-refresh:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
    </style>

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-clipboard-list text-primary me-2"></i>
                        Advanced Editor Logs Viewer
                    </h1>
                    <div>
                        <small class="text-muted me-3">
                            <i class="fas fa-clock me-1"></i>
                            Last updated: <span id="lastUpdated">-</span>
                        </small>
                        <button id="refreshLogs" class="btn btn-refresh me-2">
                            <i class="fas fa-sync-alt me-1"></i> รีเฟรช
                        </button>
                        <button id="exportLogs" class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i> ส่งออก
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-list-alt fa-2x mb-2"></i>
                        <h4 id="totalLogs">-</h4>
                        <small>Total Logs</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 id="successLogs" class="text-success">-</h4>
                        <small>Success</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h4 id="warningLogs" class="text-warning">-</h4>
                        <small>Warnings</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h4 id="errorLogs" class="text-danger">-</h4>
                        <small>Errors</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>ตัวกรอง</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Target Player</label>
                    <input type="text" id="filterPlayer" class="form-control" placeholder="ชื่อผู้เล่น">
                </div>
                <div class="col-md-3">
                    <label class="form-label">User ID</label>
                    <input type="text" id="filterAdmin" class="form-control" placeholder="ID ผู้ใช้งาน">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Action</label>
                    <input type="text" id="filterAction" class="form-control" placeholder="การกระทำ">
                </div>
                <div class="col-md-3">
                    <label class="form-label">จำนวนแสดงผล</label>
                    <select id="filterLimit" class="form-select">
                        <option value="100">100</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                        <option value="2000">2000</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <button id="applyFilters" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> ค้นหา
                    </button>
                    <button id="clearFilters" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times me-1"></i> ล้างตัวกรอง
                    </button>
                </div>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>รายการ Logs</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table id="logsTable" class="table table-striped table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>เวลา</th>
                                <th>User ID</th>
                                <th>Action</th>
                                <th>Target Player</th>
                                <th>Details</th>
                                <th>IP Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <!-- Logs will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div id="loadingSpinner" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">กำลังโหลดข้อมูล...</p>
        </div>

    <!-- Log Detail Modal -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียด Log</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="logDetailContent">
                    <!-- Log details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        let logsData = [];
        let dataTable;

        // Initialize page
        $(document).ready(function() {
            initializeDataTable();
            loadLogs();
            setupEventListeners();

            // Auto-refresh every 30 seconds
            setInterval(function() {
                console.log('Auto-refreshing logs...');
                loadLogs();
            }, 30000);
        });

        function initializeDataTable() {
            dataTable = $('#logsTable').DataTable({
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true,
                language: {
                    "decimal": "",
                    "emptyTable": "ไม่มีข้อมูลในตาราง",
                    "info": "แสดง _START_ ถึง _END_ จาก _TOTAL_ รายการ",
                    "infoEmpty": "แสดง 0 ถึง 0 จาก 0 รายการ",
                    "infoFiltered": "(กรองจาก _MAX_ รายการทั้งหมด)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "แสดง _MENU_ รายการ",
                    "loadingRecords": "กำลังโหลด...",
                    "processing": "กำลังประมวลผล...",
                    "search": "ค้นหา:",
                    "zeroRecords": "ไม่พบข้อมูลที่ตรงกัน",
                    "paginate": {
                        "first": "หน้าแรก",
                        "last": "หน้าสุดท้าย",
                        "next": "ถัดไป",
                        "previous": "ก่อนหน้า"
                    },
                    "aria": {
                        "sortAscending": ": เปิดใช้งานการเรียงลำดับจากน้อยไปมาก",
                        "sortDescending": ": เปิดใช้งานการเรียงลำดับจากมากไปน้อย"
                    }
                }
            });
        }

        function setupEventListeners() {
            $('#refreshLogs').click(loadLogs);
            $('#applyFilters').click(loadLogs);
            $('#clearFilters').click(clearFilters);
            $('#exportLogs').click(exportLogs);
        }

        function loadLogs() {
            showLoading(true);
            
            const filters = {
                limit: $('#filterLimit').val() || 100,
                admin_username: $('#filterAdmin').val(),
                action: $('#filterAction').val(),
                target_player: $('#filterPlayer').val()
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key]) delete filters[key];
            });

            const queryString = new URLSearchParams(filters).toString();
            
            fetch(`files/game_systems/log_advanced_editor.php?${queryString}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        logsData = data.logs;
                        displayLogs(data.logs);
                        updateStatistics(data.logs);

                        // Update last updated time
                        const now = new Date().toLocaleString('th-TH');
                        $('#lastUpdated').text(now);

                        console.log(`Loaded ${data.logs.length} logs at ${now}`);
                    } else {
                        console.error('Error loading logs:', data.error);
                        alert('เกิดข้อผิดพลาดในการโหลดข้อมูล: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function displayLogs(logs) {
            dataTable.clear();

            logs.forEach(log => {
                const timeFormatted = new Date(log.created_at).toLocaleString('th-TH');
                const detailsPreview = truncateText(log.details || '-', 50);
                const ipAddress = log.ip_address || '-';

                dataTable.row.add([
                    log.id,
                    timeFormatted,
                    log.admin_username || '-',
                    log.action || '-',
                    log.target_player || '-',
                    detailsPreview,
                    ipAddress,
                    `<button class="btn btn-sm btn-outline-primary" onclick="showLogDetail(${log.id})">
                        <i class="fas fa-eye"></i>
                    </button>`
                ]);
            });

            dataTable.draw();
        }

        // getLevelBadge function removed - not needed for admin_logs structure

        function truncateText(text, maxLength) {
            if (!text) return '-';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function updateStatistics(logs) {
            const stats = {
                total: logs.length,
                success: logs.filter(log => log.log_level === 'success').length,
                warning: logs.filter(log => log.log_level === 'warning').length,
                error: logs.filter(log => log.log_level === 'error').length
            };

            $('#totalLogs').text(stats.total.toLocaleString());
            $('#successLogs').text(stats.success.toLocaleString());
            $('#warningLogs').text(stats.warning.toLocaleString());
            $('#errorLogs').text(stats.error.toLocaleString());
        }

        function showLogDetail(logId) {
            const log = logsData.find(l => l.id == logId);
            if (!log) return;

            // Parse details JSON if available
            let parsedDetails = {};
            try {
                if (log.details) {
                    parsedDetails = JSON.parse(log.details);
                }
            } catch (e) {
                parsedDetails = { raw: log.details };
            }

            const detailHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>ID:</strong> ${log.id}<br>
                        <strong>User ID:</strong> ${log.admin_username || '-'}<br>
                        <strong>Action:</strong> ${log.action || '-'}<br>
                        <strong>Target Player:</strong> ${log.target_player || '-'}<br>
                        <strong>IP Address:</strong> ${log.ip_address || '-'}<br>
                        <strong>Created At:</strong> ${new Date(log.created_at).toLocaleString('th-TH')}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>User Agent:</strong><br>
                        <div class="small text-muted" style="max-height: 60px; overflow-y: auto;">
                            ${log.user_agent || '-'}
                        </div>
                    </div>
                </div>
                <hr>
                <div>
                    <strong>Details:</strong><br>
                    <div class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                        <pre class="mb-0">${JSON.stringify(parsedDetails, null, 2)}</pre>
                    </div>
                </div>
            `;

            Swal.fire({
                title: 'Log Detail',
                html: detailHtml,
                width: '800px',
                showCloseButton: true,
                showConfirmButton: false
            });
        }

        function clearFilters() {
            $('#filterAdmin').val('');
            $('#filterAction').val('');
            $('#filterPlayer').val('');
            $('#filterLimit').val('100');
            loadLogs();
        }

        function exportLogs() {
            if (logsData.length === 0) {
                alert('ไม่มีข้อมูลสำหรับส่งออก');
                return;
            }

            const csvContent = convertToCSV(logsData);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `advanced_editor_logs_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function convertToCSV(data) {
            const headers = ['ID', 'User ID', 'Action', 'Target Player', 'Details', 'IP Address', 'User Agent', 'Created At'];
            const csvRows = [headers.join(',')];

            data.forEach(log => {
                const row = [
                    log.id,
                    log.admin_username || '',
                    log.action || '',
                    log.target_player || '',
                    `"${(log.details || '').replace(/"/g, '""')}"`,
                    log.ip_address || '',
                    `"${(log.user_agent || '').replace(/"/g, '""')}"`,
                    log.created_at || ''
                ];
                csvRows.push(row.join(','));
            });

            return csvRows.join('\n');
        }

        function showLoading(show) {
            if (show) {
                $('#loadingSpinner').show();
                $('#logsTable').hide();
            } else {
                $('#loadingSpinner').hide();
                $('#logsTable').show();
            }
        }
    </script>
