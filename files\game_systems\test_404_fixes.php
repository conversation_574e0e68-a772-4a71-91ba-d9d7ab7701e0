<?php
// Test 404 fixes
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 404 Fixes</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Test notification styles -->
    <link href="../notification_styles.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-tools me-2"></i>Test 404 Fixes</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Issues Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Fixed 404 Errors:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-file text-success me-2"></i>
                                Created <code>notification_styles.css</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-file text-success me-2"></i>
                                Created <code>notification_manager.js</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-edit text-info me-2"></i>
                                Fixed send_item.php path in Advanced Editor
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-edit text-info me-2"></i>
                                Changed error message to success message
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">🔧 Path Corrections:</h6>
                        <div class="bg-light p-3 rounded">
                            <small>
                                <strong>Before:</strong> <code>send_item.php</code><br>
                                <strong>After:</strong> <code>files/game_systems/send_item.php</code>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Resources</h5>
                    </div>
                    <div class="card-body">
                        <h6>Resource Tests:</h6>
                        
                        <div class="mb-3">
                            <button id="testNotificationCSS" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-palette me-2"></i>Test Notification CSS
                            </button>
                            <span id="cssStatus" class="ms-2"></span>
                        </div>
                        
                        <div class="mb-3">
                            <button id="testNotificationJS" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-code me-2"></i>Test Notification JS
                            </button>
                            <span id="jsStatus" class="ms-2"></span>
                        </div>
                        
                        <div class="mb-3">
                            <button id="testSendItem" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-paper-plane me-2"></i>Test Send Item Path
                            </button>
                            <span id="sendStatus" class="ms-2"></span>
                        </div>
                        
                        <div class="mb-3">
                            <button id="showTestNotification" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-bell me-2"></i>Show Test Notification
                            </button>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-console me-2"></i>Console Messages</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Expected Console Messages (No More Errors):</h6>
                            <div class="bg-dark text-light p-3 rounded font-monospace small">
                                <div class="text-success">✅ Good Messages:</div>
                                ✅ Using default NOT behavior for itemType: NOT<br>
                                📢 Notification Manager initialized<br>
                                🔧 Item Type set to default: NOT<br>
                                ✅ Complete item generation successful<br>
                                <br>
                                <div class="text-danger">❌ Should NOT see these anymore:</div>
                                <s>❌ No itemMapName data found for itemType: NOT</s><br>
                                <s>GET http://localhost/.../notification_styles.css 404 (Not Found)</s><br>
                                <s>GET http://localhost/.../notification_manager.js 404 (Not Found)</s><br>
                                <s>POST http://localhost/.../send_item.php 404 (Not Found)</s>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-link me-2"></i>Test Advanced Editor</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>How to Verify Fixes:</h6>
                            <ol class="mb-0">
                                <li>เปิด Advanced Editor</li>
                                <li>เปิด Browser Developer Tools (F12)</li>
                                <li>ดู Console tab - ไม่ควรมี 404 errors</li>
                                <li>ดู Network tab - ไม่ควรมี failed requests</li>
                                <li>ทดสอบส่งไอเทม - ควรทำงานได้ปกติ</li>
                                <li>ตรวจสอบ admin_logs table</li>
                            </ol>
                        </div>
                        
                        <a href="advanced-editor.php" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                        </a>
                        
                        <a href="test_send_item_debug.php" class="btn btn-success ms-2" target="_blank">
                            <i class="fas fa-bug me-2"></i>Debug Send Item
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Test notification manager -->
    <script src="../notification_manager.js"></script>

    <script>
        $(document).ready(function() {
            // Test CSS loading
            $('#testNotificationCSS').click(function() {
                const cssLoaded = document.querySelector('link[href*="notification_styles.css"]');
                if (cssLoaded) {
                    $('#cssStatus').html('<span class="badge bg-success">✅ Loaded</span>');
                } else {
                    $('#cssStatus').html('<span class="badge bg-danger">❌ Not Found</span>');
                }
            });
            
            // Test JS loading
            $('#testNotificationJS').click(function() {
                if (window.notificationManager) {
                    $('#jsStatus').html('<span class="badge bg-success">✅ Loaded</span>');
                } else {
                    $('#jsStatus').html('<span class="badge bg-danger">❌ Not Found</span>');
                }
            });
            
            // Test send item path
            $('#testSendItem').click(async function() {
                try {
                    const response = await fetch('files/game_systems/send_item.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ test: true })
                    });
                    
                    if (response.status === 404) {
                        $('#sendStatus').html('<span class="badge bg-danger">❌ 404 Error</span>');
                    } else {
                        $('#sendStatus').html('<span class="badge bg-success">✅ Path OK</span>');
                    }
                } catch (error) {
                    $('#sendStatus').html('<span class="badge bg-warning">⚠️ ' + error.message + '</span>');
                }
            });
            
            // Show test notification
            $('#showTestNotification').click(function() {
                if (window.notificationManager) {
                    window.notificationManager.success(
                        'All 404 errors have been fixed!',
                        'Test Successful'
                    );
                } else {
                    alert('Notification Manager not loaded');
                }
            });
            
            // Auto-run tests
            setTimeout(() => {
                $('#testNotificationCSS').click();
                $('#testNotificationJS').click();
                $('#testSendItem').click();
            }, 1000);
        });
    </script>
</body>
</html>
