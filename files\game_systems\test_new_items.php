<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบไอเท็มใหม่</title>
    <link href="../../assets/vendor/bootstrap/css/bootstrap.css" rel="stylesheet">
    <link href="../../assets/vendor/font-awesome/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .item-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .code-badge {
            display: inline-block;
            padding: 2px 6px;
            margin: 2px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        .mapped { background-color: #d4edda; color: #155724; }
        .unmapped { background-color: #fff3cd; color: #856404; }
        .empty { background-color: #f8f9fa; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-plus-circle"></i> ทดสอบไอเท็มใหม่</h2>
        <p class="text-muted">ตรวจสอบข้อมูลไอเท็มที่เพิ่มใหม่: Weapon, BIKE, BOOT, GLOVE, HELM, SUIT</p>

        <!-- สรุปข้อมูลใหม่ -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> สรุปข้อมูลใหม่</h5>
            </div>
            <div class="card-body">
                <div id="summaryData">
                    <button class="btn btn-primary" onclick="loadSummary()">โหลดสรุปข้อมูล</button>
                </div>
            </div>
        </div>

        <!-- รายละเอียดแต่ละประเภท -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> รายละเอียดแต่ละประเภท</h5>
            </div>
            <div class="card-body">
                <div id="itemDetails">
                    <button class="btn btn-info" onclick="loadItemDetails()">โหลดรายละเอียด</button>
                </div>
            </div>
        </div>

        <!-- Force Codes ที่ขาดหายไป -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle"></i> Force Codes ที่ขาดหายไป</h5>
            </div>
            <div class="card-body">
                <div id="missingCodes">
                    <button class="btn btn-warning" onclick="checkMissingCodes()">ตรวจสอบ Force Codes ที่ขาดหายไป</button>
                </div>
            </div>
        </div>

        <!-- เครื่องมือ -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> เครื่องมือ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="optioncode_manager.php" class="btn btn-success btn-block">
                            <i class="fas fa-cogs"></i> Option Code Manager
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_xml_structure.php" class="btn btn-info btn-block">
                            <i class="fas fa-code"></i> ทดสอบ XML
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary btn-block" onclick="runFullTest()">
                            <i class="fas fa-play"></i> ทดสอบทั้งหมด
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="setup_forcecodes_table.sql" class="btn btn-dark btn-block" download>
                            <i class="fas fa-download"></i> SQL อัพเดท
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/vendor/jquery/jquery.js"></script>
    <script src="../../assets/vendor/bootstrap/js/bootstrap.js"></script>
    <script>
        const newItemTypes = ['Weapon', 'BIKE', 'BOOT', 'GLOVE', 'HELM', 'SUIT'];

        function loadSummary() {
            $('#summaryData').html('<i class="fas fa-spinner fa-spin"></i> กำลังโหลด...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const newItems = response.data.filter(item => newItemTypes.includes(item.type));
                        
                        let totalNewCodes = 0;
                        let mappedNewCodes = 0;
                        
                        newItems.forEach(item => {
                            item.options.forEach(option => {
                                if (option.code !== '') {
                                    totalNewCodes++;
                                    if (option.forceCodeName && option.forceCodeName !== 'ไม่พบข้อมูล') {
                                        mappedNewCodes++;
                                    }
                                }
                            });
                        });
                        
                        const mappingPercentage = totalNewCodes > 0 ? Math.round((mappedNewCodes / totalNewCodes) * 100) : 0;
                        
                        $('#summaryData').html(`
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-primary">${newItems.length}</h4>
                                        <p>ประเภทไอเท็มใหม่</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-info">${totalNewCodes}</h4>
                                        <p>รหัสทั้งหมด</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-success">${mappedNewCodes}</h4>
                                        <p>เชื่อมโยงแล้ว</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4 class="text-warning">${mappingPercentage}%</h4>
                                        <p>เปอร์เซ็นต์การเชื่อมโยง</p>
                                    </div>
                                </div>
                            </div>
                        `);
                    } else {
                        $('#summaryData').html('<div class="test-result error">เกิดข้อผิดพลาด: ' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#summaryData').html('<div class="test-result error">ไม่สามารถโหลดข้อมูลได้</div>');
                }
            });
        }

        function loadItemDetails() {
            $('#itemDetails').html('<i class="fas fa-spinner fa-spin"></i> กำลังโหลด...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const newItems = response.data.filter(item => newItemTypes.includes(item.type));
                        let detailsHtml = '';
                        
                        newItems.forEach(item => {
                            let codesHtml = '';
                            item.options.forEach(option => {
                                if (option.code !== '') {
                                    const badgeClass = option.forceCodeName && option.forceCodeName !== 'ไม่พบข้อมูล' ? 'mapped' : 'unmapped';
                                    const title = option.forceCodeName || 'ไม่พบข้อมูล';
                                    codesHtml += `<span class="code-badge ${badgeClass}" title="${title}">${option.code}</span>`;
                                } else {
                                    codesHtml += `<span class="code-badge empty">-</span>`;
                                }
                            });
                            
                            const mappedCount = item.options.filter(opt => 
                                opt.code !== '' && opt.forceCodeName && opt.forceCodeName !== 'ไม่พบข้อมูล'
                            ).length;
                            const totalWithCode = item.options.filter(opt => opt.code !== '').length;
                            const percentage = totalWithCode > 0 ? Math.round((mappedCount / totalWithCode) * 100) : 0;
                            
                            detailsHtml += `
                                <div class="item-card">
                                    <h6>${item.type} <span class="badge badge-info">${mappedCount}/${totalWithCode} (${percentage}%)</span></h6>
                                    <div>${codesHtml}</div>
                                </div>
                            `;
                        });
                        
                        $('#itemDetails').html(detailsHtml);
                    } else {
                        $('#itemDetails').html('<div class="test-result error">เกิดข้อผิดพลาด: ' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#itemDetails').html('<div class="test-result error">ไม่สามารถโหลดข้อมูลได้</div>');
                }
            });
        }

        function checkMissingCodes() {
            $('#missingCodes').html('<i class="fas fa-spinner fa-spin"></i> กำลังตรวจสอบ...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const newItems = response.data.filter(item => newItemTypes.includes(item.type));
                        const missingCodes = new Set();
                        
                        newItems.forEach(item => {
                            item.options.forEach(option => {
                                if (option.code !== '' && (!option.forceCodeName || option.forceCodeName === 'ไม่พบข้อมูล')) {
                                    missingCodes.add(option.code);
                                }
                            });
                        });
                        
                        if (missingCodes.size > 0) {
                            const missingArray = Array.from(missingCodes).sort((a, b) => parseInt(a) - parseInt(b));
                            let missingHtml = `
                                <div class="test-result warning">
                                    <h6>พบ Force Codes ที่ขาดหายไป ${missingCodes.size} รายการ:</h6>
                                    <p>รหัสที่ขาดหายไป: ${missingArray.join(', ')}</p>
                                    <p><strong>แนะนำ:</strong> รันไฟล์ setup_forcecodes_table.sql เพื่ออัพเดทฐานข้อมูล</p>
                                </div>
                            `;
                            $('#missingCodes').html(missingHtml);
                        } else {
                            $('#missingCodes').html('<div class="test-result success">✅ ไม่พบ Force Codes ที่ขาดหายไป ข้อมูลครบถ้วน!</div>');
                        }
                    } else {
                        $('#missingCodes').html('<div class="test-result error">เกิดข้อผิดพลาด: ' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#missingCodes').html('<div class="test-result error">ไม่สามารถตรวจสอบได้</div>');
                }
            });
        }

        function runFullTest() {
            loadSummary();
            setTimeout(() => loadItemDetails(), 500);
            setTimeout(() => checkMissingCodes(), 1000);
        }

        // โหลดข้อมูลเมื่อเปิดหน้า
        $(document).ready(function() {
            loadSummary();
        });
    </script>
</body>
</html>
