-- Database setup for Item Sending System (SQL Server version)
-- Run this SQL to create the necessary tables

-- Table for storing item send requests
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[item_sends]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[item_sends](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [player_id] [int] NOT NULL,
        [player_username] [nvarchar](50) NOT NULL,
        [item_id] [int] NOT NULL,
        [item_name] [nvarchar](255) NULL,
        [quantity] [int] NOT NULL DEFAULT 1,
        [duration] [int] NOT NULL DEFAULT 0,
        [send_method] [nvarchar](20) NOT NULL DEFAULT 'mail',
        [item_code] [nvarchar](50) NOT NULL,
        [options_code] [nvarchar](100) NOT NULL,
        [admin_username] [nvarchar](50) NOT NULL,
        [created_at] [datetime] NOT NULL,
        [processed_at] [datetime] NULL,
        [status] [nvarchar](50) NOT NULL DEFAULT 'pending',
        [error_message] [nvarchar](max) NULL,
        CONSTRAINT [PK_item_sends] PRIMARY KEY CLUSTERED
        (
            [id] ASC
        )
    )
END
GO

-- Create indexes for item_sends table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_item_sends_player_id' AND object_id = OBJECT_ID('item_sends'))
    CREATE INDEX [idx_item_sends_player_id] ON [dbo].[item_sends] ([player_id])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_item_sends_player_username' AND object_id = OBJECT_ID('item_sends'))
    CREATE INDEX [idx_item_sends_player_username] ON [dbo].[item_sends] ([player_username])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_item_sends_status' AND object_id = OBJECT_ID('item_sends'))
    CREATE INDEX [idx_item_sends_status] ON [dbo].[item_sends] ([status])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_item_sends_created_at' AND object_id = OBJECT_ID('item_sends'))
    CREATE INDEX [idx_item_sends_created_at] ON [dbo].[item_sends] ([created_at])
GO

-- Table for admin action logs
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[admin_logs]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[admin_logs](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [admin_username] [nvarchar](50) NOT NULL,
        [action] [nvarchar](100) NOT NULL,
        [target_player] [nvarchar](50) NULL,
        [details] [nvarchar](max) NULL,
        [ip_address] [nvarchar](45) NULL,
        [user_agent] [nvarchar](max) NULL,
        [created_at] [datetime] NOT NULL,
        CONSTRAINT [PK_admin_logs] PRIMARY KEY CLUSTERED 
        (
            [id] ASC
        )
    )
END
GO

-- Create indexes for admin_logs table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_admin_logs_admin_username' AND object_id = OBJECT_ID('admin_logs'))
    CREATE INDEX [idx_admin_logs_admin_username] ON [dbo].[admin_logs] ([admin_username])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_admin_logs_action' AND object_id = OBJECT_ID('admin_logs'))
    CREATE INDEX [idx_admin_logs_action] ON [dbo].[admin_logs] ([action])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_admin_logs_target_player' AND object_id = OBJECT_ID('admin_logs'))
    CREATE INDEX [idx_admin_logs_target_player] ON [dbo].[admin_logs] ([target_player])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_admin_logs_created_at' AND object_id = OBJECT_ID('admin_logs'))
    CREATE INDEX [idx_admin_logs_created_at] ON [dbo].[admin_logs] ([created_at])
GO

-- Table for item database (if not exists)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[items]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[items](
        [id] [int] NOT NULL,
        [name] [nvarchar](255) NOT NULL,
        [full_id] [nvarchar](50) NULL,
        [category] [nvarchar](100) NULL,
        [type] [nvarchar](100) NULL,
        [description] [nvarchar](max) NULL,
        [created_at] [datetime] DEFAULT GETDATE(),
        [updated_at] [datetime] DEFAULT GETDATE(),
        CONSTRAINT [PK_items] PRIMARY KEY CLUSTERED 
        (
            [id] ASC
        )
    )
END
GO

-- Create indexes for items table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_items_name' AND object_id = OBJECT_ID('items'))
    CREATE INDEX [idx_items_name] ON [dbo].[items] ([name])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_items_category' AND object_id = OBJECT_ID('items'))
    CREATE INDEX [idx_items_category] ON [dbo].[items] ([category])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_items_type' AND object_id = OBJECT_ID('items'))
    CREATE INDEX [idx_items_type] ON [dbo].[items] ([type])
GO

-- Table for notifications
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[notifications]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[notifications](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [notification_id] [nvarchar](50) NOT NULL,
        [title] [nvarchar](255) NOT NULL,
        [message] [nvarchar](max) NOT NULL,
        [type] [nvarchar](50) NOT NULL DEFAULT 'info',
        [priority] [nvarchar](20) NOT NULL DEFAULT 'normal',
        [details] [nvarchar](max) NULL,
        [admin_username] [nvarchar](50) NOT NULL,
        [created_at] [datetime] NOT NULL,
        [is_read] [bit] NOT NULL DEFAULT 0,
        [read_at] [datetime] NULL,
        [expires_at] [datetime] NULL,
        CONSTRAINT [PK_notifications] PRIMARY KEY CLUSTERED
        (
            [id] ASC
        ),
        CONSTRAINT [UQ_notifications_notification_id] UNIQUE NONCLUSTERED
        (
            [notification_id] ASC
        )
    )
END
GO

-- Create indexes for notifications table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_admin_username' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_admin_username] ON [dbo].[notifications] ([admin_username])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_type' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_type] ON [dbo].[notifications] ([type])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_is_read' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_is_read] ON [dbo].[notifications] ([is_read])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_created_at' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_created_at] ON [dbo].[notifications] ([created_at])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_expires_at' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_expires_at] ON [dbo].[notifications] ([expires_at])
GO

-- Insert some sample items (optional)
IF NOT EXISTS (SELECT * FROM [dbo].[items] WHERE [id] = 1)
BEGIN
    INSERT INTO [dbo].[items] ([id], [name], [full_id], [category], [type]) VALUES
    (1, 'Sword of Beginner', '00000001', 'Weapon', 'Sword'),
    (2, 'Magic Sword', '00000002', 'Weapon', 'Sword'),
    (3, 'Health Potion', '00000003', 'Consumable', 'Potion'),
    (4, 'Mana Potion', '00000004', 'Consumable', 'Potion'),
    (5, 'Beginner Armor', '00000005', 'Armor', 'Body'),
    (100, 'Test Item', '00000100', 'Test', 'Test')
END
GO

-- Create view for recent item sends
IF EXISTS (SELECT * FROM sys.views WHERE name = 'recent_item_sends')
    DROP VIEW [dbo].[recent_item_sends]
GO

CREATE VIEW [dbo].[recent_item_sends] AS
SELECT TOP 100
    s.id,
    s.player_username,
    s.item_id,
    s.item_code,
    s.options_code,
    s.quantity,
    s.duration,
    s.send_method,
    s.admin_username,
    s.created_at,
    s.status
FROM item_sends s
ORDER BY s.created_at DESC
GO

-- Create view for admin activity summary
IF EXISTS (SELECT * FROM sys.views WHERE name = 'admin_activity_summary')
    DROP VIEW [dbo].[admin_activity_summary]
GO

CREATE VIEW [dbo].[admin_activity_summary] AS
SELECT 
    admin_username,
    COUNT(*) as total_actions,
    SUM(CASE WHEN action = 'Send Item' THEN 1 ELSE 0 END) as items_sent,
    MAX(created_at) as last_activity,
    CONVERT(date, created_at) as activity_date
FROM admin_logs 
WHERE created_at >= DATEADD(day, -30, GETDATE())
GROUP BY admin_username, CONVERT(date, created_at)
GO

-- Table for notifications
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[notifications]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[notifications](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [title] [nvarchar](255) NOT NULL,
        [message] [nvarchar](max) NOT NULL,
        [type] [nvarchar](50) NOT NULL DEFAULT 'info',
        [priority] [nvarchar](20) NOT NULL DEFAULT 'normal',
        [target_user] [nvarchar](50) NULL,
        [target_role] [nvarchar](50) NULL,
        [is_read] [bit] NOT NULL DEFAULT 0,
        [is_active] [bit] NOT NULL DEFAULT 1,
        [expires_at] [datetime] NULL,
        [created_by] [nvarchar](50) NOT NULL,
        [created_at] [datetime] NOT NULL DEFAULT GETDATE(),
        [read_at] [datetime] NULL,
        [metadata] [nvarchar](max) NULL,
        CONSTRAINT [PK_notifications] PRIMARY KEY CLUSTERED
        (
            [id] ASC
        )
    )
END
GO

-- Create indexes for notifications table
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_target_user' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_target_user] ON [dbo].[notifications] ([target_user])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_target_role' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_target_role] ON [dbo].[notifications] ([target_role])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_type' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_type] ON [dbo].[notifications] ([type])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_is_read' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_is_read] ON [dbo].[notifications] ([is_read])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_is_active' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_is_active] ON [dbo].[notifications] ([is_active])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_notifications_created_at' AND object_id = OBJECT_ID('notifications'))
    CREATE INDEX [idx_notifications_created_at] ON [dbo].[notifications] ([created_at])
GO

-- Create view for active notifications
IF EXISTS (SELECT * FROM sys.views WHERE name = 'active_notifications')
    DROP VIEW [dbo].[active_notifications]
GO

CREATE VIEW [dbo].[active_notifications] AS
SELECT
    id, title, message, type, priority, target_user, target_role,
    is_read, created_by, created_at, expires_at, metadata
FROM notifications
WHERE is_active = 1
  AND (expires_at IS NULL OR expires_at > GETDATE())
GO

-- Create view for unread notifications
IF EXISTS (SELECT * FROM sys.views WHERE name = 'unread_notifications')
    DROP VIEW [dbo].[unread_notifications]
GO

CREATE VIEW [dbo].[unread_notifications] AS
SELECT
    id, title, message, type, priority, target_user, target_role,
    created_by, created_at, expires_at, metadata
FROM notifications
WHERE is_active = 1
  AND is_read = 0
  AND (expires_at IS NULL OR expires_at > GETDATE())
GO

-- Note: Advanced Editor logs will be stored in the existing admin_logs table
-- with JSON details containing log_level, message, and other metadata

-- Create stored procedure for sending items
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_send_item')
    DROP PROCEDURE [dbo].[sp_send_item]
GO

CREATE PROCEDURE [dbo].[sp_send_item]
    @player_username NVARCHAR(50),
    @item_code NVARCHAR(50),
    @options_code NVARCHAR(100),
    @quantity INT,
    @duration INT = 0,
    @send_method NVARCHAR(20),
    @admin_username NVARCHAR(50) = 'System'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @player_id INT;
    DECLARE @item_id INT;
    DECLARE @item_name NVARCHAR(255);
    DECLARE @status NVARCHAR(50) = 'pending';
    DECLARE @send_id INT;
    DECLARE @error_message NVARCHAR(MAX) = NULL;

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Get player ID
        SELECT @player_id = UserNum FROM account WHERE ID = @player_username;

        IF @player_id IS NULL
        BEGIN
            RAISERROR('Player not found', 16, 1);
            RETURN;
        END

        -- Extract item ID from item code (first 8 characters)
        SET @item_id = CONVERT(INT, CONVERT(VARBINARY(4), LEFT(@item_code, 8), 2));
        SET @item_name = 'Item_' + CAST(@item_id AS NVARCHAR(20));

        -- Insert into item_sends
        INSERT INTO item_sends (
            player_id, player_username, item_id, item_name, quantity, duration, send_method,
            item_code, options_code, admin_username, created_at, status
        ) VALUES (
            @player_id, @player_username, @item_id, @item_name, @quantity, @duration, @send_method,
            @item_code, @options_code, @admin_username, GETDATE(), @status
        );
        
        SET @send_id = SCOPE_IDENTITY();
        
        -- Update status based on send method
        IF @send_method = 'inventory'
            SET @status = 'sent_to_inventory';
        ELSE IF @send_method = 'mail'
            SET @status = 'sent_to_mail';
        ELSE IF @send_method = 'warehouse'
            SET @status = 'sent_to_warehouse';
            
        UPDATE item_sends SET status = @status WHERE id = @send_id;
        
        -- Log transaction
        INSERT INTO admin_logs (
            admin_username, action, target_player, details, created_at
        ) VALUES (
            @admin_username, 'Send Item', @player_username,
            '{"action":"send_item","item_id":' + CAST(@item_id AS NVARCHAR(20)) +
            ',"item_code":"' + @item_code +
            '","options_code":"' + @options_code +
            '","quantity":' + CAST(@quantity AS NVARCHAR(20)) +
            ',"duration":' + CAST(@duration AS NVARCHAR(20)) +
            ',"method":"' + @send_method +
            '","send_id":' + CAST(@send_id AS NVARCHAR(20)) + '}',
            GETDATE()
        );
        
        COMMIT TRANSACTION;
        
        -- Return success
        SELECT
            @send_id AS send_id,
            @player_username AS player_username,
            @item_id AS item_id,
            @item_code AS item_code,
            @options_code AS options_code,
            @quantity AS quantity,
            @duration AS duration,
            @send_method AS send_method,
            @status AS status,
            GETDATE() AS timestamp;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @error_message = ERROR_MESSAGE();
        
        -- Return error
        SELECT
            0 AS send_id,
            @player_username AS player_username,
            @item_id AS item_id,
            @item_code AS item_code,
            @options_code AS options_code,
            @quantity AS quantity,
            @duration AS duration,
            @send_method AS send_method,
            'failed' AS status,
            @error_message AS error_message;
    END CATCH
END
GO
