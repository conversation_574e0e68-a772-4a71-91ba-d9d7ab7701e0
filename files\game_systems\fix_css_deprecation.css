/* Fix CSS Deprecation Warnings for -ms-high-contrast */
/* Replace deprecated -ms-high-contrast with modern forced-colors media query */

/* Original deprecated CSS patterns that cause warnings:
   @media (-ms-high-contrast: active) { ... }
   @media (-ms-high-contrast: black-on-white) { ... }
   @media (-ms-high-contrast: white-on-black) { ... }
*/

/* Modern replacement using forced-colors media query */
/* Only apply when forced-colors is actually active to avoid conflicts */
@media (forced-colors: active) {
    /* High contrast mode styles - use system colors */

    /* Button styles for high contrast */
    .btn {
        border: 2px solid ButtonText;
        background: ButtonFace;
        color: ButtonText;
    }

    .btn:hover,
    .btn:focus {
        border-color: Highlight;
        background: Highlight;
        color: HighlightText;
    }

    /* Form control styles */
    .form-control {
        border: 2px solid ButtonText;
        background: Field;
        color: FieldText;
    }

    .form-control:focus {
        border-color: Highlight;
        outline: 2px solid Highlight;
    }

    /* Card styles */
    .card {
        border: 2px solid ButtonText;
        background: Canvas;
        color: CanvasText;
    }

    .card-header {
        border-bottom: 2px solid ButtonText;
        background: ButtonFace;
        color: ButtonText;
    }
    
    /* Alert styles */
    .alert {
        border: 2px solid ButtonText;
        background: Canvas;
        color: CanvasText;
    }

    .alert-success,
    .alert-warning,
    .alert-danger,
    .alert-info {
        border-color: GrayText;
    }

    /* Badge styles */
    .badge {
        border: 1px solid ButtonText;
        background: ButtonFace;
        color: ButtonText;
    }

    /* Table styles */
    .table {
        border: 2px solid ButtonText;
        background: Canvas;
        color: CanvasText;
    }

    .table th,
    .table td {
        border: 1px solid ButtonText;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background: ButtonFace;
    }
    
    /* Navigation styles */
    .nav-link {
        color: LinkText !important;
    }
    
    .nav-link:hover,
    .nav-link:focus {
        color: VisitedText !important;
    }
    
    /* Text styles */
    .text-primary,
    .text-secondary,
    .text-success,
    .text-danger,
    .text-warning,
    .text-info,
    .text-light,
    .text-dark {
        color: CanvasText !important;
    }
    
    /* Background styles */
    .bg-primary,
    .bg-secondary,
    .bg-success,
    .bg-danger,
    .bg-warning,
    .bg-info,
    .bg-light,
    .bg-dark {
        background: Canvas !important;
        color: CanvasText !important;
    }
    
    /* Link styles */
    a {
        color: LinkText !important;
    }
    
    a:hover,
    a:focus {
        color: VisitedText !important;
    }
    
    /* Focus indicators */
    *:focus {
        outline: 2px solid Highlight !important;
        outline-offset: 2px !important;
    }
    
    /* Custom console styles for high contrast */
    #debugConsole {
        background: Canvas !important;
        color: CanvasText !important;
        border: 2px solid ButtonText !important;
    }
    
    /* DataTable styles for high contrast */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border: 2px solid ButtonText !important;
        background: ButtonFace !important;
        color: ButtonText !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: Highlight !important;
        color: HighlightText !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: Highlight !important;
        color: HighlightText !important;
        border-color: Highlight !important;
    }
}

/* Fallback for browsers that don't support forced-colors */
@media (prefers-contrast: high) {
    /* High contrast preferences */
    
    .btn {
        border-width: 2px !important;
        font-weight: bold !important;
    }
    
    .form-control {
        border-width: 2px !important;
    }
    
    .card {
        border-width: 2px !important;
    }
    
    .alert {
        border-width: 2px !important;
        font-weight: bold !important;
    }
    
    .badge {
        border: 1px solid currentColor !important;
        font-weight: bold !important;
    }
    
    .table {
        border-width: 2px !important;
    }
    
    .table th,
    .table td {
        border-width: 1px !important;
    }
    
    /* Enhanced focus indicators */
    *:focus {
        outline-width: 3px !important;
        outline-offset: 2px !important;
    }
}

/* Print styles to avoid deprecation warnings */
@media print {
    /* Remove problematic styles for printing */
    .btn,
    .form-control,
    .card,
    .alert,
    .badge {
        border: 1px solid #000 !important;
        background: #fff !important;
        color: #000 !important;
    }
    
    /* Hide interactive elements */
    .btn {
        display: none !important;
    }
    
    /* Ensure text is readable */
    body {
        background: #fff !important;
        color: #000 !important;
    }
}

/* Additional accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    /* Reduce animations for users who prefer less motion */
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support without deprecated properties */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles that don't use deprecated properties */
    
    .card {
        background-color: #2d3748 !important;
        color: #e2e8f0 !important;
        border-color: #4a5568 !important;
    }
    
    .form-control {
        background-color: #2d3748 !important;
        color: #e2e8f0 !important;
        border-color: #4a5568 !important;
    }
    
    .btn {
        background-color: #4a5568 !important;
        color: #e2e8f0 !important;
        border-color: #718096 !important;
    }
    
    .alert {
        background-color: #2d3748 !important;
        color: #e2e8f0 !important;
        border-color: #4a5568 !important;
    }
    
    #debugConsole {
        background-color: #1a202c !important;
        color: #e2e8f0 !important;
        border-color: #4a5568 !important;
    }
}

/* Remove any remaining -ms-high-contrast references */
/* This ensures no deprecated properties remain */
.no-deprecated-ms-high-contrast {
    /* Placeholder class to ensure this file is loaded */
    content: "CSS deprecation warnings fixed";
}
