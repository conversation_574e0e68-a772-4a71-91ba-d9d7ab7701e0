<?php $zpanel->checkSession(true); ?>

<!-- CSS Styles -->
<style>
/* Monitoring System Styles */
.monitoring-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.monitoring-dashboard::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.security-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 10px;
    padding: 15px;
    color: white;
    margin-bottom: 15px;
    animation: pulse-alert 2s infinite;
}

@keyframes pulse-alert {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.audit-log-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.log-entry {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.log-entry:hover {
    background-color: #f8f9fa;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.log-action {
    font-weight: 600;
    margin: 5px 0;
}

.log-details {
    font-size: 13px;
    color: #777;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online { background: #5cb85c; }
.status-offline { background: #d9534f; }
.status-warning { background: #f0ad4e; }
.status-banned { background: #333; }

.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-ban {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-unban {
    background: linear-gradient(135deg, #5cb85c, #449d44);
    color: white;
}

.btn-reset {
    background: linear-gradient(135deg, #f0ad4e, #ec971f);
    color: white;
}

.btn-view {
    background: linear-gradient(135deg, #0088cc, #0066aa);
    color: white;
}

.monitoring-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #667eea;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.real-time-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 8px;
    height: 8px;
    background: #5cb85c;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.filter-controls {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    font-weight: 600;
    min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .monitoring-stats {
        grid-template-columns: 1fr;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .action-buttons {
        justify-content: center;
    }
}
</style>

<header class="page-header">
    <h2>ระบบจัดการและตรวจสอบ (Monitoring & Management) <span class="real-time-indicator" title="อัปเดตแบบ Real-time"></span></h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Monitoring System</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Get filter parameters
$filterType = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$filterStatus = isset($_GET['status']) ? $_GET['status'] : 'all';
$filterTime = isset($_GET['time']) ? $_GET['time'] : '24';

// Security monitoring queries
$selectSecurityAlerts = "SELECT 
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -1, GETDATE()) 
          AND UserNum IN (
              SELECT UserNum FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
              WHERE LoginTime >= DATEADD(hour, -1, GETDATE())
              GROUP BY UserNum 
              HAVING COUNT(*) > 5
          ) THEN 1 END) as suspicious_logins,
    COUNT(CASE WHEN AuthType = 2 THEN 1 END) as banned_accounts,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as online_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(hour, -24, GETDATE()) THEN 1 END) as recent_logins
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";

$securityQuery = sqlsrv_query($conn, $selectSecurityAlerts);
if ($securityQuery === false) {
    $securityStats = array(
        'suspicious_logins' => 0,
        'banned_accounts' => 0,
        'online_accounts' => 0,
        'recent_logins' => 0
    );
} else {
    $securityStats = sqlsrv_fetch_array($securityQuery, SQLSRV_FETCH_ASSOC);
}

// Recent account activities
$selectRecentActivities = "SELECT TOP 20
    ID,
    UserNum,
    LoginTime,
    LogoutTime,
    LastIp,
    Login,
    AuthType,
    PlayTime
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(hour, -$filterTime, GETDATE())
    ORDER BY LoginTime DESC";

$activitiesQuery = sqlsrv_query($conn, $selectRecentActivities);
$recentActivities = array();
if ($activitiesQuery !== false) {
    while ($row = sqlsrv_fetch_array($activitiesQuery, SQLSRV_FETCH_ASSOC)) {
        $recentActivities[] = $row;
    }
}

// Handle admin actions
if (isset($_POST['action']) && isset($_POST['user_id'])) {
    $action = $_POST['action'];
    $userId = $_POST['user_id'];
    $adminId = $_SESSION['user_id'] ?? 'admin';
    
    switch ($action) {
        case 'ban':
            $banQuery = "UPDATE [".DATABASE_ACC."].[dbo].cabal_auth_table SET AuthType = 2 WHERE UserNum = ?";
            $banResult = sqlsrv_query($conn, $banQuery, array($userId));
            if ($banResult) {
                // Log the action
                logAdminAction($conn, $adminId, 'BAN_USER', "Banned user ID: $userId");
                $message = "ผู้เล่นถูกแบนเรียบร้อยแล้ว";
                $messageType = "success";
            }
            break;
            
        case 'unban':
            $unbanQuery = "UPDATE [".DATABASE_ACC."].[dbo].cabal_auth_table SET AuthType = 1 WHERE UserNum = ?";
            $unbanResult = sqlsrv_query($conn, $unbanQuery, array($userId));
            if ($unbanResult) {
                logAdminAction($conn, $adminId, 'UNBAN_USER', "Unbanned user ID: $userId");
                $message = "ผู้เล่นถูกปลดแบนเรียบร้อยแล้ว";
                $messageType = "success";
            }
            break;
            
        case 'reset_password':
            // In a real implementation, you would generate a new password
            logAdminAction($conn, $adminId, 'RESET_PASSWORD', "Reset password for user ID: $userId");
            $message = "รีเซ็ตรหัสผ่านเรียบร้อยแล้ว";
            $messageType = "info";
            break;
    }
}

function logAdminAction($conn, $adminId, $action, $details) {
    // In a real implementation, you would have a dedicated audit log table
    // For now, we'll just simulate logging
    $logQuery = "INSERT INTO WEB_AdminLog (AdminID, Action, Details, Timestamp) VALUES (?, ?, ?, GETDATE())";
    // This would be executed if the table exists
}
?>
