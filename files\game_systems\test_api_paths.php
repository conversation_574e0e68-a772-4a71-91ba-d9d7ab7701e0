<?php
// Test API Paths
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Paths</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-route me-2"></i>Test API Paths</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>404 Errors Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>🚨 Path Issues Found:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                <code>api/get_stats.php</code> → 404 Error
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                <code>log_advanced_editor.php</code> → 404 Error
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">✅ Fixed Paths:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                <code>files/game_systems/api/get_stats.php</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                <code>files/game_systems/log_advanced_editor.php</code>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test API Endpoints</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="testStatsAPI" class="btn btn-primary">
                                <i class="fas fa-chart-bar me-2"></i>Test Stats API
                            </button>
                            
                            <button id="testLogsAPI" class="btn btn-success">
                                <i class="fas fa-list me-2"></i>Test Logs API
                            </button>
                            
                            <button id="testAllPaths" class="btn btn-info">
                                <i class="fas fa-globe me-2"></i>Test All Paths
                            </button>
                            
                            <a href="?url=game_systems/index" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Test Dashboard
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-comparison me-2"></i>Path Comparison</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Wrong Paths (404 Errors):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// These paths don't work from admin panel
fetch('api/get_stats.php')
fetch('log_advanced_editor.php?limit=5')

// Results in:
// GET /adminpanel-cabal35/api/get_stats.php 404
// GET /adminpanel-cabal35/log_advanced_editor.php 404</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ Correct Paths (Working):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// These paths work correctly
fetch('files/game_systems/api/get_stats.php')
fetch('files/game_systems/log_advanced_editor.php?limit=5')

// Results in:
// GET /adminpanel-cabal35/files/game_systems/api/get_stats.php ✅
// GET /adminpanel-cabal35/files/game_systems/log_advanced_editor.php ✅</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-folder-tree me-2"></i>File Structure</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📁 Current File Structure:</h6>
                                <div class="bg-dark text-light p-3 rounded small">
                                    <pre>adminpanel-cabal35/
├── home.php (admin panel router)
├── files/
│   └── game_systems/
│       ├── index.php
│       ├── log_advanced_editor.php
│       ├── api/
│       │   └── get_stats.php
│       └── ...</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🔗 URL Routing:</h6>
                                <div class="bg-dark text-light p-3 rounded small">
                                    <pre>Admin Panel URL:
?url=game_systems/index

Resolves to:
files/game_systems/index.php

API Calls from index.php:
- files/game_systems/api/get_stats.php
- files/game_systems/log_advanced_editor.php</pre>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>💡 Path Resolution Logic:</h6>
                            <p class="mb-0">เมื่อเรียก API จากหน้า <code>?url=game_systems/index</code> ต้องใช้ relative path ที่ถูกต้องตาม file structure ของ admin panel</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Expected Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>After Path Fixes:</h6>
                            <ul class="mb-0">
                                <li>✅ No more 404 errors in console</li>
                                <li>✅ Stats API returns JSON data</li>
                                <li>✅ Logs API returns recent activities</li>
                                <li>✅ Dashboard auto-refresh works</li>
                                <li>✅ Manual refresh buttons work</li>
                                <li>✅ Smart refresh system functions properly</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>How to Verify:</h6>
                            <ol class="mb-0">
                                <li>Open Dashboard and check browser console</li>
                                <li>Should see no 404 errors</li>
                                <li>Stats should update automatically</li>
                                <li>Activity feed should show recent actions</li>
                                <li>Refresh buttons should work smoothly</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testStatsAPI').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const response = await fetch('files/game_systems/api/get_stats.php');
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('Stats API Test Success', `
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Response:</strong> Valid JSON received</p>
                        <pre class="bg-light p-2 rounded small">${JSON.stringify(data, null, 2).substring(0, 200)}...</pre>
                    `, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('Stats API Test Failed', `
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>The API endpoint is not accessible or returning errors.</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        $('#testLogsAPI').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const response = await fetch('files/game_systems/log_advanced_editor.php?limit=5');
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('Logs API Test Success', `
                        <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                        <p><strong>Logs Found:</strong> ${data.logs ? data.logs.length : 0}</p>
                        <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
                    `, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('Logs API Test Failed', `
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>The logs API endpoint is not accessible or returning errors.</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        $('#testAllPaths').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing All...').prop('disabled', true);
            
            const paths = [
                'files/game_systems/api/get_stats.php',
                'files/game_systems/log_advanced_editor.php?limit=5',
                'files/game_systems/index.php',
                'files/game_systems/advanced-editor.php'
            ];
            
            let results = [];
            
            for (const path of paths) {
                try {
                    const response = await fetch(path);
                    results.push({
                        path: path,
                        status: response.status,
                        ok: response.ok
                    });
                } catch (error) {
                    results.push({
                        path: path,
                        status: 'Error',
                        ok: false,
                        error: error.message
                    });
                }
            }
            
            let resultHtml = '<p><strong>Path Test Results:</strong></p><ul class="mb-0">';
            results.forEach(result => {
                const icon = result.ok ? 'check text-success' : 'times text-danger';
                resultHtml += `<li><i class="fas fa-${icon} me-2"></i><code>${result.path}</code> - ${result.status}</li>`;
            });
            resultHtml += '</ul>';
            
            const allOk = results.every(r => r.ok);
            showResult(allOk ? 'All Paths Working' : 'Some Paths Failed', resultHtml, allOk ? 'success' : 'warning');
            
            btn.html(originalText).prop('disabled', false);
        });

        // Auto-run tests
        setTimeout(() => {
            $('#testStatsAPI').click();
        }, 1000);
    </script>
</body>
</html>
