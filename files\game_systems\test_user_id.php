<?php
// Test User ID functionality
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User ID System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-user-check me-2"></i>Test User ID System</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle me-2"></i>Session Information</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <tr>
                                <td><strong>Session ID:</strong></td>
                                <td><code><?php echo session_id(); ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>User Login:</strong></td>
                                <td><code><?php echo $_SESSION['userLogin'] ?? 'Not Set'; ?></code></td>
                            </tr>
                            <tr>
                                <td><strong>Is Developer:</strong></td>
                                <td>
                                    <?php 
                                    $isDeveloper = $_SESSION['IsDeveloper'] ?? false;
                                    if ($isDeveloper) {
                                        echo '<span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>';
                                    } else {
                                        echo '<span class="badge bg-warning"><i class="fas fa-times"></i> No</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>All Session Data:</strong></td>
                                <td>
                                    <details>
                                        <summary>Click to view</summary>
                                        <pre class="mt-2"><?php print_r($_SESSION); ?></pre>
                                    </details>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-database me-2"></i>Database Test</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $conn = db_connect();
                            if ($conn) {
                                echo '<div class="alert alert-success"><i class="fas fa-check"></i> Database connection successful</div>';
                                
                                // Test admin_logs table
                                $testQuery = "SELECT TOP 5 admin_username, action, created_at FROM admin_logs ORDER BY created_at DESC";
                                $result = sqlsrv_query($conn, $testQuery);
                                
                                if ($result) {
                                    echo '<h6>Recent Admin Logs:</h6>';
                                    echo '<table class="table table-sm">';
                                    echo '<tr><th>User ID</th><th>Action</th><th>Date</th></tr>';
                                    
                                    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                        $date = is_object($row['created_at']) ? $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'];
                                        echo '<tr>';
                                        echo '<td><code>' . htmlspecialchars($row['admin_username']) . '</code></td>';
                                        echo '<td>' . htmlspecialchars($row['action']) . '</td>';
                                        echo '<td>' . htmlspecialchars($date) . '</td>';
                                        echo '</tr>';
                                    }
                                    echo '</table>';
                                } else {
                                    echo '<div class="alert alert-warning">No admin_logs data found</div>';
                                }
                                
                                sqlsrv_close($conn);
                            } else {
                                echo '<div class="alert alert-danger"><i class="fas fa-times"></i> Database connection failed</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-cogs me-2"></i>Test User ID in Advanced Editor</h5>
                    </div>
                    <div class="card-body">
                        <p>Current User ID that will be used in Advanced Editor: <strong><code><?php echo $_SESSION['userLogin'] ?? 'Unknown'; ?></code></strong></p>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>How User ID is used:</h6>
                            <ul class="mb-0">
                                <li><strong>Advanced Editor:</strong> Uses <code>$_SESSION['userLogin']</code> for logging actions</li>
                                <li><strong>Item History:</strong> Shows User ID in admin column instead of "Admin"</li>
                                <li><strong>Activity Logs:</strong> Records User ID for all operations</li>
                                <li><strong>Notifications:</strong> Uses User ID for created_by field</li>
                                <li><strong>Developer Check:</strong> Can check <code>$_SESSION['IsDeveloper']</code> for special permissions</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="advanced-editor.php" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>Test Advanced Editor
                            </a>
                            <a href="item_send_history.php" class="btn btn-success">
                                <i class="fas fa-history me-2"></i>Test Item History
                            </a>
                            <a href="advanced_editor_logs_viewer.php" class="btn btn-info">
                                <i class="fas fa-list me-2"></i>Test Activity Logs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>For Developer Access:</h6>
                            <p>Make sure your session has <code>IsDeveloper</code> set to <code>true</code> for full access to all features.</p>
                            
                            <h6>User ID Format:</h6>
                            <p>The system now uses actual User IDs from <code>$_SESSION['userLogin']</code> instead of generic "Admin" labels.</p>
                            
                            <h6>Database Logging:</h6>
                            <p>All actions are logged with the actual User ID for proper audit trails and accountability.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
