/**
 * Notification Manager for Advanced Editor
 * Handles toast notifications and alerts
 */

class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        // Create notification container
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);
        
        console.log('📢 Notification Manager initialized');
    }

    show(message, type = 'info', title = null, duration = null) {
        const notification = this.createNotification(message, type, title, duration);
        this.addNotification(notification);
        return notification;
    }

    success(message, title = 'Success', duration = null) {
        return this.show(message, 'success', title, duration);
    }

    error(message, title = 'Error', duration = null) {
        return this.show(message, 'error', title, duration);
    }

    warning(message, title = 'Warning', duration = null) {
        return this.show(message, 'warning', title, duration);
    }

    info(message, title = 'Info', duration = null) {
        return this.show(message, 'info', title, duration);
    }

    createNotification(message, type, title, duration) {
        const id = 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const notificationDuration = duration || this.defaultDuration;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.id = id;
        
        const header = document.createElement('div');
        header.className = 'notification-header';
        
        if (title) {
            const titleElement = document.createElement('h6');
            titleElement.className = 'notification-title';
            titleElement.textContent = title;
            header.appendChild(titleElement);
        }
        
        const closeButton = document.createElement('button');
        closeButton.className = 'notification-close';
        closeButton.innerHTML = '×';
        closeButton.onclick = () => this.remove(id);
        header.appendChild(closeButton);
        
        const messageElement = document.createElement('p');
        messageElement.className = 'notification-message';
        messageElement.textContent = message;
        
        const progressBar = document.createElement('div');
        progressBar.className = 'notification-progress';
        
        notification.appendChild(header);
        notification.appendChild(messageElement);
        notification.appendChild(progressBar);
        
        // Auto-remove after duration
        if (notificationDuration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, notificationDuration);
        }
        
        return {
            id: id,
            element: notification,
            type: type,
            message: message,
            title: title,
            duration: notificationDuration
        };
    }

    addNotification(notification) {
        // Remove oldest notification if we exceed max
        if (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.remove(oldest.id);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
        
        // Trigger animation
        setTimeout(() => {
            notification.element.style.opacity = '1';
            notification.element.style.transform = 'translateX(0)';
        }, 10);
    }

    remove(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;
        
        // Add removing class for animation
        notification.element.classList.add('removing');
        
        // Remove from DOM after animation
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            // Remove from array
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification.id);
        });
    }

    toast(message, duration = 2000) {
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, duration);
        
        return toast;
    }

    // Utility methods for common notifications
    itemSent(playerName, itemName) {
        return this.success(
            `Item "${itemName}" has been sent to ${playerName}`,
            'Item Sent Successfully'
        );
    }

    itemSendFailed(playerName, error) {
        return this.error(
            `Failed to send item to ${playerName}: ${error}`,
            'Send Failed'
        );
    }

    playerNotFound(playerName) {
        return this.warning(
            `Player "${playerName}" was not found`,
            'Player Not Found'
        );
    }

    invalidData(field) {
        return this.warning(
            `Please check the ${field} field`,
            'Invalid Data'
        );
    }

    systemError(message) {
        return this.error(
            message || 'An unexpected error occurred',
            'System Error'
        );
    }

    connectionError() {
        return this.error(
            'Unable to connect to the server. Please check your connection.',
            'Connection Error'
        );
    }

    // Integration with existing console logging
    logAndNotify(level, message, title = null) {
        // Log to console
        console.log(`[${level.toUpperCase()}] ${title || ''}: ${message}`);
        
        // Show notification
        switch (level.toLowerCase()) {
            case 'success':
                return this.success(message, title);
            case 'error':
                return this.error(message, title);
            case 'warning':
                return this.warning(message, title);
            case 'info':
            default:
                return this.info(message, title);
        }
    }
}

// Create global instance
window.notificationManager = new NotificationManager();

// Backward compatibility aliases
window.showNotification = (message, type, title, duration) => {
    return window.notificationManager.show(message, type, title, duration);
};

window.showToast = (message, duration) => {
    return window.notificationManager.toast(message, duration);
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
