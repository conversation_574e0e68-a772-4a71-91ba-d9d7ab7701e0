<?php
// Test file to check database connection and table existence
 $zpanel->checkSession(true);

header('Content-Type: text/html; charset=UTF-8');

echo "<h2>Advanced Editor Logs - Database Test</h2>";

try {
    // Test database connection
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if admin_logs table exists (we'll use this instead of advanced_editor_logs)
    $checkTable = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'admin_logs'";
    $result = sqlsrv_query($conn, $checkTable);

    if ($result) {
        $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
        if ($row['count'] > 0) {
            echo "<p style='color: green;'>✅ Table 'admin_logs' exists (using this for advanced editor logs)</p>";

            // Check table structure
            $structureQuery = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                              FROM INFORMATION_SCHEMA.COLUMNS
                              WHERE TABLE_NAME = 'admin_logs'
                              ORDER BY ORDINAL_POSITION";
            $structureResult = sqlsrv_query($conn, $structureQuery);

            if ($structureResult) {
                echo "<h3>Table Structure:</h3>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Column Name</th><th>Data Type</th><th>Nullable</th></tr>";

                while ($col = sqlsrv_fetch_array($structureResult, SQLSRV_FETCH_ASSOC)) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($col['COLUMN_NAME']) . "</td>";
                    echo "<td>" . htmlspecialchars($col['DATA_TYPE']) . "</td>";
                    echo "<td>" . htmlspecialchars($col['IS_NULLABLE']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }

            // Count existing records (all admin logs)
            $countQuery = "SELECT COUNT(*) as total FROM admin_logs";
            $countResult = sqlsrv_query($conn, $countQuery);

            if ($countResult) {
                $countRow = sqlsrv_fetch_array($countResult, SQLSRV_FETCH_ASSOC);
                echo "<p><strong>Total admin log records:</strong> " . $countRow['total'] . "</p>";

                // Count advanced editor logs specifically
                $advCountQuery = "SELECT COUNT(*) as total FROM admin_logs WHERE details LIKE '%log_level%'";
                $advCountResult = sqlsrv_query($conn, $advCountQuery);

                if ($advCountResult) {
                    $advCountRow = sqlsrv_fetch_array($advCountResult, SQLSRV_FETCH_ASSOC);
                    echo "<p><strong>Advanced Editor log records:</strong> " . $advCountRow['total'] . "</p>";

                    if ($advCountRow['total'] > 0) {
                        // Show recent advanced editor records
                        $recentQuery = "SELECT TOP 5 id, admin_username, action, target_player, details, created_at
                                       FROM admin_logs
                                       WHERE details LIKE '%log_level%'
                                       ORDER BY created_at DESC";
                        $recentResult = sqlsrv_query($conn, $recentQuery);

                        if ($recentResult) {
                            echo "<h3>Recent Advanced Editor Log Entries:</h3>";
                            echo "<table border='1' style='border-collapse: collapse;'>";
                            echo "<tr><th>ID</th><th>Admin</th><th>Action</th><th>Target Player</th><th>Details</th><th>Created At</th></tr>";

                            while ($log = sqlsrv_fetch_array($recentResult, SQLSRV_FETCH_ASSOC)) {
                                $details = json_decode($log['details'], true);
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($log['id']) . "</td>";
                                echo "<td>" . htmlspecialchars($log['admin_username']) . "</td>";
                                echo "<td>" . htmlspecialchars($log['action']) . "</td>";
                                echo "<td>" . htmlspecialchars($log['target_player'] ?: '-') . "</td>";
                                echo "<td>" . htmlspecialchars(substr($details['message'] ?? '', 0, 50)) . "...</td>";
                                echo "<td>" . htmlspecialchars($log['created_at']->format('Y-m-d H:i:s')) . "</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        }
                    } else {
                        echo "<p style='color: orange;'>⚠️ No advanced editor log records found.</p>";
                    }
                }
            }

        } else {
            echo "<p style='color: red;'>❌ Table 'admin_logs' does not exist</p>";
            echo "<p><strong>You need to run the SQL script to create the table:</strong></p>";
            echo "<pre>files/game_systems/database_setup_sqlserver.sql</pre>";
        }
    }
    
    // Test API endpoint
    echo "<h3>API Test:</h3>";
    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/log_advanced_editor.php';
    echo "<p><strong>API URL:</strong> <a href='$apiUrl' target='_blank'>$apiUrl</a></p>";
    
    // Test GET request
    $testUrl = $apiUrl . '?limit=5';
    echo "<p><strong>Test GET request:</strong> <a href='$testUrl' target='_blank'>$testUrl</a></p>";
    
    // Test POST (insert a test log)
    echo "<h3>Insert Test Log:</h3>";
    if (isset($_POST['test_insert'])) {
        // Create test log data for admin_logs table
        $detailsData = [
            'session_id' => 'test_session_' . time(),
            'log_level' => 'info',
            'message' => 'This is a test log entry created at ' . date('Y-m-d H:i:s'),
            'original_details' => json_encode(['test' => true, 'timestamp' => time()]),
            'item_id' => null,
            'item_code' => null,
            'options_code' => null,
            'page_url' => $_SERVER['REQUEST_URI'],
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $insertSql = "INSERT INTO admin_logs (
            admin_username, action, target_player, details,
            ip_address, user_agent, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, GETDATE())";

        $params = [
            'TestAdmin',
            '[info] TEST_ACTION',
            null,
            json_encode($detailsData),
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ];

        $stmt = sqlsrv_prepare($conn, $insertSql, $params);

        if ($stmt && sqlsrv_execute($stmt)) {
            echo "<p style='color: green;'>✅ Test log inserted successfully into admin_logs!</p>";
            echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>Refresh page to see the new record</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to insert test log: " . print_r(sqlsrv_errors(), true) . "</p>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='test_insert' value='1'>Insert Test Log into admin_logs</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; }
</style>
