<?php
// Check session and permissions
require_once '../../_app/dbinfo.inc.php';

// You might want to add permission checks here
require_once '../../_app/php/zpanel.class.php';
// $zpanel = new zpanel();
// $zpanel->checkSession(true);

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Item Send History API
 * Retrieves item send history from database
 */

function getItemHistory($conn, $limit = 100, $filters = []) {
    try {
        // Check what tables exist
        $tablesQuery = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
        $tablesResult = sqlsrv_query($conn, $tablesQuery);
        $availableTables = [];
        
        if ($tablesResult) {
            while ($table = sqlsrv_fetch_array($tablesResult, SQLSRV_FETCH_ASSOC)) {
                $availableTables[] = $table['TABLE_NAME'];
            }
        }
        
        // Try different table options
        $tableOptions = [
            'item_sends' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    id, player_id, player_username, item_id, item_name,
                    quantity, duration, send_method, item_code, options_code,
                    admin_username, created_at, processed_at, status, error_message
                    FROM item_sends",
                'map_function' => 'mapItemSends'
            ],
            'admin_logs' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    id, admin_username, action, target_player, details, created_at
                    FROM admin_logs",
                'filter_condition' => "action LIKE '%ITEM_SEND%'",
                'map_function' => 'mapAdminLogs'
            ],
            'notifications' => [
                'sql' => "SELECT TOP " . intval($limit) . "
                    id, title, message, type, target_user, created_by, created_at, metadata
                    FROM notifications",
                'filter_condition' => "type = 'item_send' OR message LIKE '%item%'",
                'map_function' => 'mapNotifications'
            ]
        ];
        
        $selectedTable = null;
        $selectedConfig = null;
        
        // Find the first available table
        foreach ($tableOptions as $tableName => $config) {
            if (in_array($tableName, $availableTables)) {
                $selectedTable = $tableName;
                $selectedConfig = $config;
                break;
            }
        }
        
        if (!$selectedTable) {
            return [];
        }
        
        $sql = $selectedConfig['sql'];
        $whereConditions = [];
        $params = [];
        
        // Add table-specific filter
        if (isset($selectedConfig['filter_condition'])) {
            $whereConditions[] = $selectedConfig['filter_condition'];
        }
        
        // Add user filters
        if (!empty($filters['admin_username'])) {
            $whereConditions[] = "admin_username = ?";
            $params[] = $filters['admin_username'];
        }
        
        if (!empty($filters['player_username'])) {
            if ($selectedTable === 'item_sends') {
                $whereConditions[] = "player_username = ?";
                $params[] = $filters['player_username'];
            } else {
                $whereConditions[] = "target_player = ?";
                $params[] = $filters['player_username'];
            }
        }
        
        if (!empty($filters['status'])) {
            if ($selectedTable === 'item_sends') {
                $whereConditions[] = "status = ?";
                $params[] = $filters['status'];
            } elseif ($selectedTable === 'admin_logs') {
                $whereConditions[] = "details LIKE ?";
                $params[] = '%"status":"' . $filters['status'] . '"%';
            }
        }

        if (!empty($filters['send_method'])) {
            if ($selectedTable === 'item_sends') {
                $whereConditions[] = "send_method = ?";
                $params[] = $filters['send_method'];
            } elseif ($selectedTable === 'admin_logs') {
                $whereConditions[] = "details LIKE ?";
                $params[] = '%"send_method":"' . $filters['send_method'] . '"%';
            }
        }
        
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if (empty($params)) {
            $stmt = sqlsrv_query($conn, $sql);
        } else {
            $stmt = sqlsrv_prepare($conn, $sql, $params);
            if ($stmt === false) {
                throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
            }
            $stmt = sqlsrv_execute($stmt) ? $stmt : false;
        }
        
        if ($stmt === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        $history = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $mappedRow = call_user_func($selectedConfig['map_function'], $row);
            if ($mappedRow) {
                $history[] = $mappedRow;
            }
        }
        
        return $history;
        
    } catch (Exception $e) {
        error_log("Get Item History Error: " . $e->getMessage());
        return [];
    }
}

function mapItemSends($row) {
    return [
        'id' => $row['id'],
        'sendId' => $row['id'],
        'playerId' => $row['player_id'],
        'playerUsername' => $row['player_username'],
        'itemId' => $row['item_id'],
        'itemName' => $row['item_name'],
        'quantity' => $row['quantity'],
        'duration' => $row['duration'],
        'sendMethod' => $row['send_method'],
        'itemCode' => $row['item_code'],
        'optionsCode' => $row['options_code'],
        'adminUsername' => $row['admin_username'],
        'created_at' => is_object($row['created_at']) ? 
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'],
        'processed_at' => is_object($row['processed_at']) ? 
            $row['processed_at']->format('Y-m-d H:i:s') : $row['processed_at'],
        'status' => $row['status'],
        'errorMessage' => $row['error_message'],
        'timestamp' => is_object($row['created_at']) ? 
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at']
    ];
}

function mapAdminLogs($row) {
    $details = json_decode($row['details'] ?? '{}', true);

    // Only return if it's an item send log
    if (!isset($details['item_id']) && !strpos($row['action'], 'ITEM_SEND')) {
        return null;
    }

    return [
        'id' => $row['id'],
        'sendId' => $row['id'],
        'playerUsername' => $row['target_player'],
        'itemId' => $details['item_id'] ?? null,
        'itemName' => $details['item_name'] ?? 'Custom Item',
        'quantity' => $details['quantity'] ?? 1,
        'sendMethod' => $details['send_method'] ?? 'inventory',
        'itemCode' => $details['item_code'] ?? null,
        'optionsCode' => $details['options_code'] ?? null,
        'adminUsername' => $row['admin_username'],
        'created_at' => is_object($row['created_at']) ?
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'],
        'status' => $details['status'] ?? 'unknown',
        'timestamp' => is_object($row['created_at']) ?
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at']
    ];
}

function mapNotifications($row) {
    $metadata = json_decode($row['metadata'] ?? '{}', true);

    // Only return if it's related to item sending
    if ($row['type'] !== 'item_send' && !strpos(strtolower($row['message']), 'item')) {
        return null;
    }

    return [
        'id' => $row['id'],
        'sendId' => $row['id'],
        'playerUsername' => $row['target_user'] ?? $metadata['target_player'] ?? null,
        'itemId' => $metadata['item_id'] ?? null,
        'itemName' => $metadata['item_name'] ?? 'Item from Notification',
        'quantity' => $metadata['quantity'] ?? 1,
        'sendMethod' => $metadata['send_method'] ?? 'notification',
        'itemCode' => $metadata['item_code'] ?? null,
        'optionsCode' => $metadata['options_code'] ?? null,
        'adminUsername' => $row['created_by'],
        'created_at' => is_object($row['created_at']) ?
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'],
        'status' => $metadata['status'] ?? 'notified',
        'timestamp' => is_object($row['created_at']) ?
            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'],
        'message' => $row['message'],
        'title' => $row['title']
    ];
}

// Main API handler
try {
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $limit = intval($_GET['limit'] ?? 100);
        $filters = [
            'admin_username' => $_GET['admin_username'] ?? null,
            'player_username' => $_GET['player_username'] ?? null,
            'status' => $_GET['status'] ?? null,
            'send_method' => $_GET['send_method'] ?? null
        ];
        
        // Remove empty filters
        $filters = array_filter($filters, function($value) {
            return !empty($value);
        });

        // Log filters for debugging
        error_log("Item History Filters: " . print_r($filters, true));

        $history = getItemHistory($conn, $limit, $filters);
        
        echo json_encode([
            'success' => true,
            'history' => $history,
            'count' => count($history),
            'debug' => [
                'limit' => $limit,
                'filters' => $filters,
                'request_time' => date('Y-m-d H:i:s')
            ]
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'history' => []
    ]);
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>
