<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// ปรับ path ให้ถูกต้อง
$basePath = dirname(dirname(dirname(__DIR__)));
require_once($basePath . '/_app/dbinfo.inc.php');
require_once($basePath . '/_app/general_config.inc.php');

function thaitrans($string){
    $name2 =  mb_convert_encoding($string, 'UTF-16', 'UTF-8');
    $name3 =  iconv('windows-874', 'UTF-8',$name2);
    return $name3;
}

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'live_stats':
            echo json_encode(getLiveStats($conn));
            break;
            
        case 'recent_activities':
            $limit = (int)($_GET['limit'] ?? 20);
            echo json_encode(getRecentActivities($conn, $limit));
            break;
            
        case 'alerts':
            echo json_encode(getCharacterAlerts($conn));
            break;
            
        case 'online_stats':
            echo json_encode(getOnlineStats($conn));
            break;
            
        case 'class_stats':
            echo json_encode(getClassStats($conn));
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action,
                'available_actions' => ['live_stats', 'recent_activities', 'alerts', 'online_stats', 'class_stats']
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage(),
        'debug_info' => $debug ? [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ] : null
    ]);
}

function getLiveStats($conn) {
    $stats = [];
    
    try {
        // Total characters
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_characters'] = $row['count'];
        }
        
        // Online characters - ใช้วิธีเดียวกับ getOnlineStats
        $onlineCount = 0;

        // วิธีที่ 1: เช็คจาก Login/Auth table
        $sql = "SELECT COUNT(DISTINCT c.CharacterIdx) as count
                FROM [".DATABASE_SV."].[dbo].cabal_character_table c
                INNER JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table a ON c.UserNum = a.UserNum
                WHERE a.login_flag = 1
                AND a.logout_time IS NULL
                AND c.ChannelIdx > 0";

        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $onlineCount = $row['count'];
        }

        // ถ้าไม่มีข้อมูลจากวิธีแรก ใช้วิธีสำรอง
        if ($onlineCount == 0) {
            $sql = "SELECT COUNT(*) as count
                    FROM [".DATABASE_SV."].[dbo].cabal_character_table
                    WHERE ChannelIdx > 0
                    AND Login = 1";

            $result = sqlsrv_query($conn, $sql);
            if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $onlineCount = $row['count'];
            }
        }

        $stats['online_characters'] = $onlineCount;
        
        // Characters created today
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CAST(CreateDate AS DATE) = CAST(GETDATE() AS DATE)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['created_today'] = $row['count'];
        }
        
        // Characters created this hour
        $sql = "SELECT COUNT(*) as count FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE CreateDate >= DATEADD(hour, DATEDIFF(hour, 0, GETDATE()), 0)
                AND CreateDate < DATEADD(hour, DATEDIFF(hour, 0, GETDATE()) + 1, 0)";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['created_this_hour'] = $row['count'];
        }
        
        // Average level
        $sql = "SELECT AVG(CAST(LEV AS FLOAT)) as avg_level FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE LEV > 0";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['avg_level'] = round($row['avg_level'] ?? 0, 1);
        }
        
        // Total Alz
        $sql = "SELECT SUM(CAST(Alz AS BIGINT)) as total_alz FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_alz'] = $row['total_alz'] ?? 0;
        }
        
        // Total playtime
        $sql = "SELECT SUM(PlayTime) as total_playtime FROM [".DATABASE_SV."].[dbo].cabal_character_table";
        $result = sqlsrv_query($conn, $sql);
        if ($result && $row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            $stats['total_playtime'] = $row['total_playtime'] ?? 0;
        }
        
    } catch (Exception $e) {
        error_log("Live stats error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

function getRecentActivities($conn, $limit = 20) {
    $activities = [];
    $sqlError = null;
    try {
        $limit = intval($limit) > 0 ? intval($limit) : 20;
        $sql = "SELECT TOP $limit
                    CharacterIdx,
                    Name,
                    LEV,
                    Style,
                    CreateDate,
                    WorldIdx,
                    Alz,
                    PlayTime
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                ORDER BY CreateDate DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                $formattedDate = '';
                if ($createDate instanceof DateTime) {
                    $formattedDate = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedDate = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedDate = date('Y-m-d H:i:s');
                }

                $classInfo = getClassInfoFromStyle($row['Style']);

                $activities[] = [
                    'character_idx' => $row['CharacterIdx'],
                    'name' => thaitrans($row['Name']),
                    'level' => $row['LEV'],
                    'style' => $row['Style'],
                    'class_name' => $classInfo['name'],
                    'class_color' => $classInfo['color'],
                    'class_icon' => $classInfo['icon'],
                    'create_date' => $row['CreateDate'],
                    'activity_time' => $formattedDate,
                    'world_idx' => $row['WorldIdx'],
                    'alz' => $row['Alz'],
                    'playtime' => $row['PlayTime'],
                    'CharacterIdx' => $row['CharacterIdx'],
                    'Name' => $row['Name'],
                    'LEV' => $row['LEV'],
                    'Style' => $row['Style'],
                    'CreateDate' => $row['CreateDate'],
                    'WorldIdx' => $row['WorldIdx'],
                    'Alz' => $row['Alz'],
                    'PlayTime' => $row['PlayTime']
                ];
            }
        } else {
            $sqlError = sqlsrv_errors();
            error_log("SQLSRV query error: " . print_r($sqlError, true));
        }
    } catch (Exception $e) {
        error_log("Recent activities error: " . $e->getMessage());
        $sqlError = $e->getMessage();
    }

    // No fallback: Only return real database results. If no activities, return empty array.

    return [
        'success' => true,
        'data' => $activities,
        'count' => count($activities),
        'debug' => [
            'sql_limit' => $limit,
            'activities_count' => count($activities),
            'first_activity' => isset($activities[0]) ? $activities[0] : null,
            'sql_error' => $sqlError
        ]
    ];
}

function getCharacterAlerts($conn) {
    $alerts = [];
    
    try {
        // High level characters created recently
        $sql = "SELECT TOP 3 
                    'high_level_new' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    CreateDate,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table 
                WHERE LEV > 100 AND CreateDate >= DATEADD(hour, -24, GETDATE())
                ORDER BY LEV DESC";
        
        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
               // $name_encode = $userLogin->thaitrans($row['Name']);
                $name_encode = thaitrans($row['Name'] ?? 'N/A');
                $formattedTime = '';
                if ($createDate instanceof DateTime) {
                    $formattedTime = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedTime = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedTime = date('Y-m-d H:i:s');
                }

                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "🔥 ตัวละครเลเวลสูงใหม่: " .htmlspecialchars($name_encode) . " (เลเวล " . $row['LEV'] . ")",
                    'severity' => 'high',
                    'time' => $formattedTime,
                    'data' => $row
                ];
            }
        }
        
        // Characters with excessive Alz (only new characters to avoid duplicates)
        $sql = "SELECT TOP 3
                    'high_alz' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    Alz,
                    CreateDate
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE Alz > 100000000
                AND CreateDate >= DATEADD(day, -1, GETDATE())
                ORDER BY Alz DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                 $name_encode = thaitrans($row['Name'] ?? 'N/A');
                $formattedTime = '';
                if ($createDate instanceof DateTime) {
                    $formattedTime = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedTime = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedTime = date('Y-m-d H:i:s');
                }

                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "💰 ตัวละครเงินเยอะใหม่: " .htmlspecialchars($name_encode) . " (เลเวล " . $row['LEV'] . ", " . number_format($row['Alz']) . " Alz)",
                    'severity' => 'medium',
                    'time' => $formattedTime,
                    'data' => $row
                ];
            }
        }

        // 3. New characters created in last 30 minutes
        $sql = "SELECT TOP 5
                    'new_character' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    CreateDate,
                    Alz
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(minute, -30, GETDATE())
                ORDER BY CreateDate DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                $name_encode = thaitrans($row['Name'] ?? 'N/A');
                $formattedTime = '';
                if ($createDate instanceof DateTime) {
                    $formattedTime = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedTime = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedTime = date('Y-m-d H:i:s');
                }

                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "👤 ตัวละครใหม่: " .htmlspecialchars($name_encode) . " (เลเวล " . $row['LEV'] . ")",
                    'severity' => 'info',
                    'time' => $formattedTime,
                    'data' => $row
                ];
            }
        }

        // 4. Rapid character creation (multiple characters in short time)
        $sql = "SELECT
                    'rapid_creation' as type,
                    COUNT(*) as count,
                    MIN(Name) as first_name,
                    MAX(Name) as last_name
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(hour, -1, GETDATE())
                GROUP BY SUBSTRING(Name, 1, 5)
                HAVING COUNT(*) >= 3
                ORDER BY count DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "⚠️ การสร้างตัวละครรวดเร็ว: " . $row['count'] . " ตัวละครในชั่วโมงที่ผ่านมา",
                    'severity' => 'medium',
                    'time' => date('Y-m-d H:i:s'),
                    'data' => $row
                ];
            }
        }

        // 5. Characters with unusual names (potential bots)
        $sql = "SELECT TOP 3
                    'suspicious_name' as type,
                    CharacterIdx,
                    Name,
                    LEV,
                    CreateDate
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE CreateDate >= DATEADD(hour, -2, GETDATE())
                AND (Name LIKE '%123%' OR Name LIKE '%test%' OR Name LIKE '%bot%' OR LEN(Name) <= 3)
                ORDER BY CreateDate DESC";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $createDate = $row['CreateDate'];
                $name_encode = thaitrans($row['Name'] ?? 'N/A');
                $formattedTime = '';
                if ($createDate instanceof DateTime) {
                    $formattedTime = $createDate->format('Y-m-d H:i:s');
                } elseif ($createDate) {
                    $formattedTime = date('Y-m-d H:i:s', strtotime($createDate));
                } else {
                    $formattedTime = date('Y-m-d H:i:s');
                }

                $alerts[] = [
                    'type' => $row['type'],
                    'message' => "🤖 ชื่อตัวละครน่าสงสัย: " .htmlspecialchars($name_encode) . " (เลเวล " . $row['LEV'] . ")",
                    'severity' => 'medium',
                    'time' => $formattedTime,
                    'data' => $row
                ];
            }
        }

    } catch (Exception $e) {
        error_log("Character alerts error: " . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $alerts,
        'count' => count($alerts)
    ];
}

function getOnlineStats($conn) {
    $stats = [];

    try {
        // เช็คผู้เล่นออนไลน์จากตาราง Login/Session
        // ใช้หลายวิธีเพื่อให้แม่นยำที่สุด

        // วิธีที่ 1: เช็คจาก Login Log (ถ้ามี)
        $onlineUsers = [];

        // ลองเช็คจากตาราง cabal_auth_table หรือ login log
        $sql = "SELECT DISTINCT
                    c.CharacterIdx,
                    c.Name,
                    c.WorldIdx,
                    c.LEV,
                    c.ChannelIdx
                FROM [".DATABASE_SV."].[dbo].cabal_character_table c
                INNER JOIN [".DATABASE_ACC."].[dbo].cabal_auth_table a ON c.UserNum = a.UserNum
                WHERE a.Login = 1
                AND c.Login = 1
                AND c.ChannelIdx > 0";

        $result = sqlsrv_query($conn, $sql);
        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $onlineUsers[] = $row;
            }
        }

        // ถ้าไม่มีข้อมูลจากวิธีแรก ใช้วิธีสำรอง
        if (empty($onlineUsers)) {
            // วิธีที่ 2: เช็คจาก ChannelIdx และเวลาล่าสุด
            $sql = "SELECT
                        CharacterIdx,
                        Name,
                        WorldIdx,
                        LEV,
                        ChannelIdx,
                        LastLoginDate
                    FROM [".DATABASE_SV."].[dbo].cabal_character_table
                    WHERE ChannelIdx > 0
                    AND LastLoginDate >= DATEADD(minute, -30, GETDATE())";

            $result = sqlsrv_query($conn, $sql);
            if ($result) {
                while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                    $onlineUsers[] = $row;
                }
            }
        }

        // นับผู้เล่นออนไลน์ตาม World
        $worldCounts = [];
        foreach ($onlineUsers as $user) {
            $world = $user['WorldIdx'];
            if (!isset($worldCounts[$world])) {
                $worldCounts[$world] = 0;
            }
            $worldCounts[$world]++;
        }

        // จัดเรียงตามจำนวนผู้เล่น
        arsort($worldCounts);

        foreach ($worldCounts as $world => $count) {
            $stats['by_world'][] = [
                'world' => $world,
                'count' => $count
            ];
        }

        // นับผู้เล่นออนไลน์ตาม Level Range
        $levelCounts = [
            '1-50' => 0,
            '51-100' => 0,
            '101-150' => 0,
            '151-200' => 0,
            '200+' => 0
        ];

        foreach ($onlineUsers as $user) {
            $level = $user['LEV'];
            if ($level >= 1 && $level <= 50) {
                $levelCounts['1-50']++;
            } elseif ($level >= 51 && $level <= 100) {
                $levelCounts['51-100']++;
            } elseif ($level >= 101 && $level <= 150) {
                $levelCounts['101-150']++;
            } elseif ($level >= 151 && $level <= 200) {
                $levelCounts['151-200']++;
            } elseif ($level > 200) {
                $levelCounts['200+']++;
            }
        }

        // จัดเรียงตามจำนวนผู้เล่น
        arsort($levelCounts);

        foreach ($levelCounts as $range => $count) {
            if ($count > 0) { // แสดงเฉพาะช่วงที่มีผู้เล่น
                $stats['by_level'][] = [
                    'range' => $range,
                    'count' => $count
                ];
            }
        }

        // เพิ่มข้อมูลสถิติรวม
        $stats['total_online'] = count($onlineUsers);
        $stats['last_updated'] = date('Y-m-d H:i:s');

        // เพิ่มข้อมูลผู้เล่นออนไลน์ล่าสุด (5 คนล่าสุด)
        $recentOnline = array_slice($onlineUsers, 0, 5);
        $stats['recent_online'] = [];
        foreach ($recentOnline as $user) {
            $stats['recent_online'][] = [
                'name' => $user['Name'],
                'level' => $user['LEV'],
                'world' => $user['WorldIdx'],
                'channel' => $user['ChannelIdx']
            ];
        }

    } catch (Exception $e) {
        error_log("Online stats error: " . $e->getMessage());

        // ถ้าเกิด error ให้ส่งข้อมูลเปล่า
        $stats = [
            'total_online' => 0,
            'by_world' => [],
            'by_level' => [],
            'recent_online' => [],
            'last_updated' => date('Y-m-d H:i:s'),
            'error' => 'Unable to fetch online stats'
        ];
    }

    return [
        'success' => true,
        'data' => $stats
    ];
}

function getClassStats($conn) {
    $stats = [];

    try {
        // ใช้ข้อมูลจากฟิลด์ Style ในตาราง cabal_character_table โดยตรง
        // คำนวณคลาสจาก Style value ตามโครงสร้างของ Cabal Online
        $sql = "SELECT
                    Style,
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN ChannelIdx > 0 AND LastLoginDate >= DATEADD(minute, -30, GETDATE()) THEN 1 END) as online_count,
                    AVG(CAST(LEV AS FLOAT)) as avg_level,
                    MAX(LEV) as max_level,
                    MIN(LEV) as min_level
                FROM [".DATABASE_SV."].[dbo].cabal_character_table
                WHERE Style IS NOT NULL AND Style > 0
                GROUP BY Style
                ORDER BY total_count DESC";

        $result = sqlsrv_query($conn, $sql);
        $classDistribution = array();

        if ($result) {
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                // คำนวณคลาสแบบเดียวกับ PHP cabalstyle function
                $style = $row['Style'];
                $battleStyle = $style & 7; // 3 บิตแรก
                $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
                $classIndex = $battleStyle | ($extendedBattleStyle << 3);

                // ชื่อคลาสตาม Cabal Online EP33-35
                $classNames = [
                    0 => 'Novice',
                    1 => 'Warrior',
                    2 => 'Blader',
                    3 => 'Wizard',
                    4 => 'Force Archer',
                    5 => 'Force Shielder',
                    6 => 'Force Blader',
                    7 => 'Gladiator',
                    8 => 'Force Gunner',
                    9 => 'Dark Mage',
                    10 => 'Sage',
                    11 => 'Paladin'
                ];

                $className = $classNames[$classIndex] ?? 'Unknown';

                // รวมจำนวนตามคลาส
                if (isset($classDistribution[$className])) {
                    $classDistribution[$className]['total_count'] += $row['total_count'];
                    $classDistribution[$className]['online_count'] += $row['online_count'];
                    // คำนวณ average level แบบถ่วงน้ำหนัก
                    $totalChars = $classDistribution[$className]['total_count'];
                    $newChars = $row['total_count'];
                    $classDistribution[$className]['avg_level'] =
                        (($classDistribution[$className]['avg_level'] * ($totalChars - $newChars)) +
                         ($row['avg_level'] * $newChars)) / $totalChars;
                    $classDistribution[$className]['max_level'] = max($classDistribution[$className]['max_level'], $row['max_level']);
                    $classDistribution[$className]['min_level'] = min($classDistribution[$className]['min_level'], $row['min_level']);
                } else {
                    $classDistribution[$className] = [
                        'class_name' => $className,
                        'style_value' => $style,
                        'total_count' => $row['total_count'],
                        'online_count' => $row['online_count'],
                        'avg_level' => round($row['avg_level'] ?? 0, 1),
                        'max_level' => $row['max_level'] ?? 0,
                        'min_level' => $row['min_level'] ?? 0,
                        'online_percentage' => $row['total_count'] > 0 ? round(($row['online_count'] / $row['total_count']) * 100, 1) : 0
                    ];
                }
            }

            // แปลงเป็น array และเรียงลำดับ
            foreach ($classDistribution as $data) {
                $stats[] = $data;
            }

            // เรียงลำดับตามจำนวน
            usort($stats, function($a, $b) {
                return $b['total_count'] - $a['total_count'];
            });
        }

    } catch (Exception $e) {
        error_log("Class stats error: " . $e->getMessage());
    }

    return [
        'success' => true,
        'data' => $stats
    ];
}

// ฟังก์ชันช่วยสำหรับแปลง Style value เป็นชื่อคลาส
function getClassNameFromStyle($style) {
    if (!$style || $style <= 0) {
        return 'Novice';
    }

    // คำนวณคลาสจาก Style value ตามโครงสร้างของ Cabal Online
    $battleStyle = $style & 7; // 3 บิตแรก (0-7)
    $extendedBattleStyle = ($style >> 23) & 1; // บิตที่ 23
    $classIndex = $battleStyle | ($extendedBattleStyle << 3);

    // ชื่อคลาสตาม Cabal Online EP33-35
    $classNames = [
        0 => 'Novice',
        1 => 'Warrior',
        2 => 'Blader',
        3 => 'Wizard',
        4 => 'Force Archer',
        5 => 'Force Shielder',
        6 => 'Force Blader',
        7 => 'Gladiator',
        8 => 'Force Gunner',
        9 => 'Dark Mage',
        10 => 'Sage',
        11 => 'Paladin'
    ];

    return $classNames[$classIndex] ?? 'Unknown';
}

// ฟังก์ชันช่วยสำหรับแปลง Style value เป็นข้อมูลคลาสแบบละเอียด
function getClassInfoFromStyle($style) {
    $className = getClassNameFromStyle($style);

    // กำหนดสีและไอคอนสำหรับแต่ละคลาส
    $classInfo = [
        'Novice' => ['color' => '#6c757d', 'icon' => 'fa-user'],
        'Warrior' => ['color' => '#dc3545', 'icon' => 'fa-sword'],
        'Blader' => ['color' => '#fd7e14', 'icon' => 'fa-cut'],
        'Wizard' => ['color' => '#6f42c1', 'icon' => 'fa-magic'],
        'Force Archer' => ['color' => '#28a745', 'icon' => 'fa-bow-arrow'],
        'Force Shielder' => ['color' => '#17a2b8', 'icon' => 'fa-shield'],
        'Force Blader' => ['color' => '#e83e8c', 'icon' => 'fa-sword'],
        'Gladiator' => ['color' => '#ffc107', 'icon' => 'fa-fist-raised'],
        'Force Gunner' => ['color' => '#20c997', 'icon' => 'fa-gun'],
        'Dark Mage' => ['color' => '#343a40', 'icon' => 'fa-skull'],
        'Sage' => ['color' => '#007bff', 'icon' => 'fa-book'],
        'Paladin' => ['color' => '#f8f9fa', 'icon' => 'fa-cross']
    ];

    return [
        'name' => $className,
        'style_value' => $style,
        'color' => $classInfo[$className]['color'] ?? '#6c757d',
        'icon' => $classInfo[$className]['icon'] ?? 'fa-user'
    ];
}
?>
