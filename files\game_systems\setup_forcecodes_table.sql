-- ===================================================================
-- สคริปต์สำหรับสร้างตาราง WEB_cabal_forcecodes และข้อมูลตัวอย่าง
-- ===================================================================

-- ตรวจสอบและสร้างตารางถ้ายังไม่มี
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WEB_cabal_forcecodes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[WEB_cabal_forcecodes] (
        [ForceCode] int NOT NULL,
        [OptionScroll] int NOT NULL,
        [OptionScrollName] nvarchar(1000) COLLATE Thai_CI_AS NOT NULL,
        [ForceCodeName] nvarchar(1000) COLLATE Thai_CI_AS NULL
    )
    
    ALTER TABLE [dbo].[WEB_cabal_forcecodes] SET (LOCK_ESCALATION = TABLE)
    
    PRINT 'สร้างตาราง WEB_cabal_forcecodes สำเร็จ'
END
ELSE
BEGIN
    PRINT 'ตาราง WEB_cabal_forcecodes มีอยู่แล้ว'
END
GO

-- ลบข้อมูลเก่า (ถ้าต้องการ)
-- DELETE FROM [dbo].[WEB_cabal_forcecodes]

-- เพิ่มข้อมูลตัวอย่าง
INSERT INTO [dbo].[WEB_cabal_forcecodes] ([ForceCode], [OptionScroll], [OptionScrollName], [ForceCodeName]) VALUES 
-- Basic Options
(0, 1, 'Random', 'Random'),
(1, 129, 'HP', 'HP'),
(2, 257, 'MP', 'MP'),
(3, 385, 'Attack Rate', 'Attack Rate'),
(4, 513, 'Defense Rate', 'Defense Rate'),
(5, 641, 'Attack', 'Attack'),
(6, 769, 'Defense', 'Defense'),
(7, 897, 'Magic Attack', 'Magic Attack'),
(8, 1025, 'Magic Defense', 'Magic Defense'),
(9, 1153, 'Accuracy', 'Accuracy'),
(10, 1281, 'Evasion', 'Evasion'),

-- Advanced Options
(12, 1537, 'Critical Rate', 'Critical Rate'),
(13, 1665, 'Critical Damage', 'Critical Damage'),
(25, 3201, 'Sword Skill Amp', 'Sword Skill Amp'),
(28, 3585, 'Magic Skill Amp', 'Magic Skill Amp'),
(30, 3841, 'HP Steal', 'HP Steal'),
(32, 4097, 'MP Steal', 'MP Steal'),
(34, 4353, 'Resist Critical Rate', 'Resist Critical Rate'),
(35, 4481, 'Resist Critical Damage', 'Resist Critical Damage'),
(36, 4609, 'Resist Skill Amp', 'Resist Skill Amp'),
(37, 4737, 'Resist Ignore Def', 'Resist Ignore Def'),
(38, 4865, 'Resist Ignore Def Rate', 'Resist Ignore Def Rate'),
(39, 4993, 'Ignore Defense', 'Ignore Defense'),
(41, 5249, 'Ignore Defense Rate', 'Ignore Defense Rate'),
(42, 5377, 'Add Damage', 'Add Damage'),

-- Special Options
(48, 6145, 'All Skill Amp', 'All Skill Amp'),
(70, 8961, 'Charm Resist', 'Charm Resist'),
(71, 9089, 'Fear Resist', 'Fear Resist'),
(72, 9217, 'Stun Resist', 'Stun Resist'),
(73, 9345, 'Down Resist', 'Down Resist'),
(74, 9473, 'Silence Resist', 'Silence Resist'),
(78, 9985, 'Decrease Damage', 'Decrease Damage'),
(79, 10113, 'PvP Damage', 'PvP Damage'),
(80, 10241, 'PvP Defense', 'PvP Defense'),
(81, 10369, 'PvP Magic Damage', 'PvP Magic Damage'),

-- High-Level Options
(97, 12417, 'Sword Skill Amp %', 'Sword Skill Amp %'),
(98, 12545, 'Magic Skill Amp %', 'Magic Skill Amp %'),
(112, 14337, 'Movement Speed', 'Movement Speed'),
(113, 14465, 'Attack Speed', 'Attack Speed'),
(114, 14593, 'Cast Speed', 'Cast Speed'),
(115, 14721, 'Cooldown Reduction', 'Cooldown Reduction'),

-- Ultra Rare Options
(125, 16001, 'Ultimate Damage', 'Ultimate Damage'),
(127, 16257, 'Ultimate Defense', 'Ultimate Defense'),
(128, 16385, 'Ultimate Magic', 'Ultimate Magic'),
(132, 16897, 'Perfect Block', 'Perfect Block'),
(139, 17793, 'Absolute Defense', 'Absolute Defense'),
(153, 19585, 'Divine Protection', 'Divine Protection'),
(158, 20225, 'Legendary Power', 'Legendary Power'),
(163, 20865, 'Mythical Force', 'Mythical Force'),
(181, 23169, 'Transcendent Might', 'Transcendent Might'),

-- Additional Force Codes for new item types
(43, 5505, 'Penetration', 'Penetration'),
(49, 6273, 'Sword Skill Damage', 'Sword Skill Damage'),
(51, 6529, 'Magic Skill Damage', 'Magic Skill Damage')
GO

-- สร้าง Index สำหรับ performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_WEB_cabal_forcecodes_ForceCode' AND object_id = OBJECT_ID('WEB_cabal_forcecodes'))
BEGIN
    CREATE INDEX [IX_WEB_cabal_forcecodes_ForceCode] ON [dbo].[WEB_cabal_forcecodes] ([ForceCode])
    PRINT 'สร้าง Index IX_WEB_cabal_forcecodes_ForceCode สำเร็จ'
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_WEB_cabal_forcecodes_OptionScroll' AND object_id = OBJECT_ID('WEB_cabal_forcecodes'))
BEGIN
    CREATE INDEX [IX_WEB_cabal_forcecodes_OptionScroll] ON [dbo].[WEB_cabal_forcecodes] ([OptionScroll])
    PRINT 'สร้าง Index IX_WEB_cabal_forcecodes_OptionScroll สำเร็จ'
END
GO

-- แสดงสถิติข้อมูล
SELECT 
    COUNT(*) as TotalRecords,
    MIN(ForceCode) as MinForceCode,
    MAX(ForceCode) as MaxForceCode,
    COUNT(DISTINCT OptionScroll) as UniqueOptionScrolls
FROM [dbo].[WEB_cabal_forcecodes]

-- แสดงข้อมูลตัวอย่าง
SELECT TOP 10 * FROM [dbo].[WEB_cabal_forcecodes] ORDER BY ForceCode

PRINT 'การติดตั้งเสร็จสิ้น!'
PRINT 'ตอนนี้สามารถใช้งาน Option Code Tools ได้แล้ว'
GO
