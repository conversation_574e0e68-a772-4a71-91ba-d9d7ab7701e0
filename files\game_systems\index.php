<?php

$zpanel->checkSession(true);

// Get real-time statistics
$stats = [
    'total_items_sent' => 0,
    'total_admin_logs' => 0,
    'recent_activity' => [],
    'system_status' => 'online',
    'last_activity' => null
];

try {
    $conn = db_connect();
    if ($conn) {
        // Get total items sent
        $itemsQuery = "SELECT COUNT(*) as count FROM item_sends";
        $itemsResult = sqlsrv_query($conn, $itemsQuery);
        if ($itemsResult) {
            $itemsRow = sqlsrv_fetch_array($itemsResult, SQLSRV_FETCH_ASSOC);
            $stats['total_items_sent'] = $itemsRow['count'];
        }

        // Get total admin logs
        $logsQuery = "SELECT COUNT(*) as count FROM admin_logs";
        $logsResult = sqlsrv_query($conn, $logsQuery);
        if ($logsResult) {
            $logsRow = sqlsrv_fetch_array($logsResult, SQLSRV_FETCH_ASSOC);
            $stats['total_admin_logs'] = $logsRow['count'];
        }

        // Get recent activity
        $recentQuery = "SELECT TOP 5 admin_username, action, target_player, created_at
                       FROM admin_logs
                       ORDER BY id DESC, created_at DESC";
        $recentResult = sqlsrv_query($conn, $recentQuery);
        if ($recentResult) {
            while ($row = sqlsrv_fetch_array($recentResult, SQLSRV_FETCH_ASSOC)) {
                $stats['recent_activity'][] = [
                    'admin' => $row['admin_username'],
                    'action' => $row['action'],
                    'target' => $row['target_player'],
                    'time' => is_object($row['created_at']) ?
                        $row['created_at']->format('Y-m-d H:i:s') : $row['created_at']
                ];
            }
        }

        // Get last activity time
        if (!empty($stats['recent_activity'])) {
            $stats['last_activity'] = $stats['recent_activity'][0]['time'];
        }

        sqlsrv_close($conn);
    }
} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
}
?>

    <style>
        .dashboard-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .btn-dashboard {
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-dashboard:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
        }
    </style>

    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-gamepad me-3"></i>
                        Game Systems Dashboard
                    </h1>
                    <p class="lead mb-4">
                        ระบบจัดการเกมขั้นสูง สำหรับ Admin และ Developer
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-cogs me-1"></i> Advanced Tools
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-shield-alt me-1"></i> Secure Access
                        </span>
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="fas fa-chart-line me-1"></i> Real-time Monitoring
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-server" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Quick Actions Panel -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-gradient text-white" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="input-group">
                                    <input type="text" id="quickSearch" class="form-control" placeholder="Search systems...">
                                    <button class="btn btn-light" type="button" id="searchBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-outline-primary w-100" onclick="quickAction('advanced-editor')">
                                    <i class="fas fa-cogs me-2"></i>Advanced Editor
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-outline-success w-100" onclick="quickAction('item_send_history')">
                                    <i class="fas fa-history me-2"></i>Send History
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-outline-info w-100" onclick="quickAction('advanced_editor_logs_viewer')">
                                    <i class="fas fa-clipboard-list me-2"></i>Activity Logs
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button id="refreshStatsBtn" class="btn btn-outline-warning w-100" onclick="refreshStats()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Stats
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Statistics -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-paper-plane text-primary" style="font-size: 2rem;"></i>
                        <h3 class="mt-2 mb-1" id="totalItemsSent"><?php echo number_format($stats['total_items_sent']); ?></h3>
                        <p class="text-muted mb-0">Items Sent</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-clipboard-list text-success" style="font-size: 2rem;"></i>
                        <h3 class="mt-2 mb-1" id="totalAdminLogs"><?php echo number_format($stats['total_admin_logs']); ?></h3>
                        <p class="text-muted mb-0">Admin Logs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-circle text-success" style="font-size: 2rem;"></i>
                        <h3 class="mt-2 mb-1" id="systemStatus"><?php echo ucfirst($stats['system_status']); ?></h3>
                        <p class="text-muted mb-0">System Status</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center border-0 shadow-sm">
                    <div class="card-body">
                        <i class="fas fa-clock text-info" style="font-size: 2rem;"></i>
                        <h3 class="mt-2 mb-1" id="lastActivity">
                            <?php echo $stats['last_activity'] ? date('H:i', strtotime($stats['last_activity'])) : 'N/A'; ?>
                        </h3>
                        <p class="text-muted mb-0">Last Activity</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="row mb-5">
            <div class="col-md-8">
                <!-- Main Systems will go here -->
            </div>
            <div class="col-md-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">
                            <i class="fas fa-stream me-2"></i>Recent Activity
                            <small class="text-muted ms-2" id="activityRefreshStatus">(auto-refresh: 45s)</small>
                            <div class="btn-group float-end" role="group">
                                <button id="refreshActivityBtn" class="btn btn-sm btn-outline-secondary" onclick="refreshActivity()">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                    <span class="visually-hidden">Toggle Dropdown</span>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><h6 class="dropdown-header">Auto-refresh Rate</h6></li>
                                    <li><a class="dropdown-item" href="#" onclick="setActivityRefreshRate(30000)">30 seconds</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setActivityRefreshRate(45000)">45 seconds (default)</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setActivityRefreshRate(60000)">1 minute</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setActivityRefreshRate(120000)">2 minutes</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="pauseActivityRefresh()">Pause Auto-refresh</a></li>
                                </ul>
                            </div>
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div id="recentActivityFeed" style="max-height: 400px; overflow-y: auto;">
                            <?php if (!empty($stats['recent_activity'])): ?>
                                <?php foreach ($stats['recent_activity'] as $activity): ?>
                                    <div class="p-3 border-bottom">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-user-circle text-muted"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($activity['action']); ?></h6>
                                                <p class="mb-1 small text-muted">
                                                    by <strong><?php echo htmlspecialchars($activity['admin']); ?></strong>
                                                    <?php if ($activity['target']): ?>
                                                        → <strong><?php echo htmlspecialchars($activity['target']); ?></strong>
                                                    <?php endif; ?>
                                                </p>
                                                <small class="text-muted"><?php echo date('M j, H:i', strtotime($activity['time'])); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="p-3 text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <p>No recent activity</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Systems -->
        <div class="row mb-5">
            <div class="col-md-12">
                <h2 class="text-center mb-4">
                    <i class="fas fa-tools text-primary me-2"></i>
                    Main Systems
                </h2>
                <div class="row">
            
            <!-- Advanced Editor -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card dashboard-card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-cogs card-icon text-primary"></i>
                        <h5 class="card-title">Advanced Item Editor</h5>
                        <p class="card-text">
                            ระบบจัดการไอเทมขั้นสูง คำนวณ Item Code และ Options Code อัตโนมัติ
                        </p>
                        <div class="mb-3">
                            <span class="badge bg-success me-1">Auto-calculation</span>
                            <span class="badge bg-info me-1">Smart Validation</span>
                            <span class="badge bg-warning">History Tracking</span>
                        </div>
                        <a href="?url=game_systems/advanced-editor" class="btn btn-primary btn-dashboard">
                            <i class="fas fa-arrow-right me-2"></i>เข้าใช้งาน
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Item Send History -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card dashboard-card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-history card-icon text-success"></i>
                        <h5 class="card-title">Item Send History</h5>
                        <p class="card-text">
                            ดูประวัติการส่งไอเทมทั้งหมด พร้อมระบบกรองและส่งออกข้อมูล
                        </p>
                        <div class="mb-3">
                            <span class="badge bg-primary me-1">DataTables</span>
                            <span class="badge bg-success me-1">Export CSV</span>
                            <span class="badge bg-info">Advanced Filter</span>
                        </div>
                        <a href="?url=game_systems/item_send_history" class="btn btn-success btn-dashboard">
                            <i class="fas fa-arrow-right me-2"></i>ดูประวัติ
                        </a>
                    </div>
                </div>
            </div>
            <!-- Database Check -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card dashboard-card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-database card-icon text-warning"></i>
                        <h5 class="card-title">Database Check</h5>
                        <p class="card-text">
                            ตรวจสอบสถานะฐานข้อมูล ตาราง และการเชื่อมต่อ
                        </p>
                        <a href="?url=game_systems/check_all_tables" class="btn btn-warning btn-dashboard">
                            <i class="fas fa-search me-2"></i>ตรวจสอบ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- Utilities -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">
                    <i class="fas fa-wrench text-secondary me-2"></i>
                    Utilities & Tools
                </h2>
            </div>
            
            <!-- Connection Test -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card dashboard-card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-plug card-icon text-secondary"></i>
                        <h5 class="card-title">Connection Test</h5>
                        <p class="card-text">
                            ทดสอบการเชื่อมต่อ API และระบบ logging
                        </p>
                        <a href="?url=game_systems/test_logs_connection" class="btn btn-secondary btn-dashboard">
                            <i class="fas fa-wifi me-2"></i>ทดสอบ
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notifications System -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card dashboard-card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-bell card-icon text-primary"></i>
                        <h5 class="card-title">Notifications</h5>
                        <p class="card-text">
                            ระบบแจ้งเตือนและการสื่อสาร
                        </p>
                        <div class="mb-3">
                            <span class="badge bg-info me-1">Real-time</span>
                            <span class="badge bg-success">Push Notifications</span>
                        </div>
                        <a href="?url=game_systems/notifications_api" class="btn btn-primary btn-dashboard">
                            <i class="fas fa-bell me-2"></i>API Test
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">
                    <i class="fas fa-star text-warning me-2"></i>
                    Key Features
                </h2>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4 text-center">
                <i class="fas fa-shield-alt feature-icon"></i>
                <h5>Secure Access</h5>
                <p class="text-muted">ระบบรักษาความปลอดภัยขั้นสูง</p>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4 text-center">
                <i class="fas fa-tachometer-alt feature-icon"></i>
                <h5>Real-time</h5>
                <p class="text-muted">ข้อมูลแบบ real-time และ auto-refresh</p>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4 text-center">
                <i class="fas fa-mobile-alt feature-icon"></i>
                <h5>Responsive</h5>
                <p class="text-muted">รองรับทุกอุปกรณ์ มือถือ แท็บเล็ต</p>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4 text-center">
                <i class="fas fa-chart-bar feature-icon"></i>
                <h5>Analytics</h5>
                <p class="text-muted">สถิติและการวิเคราะห์ข้อมูล</p>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 class="mb-3">
                            <i class="fas fa-rocket me-2"></i>
                            System Status
                        </h3>
                        <div class="row">
                            <div class="col-md-3">
                                <h4 id="systemUptime">99.9%</h4>
                                <small>Uptime</small>
                            </div>
                            <div class="col-md-3">
                                <h4 id="activeUsers">24/7</h4>
                                <small>Available</small>
                            </div>
                            <div class="col-md-3">
                                <h4 id="totalOperations">∞</h4>
                                <small>Operations</small>
                            </div>
                            <div class="col-md-3">
                                <h4 id="responseTime">< 1s</h4>
                                <small>Response Time</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-code me-2"></i>
                Game Systems Dashboard - Advanced Admin Tools
            </p>
            <small class="text-muted">
                Powered by Bootstrap 5 & Font Awesome
            </small>
        </div>
    </footer>

    <script>
        // Quick action functions
        function quickAction(page) {
            window.open(`?url=game_systems/${page}`, '_blank');
        }

        // Refresh statistics with smooth updates
        async function refreshStats() {
            const refreshBtn = document.querySelector('#refreshStatsBtn');
            const originalText = refreshBtn ? refreshBtn.innerHTML : '';

            try {
                // Show loading state
                if (refreshBtn) {
                    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
                    refreshBtn.disabled = true;
                }

                const response = await fetch('files/game_systems/api/get_stats.php');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        await updateStatsDisplay(data.stats);
                        showNotification('Statistics updated successfully', 'success');
                    } else {
                        throw new Error(data.error || 'Failed to fetch stats');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('Error refreshing stats:', error);
                showNotification('Failed to update statistics: ' + error.message, 'error');

                // Graceful degradation - just show error, don't reload
                console.log('Stats refresh failed, continuing with cached data');
            } finally {
                // Restore button state
                if (refreshBtn) {
                    refreshBtn.innerHTML = originalText;
                    refreshBtn.disabled = false;
                }
            }
        }

        // Refresh activity feed with smooth updates
        async function refreshActivity() {
            const activityContainer = document.getElementById('recentActivityFeed');
            const refreshBtn = document.querySelector('#refreshActivityBtn');

            try {
                // Show subtle loading indicator
                if (refreshBtn) {
                    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    refreshBtn.disabled = true;
                }

                const response = await fetch('files/game_systems/log_advanced_editor.php?limit=5');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.logs) {
                        await updateActivityFeed(data.logs);

                        // Subtle success indication
                        if (refreshBtn) {
                            refreshBtn.innerHTML = '<i class="fas fa-check text-success"></i>';
                            setTimeout(() => {
                                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
                            }, 1000);
                        }
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                console.error('Error refreshing activity:', error);

                // Show error state briefly
                if (refreshBtn) {
                    refreshBtn.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                    setTimeout(() => {
                        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
                    }, 2000);
                }
            } finally {
                if (refreshBtn) {
                    refreshBtn.disabled = false;
                }
            }
        }

        // Update stats display with smooth animations
        async function updateStatsDisplay(data) {
            const updates = [
                { id: 'totalItemsSent', value: data.total_items_sent, format: 'number' },
                { id: 'totalAdminLogs', value: data.total_admin_logs, format: 'number' },
                { id: 'systemStatus', value: data.system_status, format: 'text' },
                { id: 'lastActivity', value: data.last_activity, format: 'time' }
            ];

            for (const update of updates) {
                const element = document.getElementById(update.id);
                if (!element || update.value === undefined) continue;

                // Add update animation
                element.style.transition = 'all 0.3s ease';
                element.style.transform = 'scale(1.05)';
                element.style.color = '#007bff';

                // Update value based on format
                let newValue;
                switch (update.format) {
                    case 'number':
                        newValue = update.value.toLocaleString();
                        break;
                    case 'time':
                        newValue = update.value ?
                            new Date(update.value).toLocaleTimeString('th-TH', {
                                hour: '2-digit',
                                minute: '2-digit'
                            }) : 'N/A';
                        break;
                    case 'text':
                    default:
                        newValue = update.value;
                }

                element.textContent = newValue;

                // Reset animation after delay
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 300);

                // Small delay between updates for smooth effect
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // Update activity feed with smooth transitions
        async function updateActivityFeed(activities) {
            const feed = document.getElementById('recentActivityFeed');
            if (!feed) return;

            // Fade out current content
            feed.style.transition = 'opacity 0.3s ease';
            feed.style.opacity = '0.5';

            await new Promise(resolve => setTimeout(resolve, 3000));

            if (!activities || activities.length === 0) {
                feed.innerHTML = `
                    <div class="p-3 text-center text-muted fade-in">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>No recent activity</p>
                    </div>
                `;
            } else {
                feed.innerHTML = activities.map((activity, index) => `
                    <div class="p-3 border-bottom activity-item" style="animation-delay: ${index * 0.1}s">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-user-circle text-muted"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">${escapeHtml(activity.action || 'Unknown Action')}</h6>
                                <p class="mb-1 small text-muted">
                                    by <strong>${escapeHtml(activity.admin_username || 'System')}</strong>
                                    ${activity.target_player ? `→ <strong>${escapeHtml(activity.target_player)}</strong>` : ''}
                                </p>
                                <small class="text-muted">${formatTime(activity.created_at)}</small>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // Fade in new content
            feed.style.opacity = '1';

            // Add CSS for animations if not exists
            if (!document.getElementById('activityAnimations')) {
                const style = document.createElement('style');
                style.id = 'activityAnimations';
                style.textContent = `
                    .activity-item {
                        animation: slideInLeft 0.5s ease forwards;
                        opacity: 0;
                        transform: translateX(-20px);
                    }
                    @keyframes slideInLeft {
                        to {
                            opacity: 1;
                            transform: translateX(0);
                        }
                    }
                    .fade-in {
                        animation: fadeIn 0.5s ease forwards;
                    }
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Helper functions
        function formatTime(timeString) {
            try {
                const date = new Date(timeString);
                return date.toLocaleDateString('th-TH', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return timeString || 'Unknown';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Refresh rate control functions
        let activityRefreshTimer = null;
        let statsRefreshTimer = null;
        let isActivityRefreshPaused = false;

        function setActivityRefreshRate(newRate) {
            activityRefreshInterval = newRate;

            // Update status display
            const statusElement = document.getElementById('activityRefreshStatus');
            if (statusElement) {
                statusElement.textContent = `(auto-refresh: ${newRate/1000}s)`;
            }

            // Clear existing timer and start new one
            if (activityRefreshTimer) {
                clearInterval(activityRefreshTimer);
            }

            if (!isActivityRefreshPaused) {
                activityRefreshTimer = setInterval(smartRefreshActivity, newRate);
                showNotification(`Activity refresh rate set to ${newRate/1000} seconds`, 'success');
            }

            console.log(`Activity refresh rate changed to ${newRate/1000}s`);
        }

        function pauseActivityRefresh() {
            isActivityRefreshPaused = !isActivityRefreshPaused;

            const statusElement = document.getElementById('activityRefreshStatus');

            if (isActivityRefreshPaused) {
                if (activityRefreshTimer) {
                    clearInterval(activityRefreshTimer);
                    activityRefreshTimer = null;
                }
                if (statusElement) {
                    statusElement.textContent = '(paused)';
                    statusElement.className = 'text-warning ms-2';
                }
                showNotification('Activity auto-refresh paused', 'warning');
                console.log('Activity auto-refresh paused');
            } else {
                activityRefreshTimer = setInterval(smartRefreshActivity, activityRefreshInterval);
                if (statusElement) {
                    statusElement.textContent = `(auto-refresh: ${activityRefreshInterval/1000}s)`;
                    statusElement.className = 'text-muted ms-2';
                }
                showNotification('Activity auto-refresh resumed', 'success');
                console.log('Activity auto-refresh resumed');
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Search functionality
        function setupSearch() {
            const searchInput = document.getElementById('quickSearch');
            const searchBtn = document.getElementById('searchBtn');

            function performSearch() {
                const query = searchInput.value.toLowerCase().trim();
                if (!query) return;

                // Search through cards and highlight matches
                const cards = document.querySelectorAll('.dashboard-card');
                let found = false;

                cards.forEach(card => {
                    const text = card.textContent.toLowerCase();
                    if (text.includes(query)) {
                        card.style.border = '2px solid #007bff';
                        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        found = true;
                    } else {
                        card.style.border = '';
                    }
                });

                if (!found) {
                    showNotification(`No results found for "${query}"`, 'warning');
                }
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Clear search on input clear
            searchInput.addEventListener('input', function() {
                if (!this.value) {
                    document.querySelectorAll('.dashboard-card').forEach(card => {
                        card.style.border = '';
                    });
                }
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Setup search
            setupSearch();

            // Smart auto-refresh with exponential backoff
            let statsRefreshInterval = 60000; // Start with 60 seconds (slower)
            let activityRefreshInterval = 45000; // Start with 45 seconds (much slower)
            let consecutiveErrors = 0;

            function smartRefreshStats() {
                refreshStats().then(() => {
                    consecutiveErrors = 0;
                    statsRefreshInterval = 60000; // Reset to normal interval (60s)
                }).catch(() => {
                    consecutiveErrors++;
                    // Exponential backoff: increase interval on errors
                    statsRefreshInterval = Math.min(60000 * Math.pow(2, consecutiveErrors), 600000); // Max 10 minutes
                    console.log(`Stats refresh failed ${consecutiveErrors} times, next attempt in ${statsRefreshInterval/1000}s`);
                });
            }

            function smartRefreshActivity() {
                refreshActivity().then(() => {
                    activityRefreshInterval = 45000; // Reset to normal (45s)
                }).catch(() => {
                    activityRefreshInterval = Math.min(activityRefreshInterval * 2, 180000); // Max 3 minutes
                    console.log(`Activity refresh failed, next attempt in ${activityRefreshInterval/1000}s`);
                });
            }

            // Start smart refresh cycles with controllable timers
            statsRefreshTimer = setInterval(smartRefreshStats, statsRefreshInterval);
            activityRefreshTimer = setInterval(smartRefreshActivity, activityRefreshInterval);

            // Page visibility API - pause refresh when tab is not active
            let isPageVisible = true;
            document.addEventListener('visibilitychange', function() {
                isPageVisible = !document.hidden;
                if (isPageVisible) {
                    // Refresh immediately when page becomes visible
                    setTimeout(smartRefreshStats, 1000);
                    setTimeout(smartRefreshActivity, 1500);
                }
            });

            // Animate cards on scroll
            const cards = document.querySelectorAll('.dashboard-card');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });

            // Show welcome notification
            setTimeout(() => {
                showNotification('Dashboard loaded successfully', 'success');
            }, 1000);
        });
    </script>