<?php $zpanel->checkSession(true); ?>
<?php $userLogin->recUserPerm($conn, 'dev_masster', 'menu'); ?>
<!-- Notification System Styles -->
<link rel="stylesheet" href="files/game_systems/notification_styles.css">


<div class="row">
    <div class="col-xl-7">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4><i class="fas fa-cogs mr-2"></i>Advanced Item Editor</h4>
                <small><i class="fas fa-save mr-1"></i>Auto-saves your progress - data persists after refresh</small>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle mr-2"></i>Advanced Item Editor</h6>
                    <p class="mb-2">ระบบจัดการไอเท็มขั้นสูง สำหรับคำนวณ Item Code และ Options Code</p>
                    <div class="row">
                        <div class="col-md-6">
                            <small><strong>✨ Options Code:</strong> คำนวณจาก slots อัตโนมัติ หรือกรอกเองได้</small>
                        </div>
                        <div class="col-md-6">
                            <small><strong>🔄 Smart Switch:</strong> ใช้ slots = คำนวณอัตโนมัติ | กรอกเอง = รีเซ็ต
                                slots</small>
                        </div>
                    </div>
                </div>
                <!-- Quick Test Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-info">
                            <div class="card-header bg-info text-white d-flex align-items-center">
                                <h6 class="mb-0"><i class="fas fa-edit mr-2"></i>Item Configuration Form</h6>
                                <span class="ml-auto" data-toggle="tooltip" title="Configure and preview item codes"><i
                                        class="fas fa-question-circle"></i></span>
                            </div>
                            <div class="card-body pb-2">
                                <div class="row align-items-end">
                                    <div class="col-md-5 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-search mr-1"></i>Item Search</label>
                                        <input type="text" id="itemSearch" class="form-control"
                                            placeholder="🔍 Search item name..." list="itemSearchList"
                                            autocomplete="off">
                                        <datalist id="itemSearchList">
                                            <!-- Items will be loaded here -->
                                        </datalist>
                                        <small class="text-info"><i class="fas fa-info-circle"></i> Search and select
                                            item from database</small>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-barcode mr-1"></i>Item ID</label>
                                        <div class="input-group">
                                            <input type="number" id="itemId" class="form-control" value="" min="1"
                                                max="999999" placeholder="Enter ID" aria-label="Item ID">
                                            <div class="input-group-append">
                                                <button type="button" id="validateItemBtn"
                                                    class="btn btn-outline-info btn-sm" title="Validate Item ID">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small id="itemIdStatus" class="text-muted">Enter item ID to validate</small>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-cogs mr-1"></i>Options Code</label>
                                        <div class="input-group">
                                            <input type="text" id="optionsCode" class="form-control"
                                                placeholder="Auto-calculated"
                                                title="Options Code - Real-time calculation" aria-label="Options Code">
                                            <div class="input-group-append">
                                                <button type="button" id="copyOptionsCodeBtn"
                                                    class="btn btn-outline-success btn-sm" title="Copy Options Code">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button type="button" id="applyOptionsCodeBtn"
                                                    class="btn btn-outline-primary btn-sm"
                                                    title="Apply Options Code and Reset Slots">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" id="clearOptionsCodeBtn"
                                                    class="btn btn-outline-secondary btn-sm" title="Clear Options Code">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small id="optionsCodeStatus" class="text-muted">Auto-calculation from slots
                                            <span class="text-warning">(กรอกเพื่อรีเซ็ต slots)</span></small>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i class="fas fa-cube mr-1"></i>Item
                                            Type</label>
                                        <select id="itemType" class="form-control">
                                            <option value="NOT" selected>NOT</option>
                                            <option value="Weapon">Weapon</option>
                                            <option value="Helm">Helm</option>
                                            <option value="Suit">Suit</option>
                                            <option value="Gloves">Gloves</option>
                                            <option value="Boots">Boots</option>
                                            <option value="Bike">Bike</option>
                                            <option value="Amulet">Amulet</option>
                                            <option value="Belt">Belt</option>
                                            <option value="Earring">Earring</option>
                                            <option value="Epulet">Epulet</option>
                                            <option value="Ring">Ring</option>
                                            <option value="Brooch">Brooch</option>
                                            <option value="Charm">Charm</option>
                                            <option value="Bike_11">Bike_11</option>
                                            <option value="Bike_14">Bike_14</option>
                                            <option value="Board">Board</option>
                                            <option value="Bracelet">Bracelet</option>
                                            <option value="Costume_1">Costume_1</option>
                                            <option value="Costume_2">Costume_2</option>
                                            <option value="Costume_3">Costume_3</option>
                                            <option value="Uniquecostume">Uniquecostume</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-arrow-up mr-1"></i>Upgrade</label>
                                        <select id="upgrade" class="form-control">
                                            <option value="0" selected>+0</option>
                                            <option value="1">+1</option>
                                            <option value="2">+2</option>
                                            <option value="3">+3</option>
                                            <option value="4">+4</option>
                                            <option value="5">+5</option>
                                            <option value="6">+6</option>
                                            <option value="7">+7</option>
                                            <option value="8">+8</option>
                                            <option value="9">+9</option>
                                            <option value="10">+10</option>
                                            <option value="11">+11</option>
                                            <option value="12">+12</option>
                                            <option value="13">+13</option>
                                            <option value="14">+14</option>
                                            <option value="15">+15</option>
                                            <option value="16">+16</option>
                                            <option value="17">+17</option>
                                            <option value="18">+18</option>
                                            <option value="19">+19</option>
                                            <option value="20">+20</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-bolt mr-1"></i>Extreme</label>
                                        <select id="extreme" class="form-control">
                                            <option value="0" selected>0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-gem mr-1"></i>Divine</label>
                                        <select id="divine" class="form-control">
                                            <option value="0" selected>0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                            <option value="11">11</option>
                                            <option value="12">12</option>
                                            <option value="13">13</option>
                                            <option value="14">14</option>
                                            <option value="15">15</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-link mr-1"></i>Binding</label>
                                        <select id="binding" class="form-control">
                                            <option value="none" selected>No Binding</option>
                                            <option value="id">Bind to ID</option>
                                            <option value="char">Bind to Character</option>
                                            <option value="equ">Bind on Equip</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-clock mr-1"></i>Duration</label>
                                        <select id="duration" class="form-control">
                                            <optgroup label="อายุ ชม.">
                                                <option value="31" selected>31=ถาวร</option>
                                                <option value="1">1=1 ชม.</option>
                                                <option value="2">2=2 ชม.</option>
                                                <option value="3">3=3 ชม.</option>
                                                <option value="4">4=4 ชม.</option>
                                                <option value="5">5=5 ชม.</option>
                                                <option value="6">6=6 ชม.</option>
                                                <option value="7">7=10 ชม.</option>
                                                <option value="8">8=12 ชม</option>
                                                <option value="9">9=1 วัน</option>
                                                <option value="10">10=3 วัน</option>
                                                <option value="11">11=5 วัน</option>
                                                <option value="12">12=7 วัน</option>
                                                <option value="13">13=10 วัน</option>
                                                <option value="14">14=14 วัน</option>
                                                <option value="15">15=15 วัน</option>
                                                <option value="16">16=20 วัน</option>
                                                <option value="17">17=30 วัน</option>
                                                <option value="18">18=45 วัน</option>
                                                <option value="19">19=60 วัน</option>
                                                <option value="20">20=90 วัน</option>
                                                <option value="21">21=100 วัน</option>
                                                <option value="22">22=120 วัน</option>
                                                <option value="23">23=180 วัน</option>
                                                <option value="24">24=270 วัน</option>
                                                <option value="25">25=365 วัน</option>
                                                <option value="26">26=3 นาที</option>
                                                <option value="27">27=30 นาที</option>
                                                <option value="28">28=90 นาที</option>
                                                <option value="29">29=10 นาที</option>
                                                <option value="30">30=0 วัน</option>
                                                <option value="0">ไม่มีอายุ</option>
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>
                                <hr class="m-0 w-100">
                                <div class="row mt-3">
                                    <div class="col-md-4 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-sliders-h mr-1"></i>Slot 1</label>
                                        <select id="slot1" class="form-control">
                                            <option value="NOT">NOT</option>
                                            <option value="STR+1" selected>STR+1</option>
                                            <option value="DEX+1">DEX+1</option>
                                            <option value="INT+1">INT+1</option>
                                            <option value="HP+10">HP+10</option>
                                            <option value="MP+10">MP+10</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-sliders-h mr-1"></i>Slot 2</label>
                                        <select id="slot2" class="form-control">
                                            <option value="NOT">NOT</option>
                                            <option value="STR+1">STR+1</option>
                                            <option value="DEX+1">DEX+1</option>
                                            <option value="INT+1">INT+1</option>
                                            <option value="HP+10" selected>HP+10</option>
                                            <option value="MP+10">MP+10</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-sliders-h mr-1"></i>Slot 3</label>
                                        <select id="slot3" class="form-control">
                                            <option value="NOT" selected>NOT</option>
                                            <option value="STR+1">STR+1</option>
                                            <option value="DEX+1">DEX+1</option>
                                            <option value="INT+1">INT+1</option>
                                            <option value="HP+10">HP+10</option>
                                            <option value="MP+10">MP+10</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Craft Options -->
                                <div class="row mt-3">
                                    <div class="col-md-6 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-hammer mr-1"></i>Craft Option</label>
                                        <select id="craftOption" class="form-control">
                                            <option value="NOT" selected>NOT</option>
                                        </select>
                                        <small class="text-muted">Additional craft option slot</small>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label class="form-label font-weight-bold"><i
                                                class="fas fa-signal mr-1"></i>Craft Height (0-7)</label>
                                        <select id="craftHeight" class="form-control">
                                            <option value="0" selected>0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                        </select>
                                        <small class="text-muted">Craft option strength level</small>
                                    </div>
                                </div>
                                <!-- Hex/Dec Converter Section (compatible with manage-item.php) -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white py-2">
                                                <h6 class="mb-0"><i class="fas fa-exchange-alt mr-2"></i>Hex/Dec
                                                    Converter</h6>
                                            </div>
                                            <div class="card-body py-2 bg-light">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <label class="form-label">Hex to Dec:</label>
                                                        <input type="text" id="hextodec" class="form-control"
                                                            placeholder="Enter HEX value">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Dec Result:</label>
                                                        <input type="text" id="deccode" class="form-control" readonly
                                                            placeholder="Decimal result">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Dec to Hex:</label>
                                                        <input type="text" id="dectohex" class="form-control"
                                                            placeholder="Enter DEC value">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Hex Result:</label>
                                                        <input type="text" id="hexcode" class="form-control" readonly
                                                            placeholder="Hex result">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Binding Codes Section (compatible with manage-item.php) -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark py-2">
                                                <h6 class="mb-0"><i class="fas fa-link mr-2"></i>Binding Codes
                                                    Calculator</h6>
                                            </div>
                                            <div class="card-body py-2 bg-light">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <label class="form-label">Base Item Code:</label>
                                                        <input type="text" id="input_Item" class="form-control"
                                                            placeholder="Enter item code">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Bind-ID Result:</label>
                                                        <input type="text" id="codeoutput_bid" class="form-control"
                                                            readonly placeholder="Item + 4096">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Bind-Char Result:</label>
                                                        <input type="text" id="codeoutput_bchar" class="form-control"
                                                            readonly placeholder="Item + 524288">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label class="form-label">Bind-Equ Result:</label>
                                                        <input type="text" id="codeoutput_bequ" class="form-control"
                                                            readonly placeholder="Item + 1572864">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Send Item Section -->
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white py-2">
                                                <h6 class="mb-0"><i class="fas fa-paper-plane mr-2"></i>Send Item to
                                                    Player</h6>
                                            </div>
                                            <div class="card-body py-2">
                                                <div class="row">
                                                    <div class="col-md-4 mb-2">
                                                        <label class="form-label font-weight-bold"><i
                                                                class="fas fa-user mr-1"></i>Player Username</label>
                                                        <div class="input-group">
                                                            <input type="text" id="playerUsername" class="form-control"
                                                                placeholder="Enter player username"
                                                                list="playerSearchList">
                                                            <div class="input-group-append">
                                                                <button type="button" id="validatePlayerBtn"
                                                                    class="btn btn-outline-info btn-sm"
                                                                    title="Validate Player from Database">
                                                                    <i class="fas fa-user-check"></i>
                                                                </button>
                                                                <button type="button" id="loadPlayerDbBtn"
                                                                    class="btn btn-outline-secondary btn-sm"
                                                                    title="Load Player Database">
                                                                    <i class="fas fa-users"></i>
                                                                </button>
                                                                <button type="button" id="showPlayerListBtn"
                                                                    class="btn btn-outline-warning btn-sm"
                                                                    title="Show Player List">
                                                                    <i class="fas fa-list"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <datalist id="playerSearchList">
                                                            <!-- Players will be loaded here -->
                                                        </datalist>
                                                        <small id="playerStatus" class="text-muted">Enter username to
                                                            validate</small>
                                                    </div>
                                                    <div class="col-md-2 mb-2">
                                                        <label class="form-label font-weight-bold"><i
                                                                class="fas fa-sort-numeric-up mr-1"></i>Quantity</label>
                                                        <input type="number" id="itemQuantity" class="form-control"
                                                            value="1" min="1" max="999">
                                                        <small class="text-muted">Amount to send</small>
                                                    </div>
                                                    <div class="col-md-3 mb-2">
                                                        <label class="form-label font-weight-bold"><i
                                                                class="fas fa-truck mr-1"></i>Send Method</label>
                                                        <select id="sendMethod" class="form-control">
                                                            <option value="inventory">ส่งไปยัง Cash Inventory</option>
                                                            <option value="mail">ส่งไปยัง Mail System</option>
                                                            <option value="event_inventory">ส่งไปยัง Event Inventory</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3 mb-2">
                                                        <label class="form-label font-weight-bold"><i
                                                                class="fas fa-cogs mr-1"></i>Send Options</label>
                                                        <div class="btn-group w-100" role="group">
                                                            <button type="button" id="sendItemBtn"
                                                                class="btn btn-success" disabled>
                                                                <i class="fas fa-paper-plane mr-1"></i>Send Item
                                                            </button>
                                                            <button type="button" id="previewItemBtn"
                                                                class="btn btn-outline-info">
                                                                <i class="fas fa-eye mr-1"></i>Preview
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-md-12">
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox"
                                                                id="sendNotification" checked>
                                                            <label class="form-check-label" for="sendNotification">
                                                                <small>Send notification to player</small>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input class="form-check-input" type="checkbox"
                                                                id="logTransaction">
                                                            <label class="form-check-label" for="logTransaction">
                                                                <small>Log transaction</small>
                                                            </label>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="row mt-3">
                                    <div class="col-md-2">
                                        <button type="button" id="calculateFromFormBtn" class="btn btn-primary w-100">
                                            <i class="fas fa-calculator mr-2"></i>Calculate
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="realTimeCalculation"
                                                checked>
                                            <label class="form-check-label" for="realTimeCalculation">
                                                <small><i class="fas fa-sync mr-1"></i>Real-time</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" id="resetFormBtn" class="btn btn-outline-secondary w-100">
                                            <i class="fas fa-undo mr-2"></i>Reset
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" id="clearSavedDataBtn"
                                            class="btn btn-outline-danger w-100">
                                            <i class="fas fa-trash mr-2"></i>Clear Saved Data
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" id="itemHistoryBtn" class="btn btn-outline-info w-100">
                                            <i class="fas fa-history mr-2"></i>Send History
                                        </button>
                                    </div>
                                </div>
                                <hr>
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <button type="button" id="converterBtn" class="btn btn-outline-info w-100 mb-2">
                                            <i class="fas fa-exchange-alt mr-2"></i>Dec ⇄ Hex
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" id="testToastBtn" class="btn btn-outline-warning w-100 mb-2">
                                            <i class="fas fa-bell mr-2"></i>Test Toast
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" id="clearToastsBtn" class="btn btn-outline-secondary w-100 mb-2">
                                            <i class="fas fa-times mr-2"></i>Clear Toasts
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" id="testSmartAdminToastBtn" class="btn btn-outline-primary w-100 mb-2">
                                            <i class="fas fa-star mr-2"></i>SmartAdmin Toast
                                        </button>
                                    </div>
                                </div>

                                <hr>
                            </div>

                        </div>

                    </div>
                </div>

                <!-- Results Display -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-bar mr-2"></i>Calculation Results</h6>
                            </div>
                            <div class="card-body">
                                <div id="resultsDisplay" class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>No calculations performed yet. Use the
                                    buttons above to test functions.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="row">
                    <div class="col-12">
                        <h6>Debug Console:</h6>
                        <div id="debugConsole" class="bg-dark text-light p-3 rounded"
                            style="height: 300px; overflow-y: auto; font-family: monospace;">
                            <!-- Debug messages will appear here -->
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="button" id="clearConsoleBtn" class="btn btn-secondary btn-sm">
                        <i class="fas fa-trash mr-1"></i>Clear Console
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6><i class="fas fa-table mr-2"></i>Item Database Browser</h6>
                <small class="card-header bg-primary text-white">Search and select items from database</small>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <button type="button" id="loadItemTableBtn" class="btn btn-primary">
                            <i class="fas fa-download mr-2"></i>Load Item Database
                        </button>
                        <button type="button" id="refreshTableBtn" class="btn btn-outline-secondary ml-2">
                            <i class="fas fa-sync mr-2"></i>Refresh
                        </button>
                    </div>
                    <div class="col-md-4 text-right">
                        <span id="itemCountBadge" class="badge badge-info">0 items loaded</span>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="itemsTable" class="table table-striped table-bordered table-hover" style="width:100%">
                        <thead class="thead-dark">
                            <tr>
                                <th width="10%">ID</th>
                                <th width="65%">Item Name</th>
                                <th width="25%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div id="toastContainer" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
    <!-- Toasts will be dynamically added here -->
</div>

<!-- Custom DataTables Styling -->
<style>
/* DataTables Search Box Styling */
#itemsTable_filter {
    margin-bottom: 10px;
    text-align: left !important;
}

#itemsTable_filter input {
    border-radius: 20px;
    border: 1px solid #ced4da;
    padding: 5px 15px;
    width: 200px !important;
    margin-left: 8px;
}

#itemsTable_filter input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#itemsTable_filter label {
    font-weight: 500;
    color: #495057;
}

/* DataTables Length Menu Styling */
#itemsTable_length {
    text-align: right !important;
}

#itemsTable_length select {
    border-radius: 5px;
    margin: 0 5px;
    width: 70px !important;
}

#itemsTable_length label {
    font-weight: 500;
    color: #495057;
    justify-content: flex-end !important;
}

/* DataTables Info Styling */
#itemsTable_info {
    font-size: 0.875rem;
    margin-top: 10px;
}

/* DataTables Pagination Styling */
#itemsTable_paginate {
    margin-top: 10px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.25rem 0.5rem;
    margin: 0 2px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    background: white;
    color: #007bff;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* Toast Styles */
.toast {
    background-color: white;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.hide {
    opacity: 0;
    transform: translateX(100%);
}

.toast-header {
    background-color: rgba(0,0,0,.03);
    border-bottom: 1px solid rgba(0,0,0,.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
}

.toast-body {
    padding: 0.75rem;
    word-wrap: break-word;
}

.close {
    background: none;
    border: none;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    opacity: 0.5;
    cursor: pointer;
}

.close:hover {
    opacity: 0.75;
}

/* Animation for toast entrance */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast.fade.show {
    animation: slideInRight 0.3s ease-out;
}
</style>

<!-- SweetAlert2 for beautiful alerts and animations -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Load ItemManager -->
<script src="files/game_systems/js/item-manager.js"></script>

<script>
// Custom console for debugging
const debugConsole = document.getElementById('debugConsole');

function addToConsole(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: 'text-info',
        success: 'text-success',
        warning: 'text-warning',
        error: 'text-danger'
    };

    const div = document.createElement('div');
    div.className = colors[type] || 'text-light';
    div.innerHTML = `[${timestamp}] ${message}`;
    debugConsole.appendChild(div);
    debugConsole.scrollTop = debugConsole.scrollHeight;
}

// Override console methods for debugging
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

console.log = function(...args) {
    originalLog.apply(console, args);
    addToConsole(args.join(' '), 'info');
};

console.error = function(...args) {
    originalError.apply(console, args);
    addToConsole(args.join(' '), 'error');
};

console.warn = function(...args) {
    originalWarn.apply(console, args);
    addToConsole(args.join(' '), 'warning');
};

/**
 * Show toast notification
 * @param {string} message - The message to display
 * @param {string} type - Type of notification (success, error, warning, info)
 * @param {number} duration - Duration in milliseconds (0 for persistent)
 * @param {string} time - Custom time string
 * @param {string} title - Custom title (default: 'การแจ้งเตือน')
 * @param {boolean} showLogo - Show logo instead of icon (default: false)
 */
function showToast(message, type = 'info', duration = 5000, time = null, title = 'การแจ้งเตือน', showLogo = false) {
    const container = document.getElementById('toastContainer');
    if (!container) {
        console.warn('Toast container not found');
        return;
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast fade show mb-2';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.setAttribute('data-toggle', 'toast');
    toast.style.minWidth = '300px';

    // Define icon and color classes based on type
    const typeConfig = {
        success: { icon: 'fa-check-circle', class: 'text-success' },
        error: { icon: 'fa-exclamation-circle', class: 'text-danger' },
        warning: { icon: 'fa-exclamation-triangle', class: 'text-warning' },
        info: { icon: 'fa-info-circle', class: 'text-info' }
    };

    const config = typeConfig[type] || typeConfig.info;
    const icon = config.icon;
    const typeClass = config.class;

    // Create header content based on showLogo option
    const headerIcon = showLogo
        ? '<img src="img/logo.png" alt="brand-logo" height="16" class="mr-2">'
        : `<i class="fal ${icon} ${typeClass} mr-2"></i>`;

    toast.innerHTML = `
        <div class="toast-header">
            ${headerIcon}
            <strong class="mr-auto">${title}</strong>
            <small class="text-muted">${time || 'just now'}</small>
            <button type="button" class="ml-2 close" data-dismiss="toast" aria-label="Close" onclick="removeToast(this)">
                <span aria-hidden="true">×</span>
            </button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;

    // Add to container
    container.appendChild(toast);

    // Auto-remove after duration (if not persistent)
    if (duration > 0) {
        setTimeout(() => {
            removeToast(toast);
        }, duration);
    }

    // Add entrance animation
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    return toast;
}

/**
 * Remove toast notification
 * @param {Element} element - Toast element or close button
 */
function removeToast(element) {
    const toast = element.closest ? element.closest('.toast') : element.parentElement.parentElement;
    if (toast) {
        toast.classList.remove('show');
        toast.classList.add('hide');

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

/**
 * Clear all toasts
 */
function clearAllToasts() {
    const container = document.getElementById('toastContainer');
    if (container) {
        const toasts = container.querySelectorAll('.toast');
        toasts.forEach(toast => removeToast(toast));
    }
}

/**
 * Show success toast
 */
function showSuccessToast(message, duration = 3000, title = 'สำเร็จ', showLogo = false) {
    return showToast(message, 'success', duration, null, title, showLogo);
}

/**
 * Show error toast
 */
function showErrorToast(message, duration = 0, title = 'ข้อผิดพลาด', showLogo = false) {
    return showToast(message, 'error', duration, null, title, showLogo);
}

/**
 * Show warning toast
 */
function showWarningToast(message, duration = 5000, title = 'คำเตือน', showLogo = false) {
    return showToast(message, 'warning', duration, null, title, showLogo);
}

/**
 * Show info toast
 */
function showInfoToast(message, duration = 4000, title = 'ข้อมูล', showLogo = false) {
    return showToast(message, 'info', duration, null, title, showLogo);
}

/**
 * Show toast with logo (SmartAdmin style)
 */
function showSmartAdminToast(message, type = 'info', duration = 5000, time = null) {
    return showToast(message, type, duration, time, 'SmartAdmin', true);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addToConsole('🚀 DOM loaded, initializing test environment...', 'info');

    // Check required libraries
    addToConsole(`jQuery loaded: ${typeof $ !== 'undefined'}`, 'info');
    addToConsole(`SweetAlert2 loaded: ${typeof Swal !== 'undefined'}`, 'info');
    addToConsole(`ItemManager class available: ${typeof ItemManager !== 'undefined'}`, 'info');

    try {
        // Initialize ItemManager
        if (typeof ItemManager !== 'undefined') {
            window.itemManager = new ItemManager();
            addToConsole('✅ ItemManager initialized successfully', 'success');
        } else {
            addToConsole('❌ ItemManager class not found', 'error');
        }
    } catch (error) {
        addToConsole(`❌ Error initializing ItemManager: ${error.message}`, 'error');
    }

    // Options Code management variables
    let userModifiedOptionsCode = false;
    let lastCalculatedOptionsCodeValue = '';

    // Copy Options Code Button
    const copyOptionsCodeBtn = document.getElementById('copyOptionsCodeBtn');
    if (copyOptionsCodeBtn) {
        copyOptionsCodeBtn.addEventListener('click', function() {
            const optionsCodeInput = document.getElementById('optionsCode');
            if (optionsCodeInput && optionsCodeInput.value) {
            navigator.clipboard.writeText(optionsCodeInput.value).then(function() {
                addToConsole(`📋 Options Code copied: ${optionsCodeInput.value}`, 'success');

                // Show toast notification
                showSuccessToast(`📋 Options Code คัดลอกแล้ว: ${optionsCodeInput.value}`);

                // Show temporary success feedback
                const btn = document.getElementById('copyOptionsCodeBtn');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.className = 'btn btn-success btn-sm';

                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.className = 'btn btn-outline-success btn-sm';
                }, 1000);
            }).catch(function(err) {
                addToConsole(`❌ Failed to copy Options Code: ${err}`, 'error');
                showErrorToast(`❌ ไม่สามารถคัดลอก Options Code ได้: ${err}`);
            });
            } else {
                addToConsole('⚠️ No Options Code to copy', 'warning');
                showWarningToast('⚠️ ไม่มี Options Code ให้คัดลอก');
            }
        });
    } else {
        console.warn('⚠️ copyOptionsCodeBtn element not found');
    }

    // Apply Options Code Button
    const applyOptionsCodeBtn = document.getElementById('applyOptionsCodeBtn');
    if (applyOptionsCodeBtn) {
        applyOptionsCodeBtn.addEventListener('click', function() {
            const optionsCodeInput = document.getElementById('optionsCode');
            if (!optionsCodeInput) {
                console.warn('⚠️ optionsCode input not found');
                return;
            }
            const currentValue = optionsCodeInput.value.trim();

        if (currentValue && !isNaN(currentValue)) {
            addToConsole(`🎯 Applying Options Code: ${currentValue}`, 'info');

            // Force apply the Options Code
            userModifiedOptionsCode = true;
            resetSlotsAndCraftOptions();
            updateFinalResultsFromOptionsCode(currentValue);
            updateOptionsCodeStatus('success', `Applied: ${currentValue}`);

            // Add visual feedback to the button
            const btn = document.getElementById('applyOptionsCodeBtn');
            const originalHTML = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check-circle"></i>';
            btn.className = 'btn btn-success btn-sm';

            setTimeout(() => {
                btn.innerHTML = originalHTML;
                btn.className = 'btn btn-outline-primary btn-sm';
            }, 2000);

            addToConsole(`✅ Options Code ${currentValue} applied and slots reset!`, 'success');
            showSuccessToast(`✅ Options Code ${currentValue} ถูกนำไปใช้และรีเซ็ต slots แล้ว!`);
        } else {
            addToConsole('❌ Please enter a valid numeric Options Code', 'error');
            updateOptionsCodeStatus('error', 'Invalid numeric value');
            showErrorToast('❌ กรุณากรอก Options Code ที่เป็นตัวเลขที่ถูกต้อง');

            // Add error feedback
            optionsCodeInput.style.borderColor = '#dc3545';
            optionsCodeInput.style.backgroundColor = '#f8d7da';
            setTimeout(() => {
                optionsCodeInput.style.borderColor = '';
                optionsCodeInput.style.backgroundColor = '';
            }, 2000);
        }
        });
    } else {
        console.warn('⚠️ applyOptionsCodeBtn element not found');
    }

    // Clear Options Code Button
    const clearOptionsCodeBtn = document.getElementById('clearOptionsCodeBtn');
    if (clearOptionsCodeBtn) {
        clearOptionsCodeBtn.addEventListener('click', function() {
            const optionsCodeInput = document.getElementById('optionsCode');
            if (optionsCodeInput) {
                optionsCodeInput.value = '';
                userModifiedOptionsCode = false;
                lastCalculatedOptionsCodeValue = '';
                updateOptionsCodeStatus('info', 'Options Code cleared');
                addToConsole('🗑️ Options Code cleared', 'info');
            }
        });
    } else {
        console.warn('⚠️ clearOptionsCodeBtn element not found');
    }

    // Options Code Input Change Detection
    const optionsCodeInput = document.getElementById('optionsCode');
    if (optionsCodeInput) {
        optionsCodeInput.addEventListener('input', function() {
            const currentValue = this.value;
            if (currentValue !== lastCalculatedOptionsCodeValue && currentValue !== '') {
            userModifiedOptionsCode = true;
            updateOptionsCodeStatus('warning', 'Manually modified');
            addToConsole(`✏️ Options Code manually modified: ${currentValue}`, 'info');

            // Reset all slots and craft options when user manually enters Options Code
            resetSlotsAndCraftOptions();

            // Update Final Results with the manually entered Options Code
            updateFinalResultsFromOptionsCode(currentValue);

        } else if (currentValue === '') {
            userModifiedOptionsCode = false;
            updateOptionsCodeStatus('info', 'Real-time options calculation');
        }
        });
    } else {
        console.warn('⚠️ optionsCode input element not found');
    }

    // Options Code Focus Event
    if (optionsCodeInput) {
        optionsCodeInput.addEventListener('focus', function() {
            addToConsole('📝 Options Code field focused - manual editing mode', 'info');
        });
    }

    // Options Code Enter Key Event
    if (optionsCodeInput) {
        optionsCodeInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const currentValue = this.value.trim();
            if (currentValue && !isNaN(currentValue)) {
                addToConsole(`⚡ Enter pressed - Processing Options Code: ${currentValue}`, 'info');

                // Force reset and update
                userModifiedOptionsCode = true;
                resetSlotsAndCraftOptions();
                updateFinalResultsFromOptionsCode(currentValue);
                updateOptionsCodeStatus('success', `Applied: ${currentValue}`);

                // Add visual confirmation
                this.style.borderColor = '#28a745';
                this.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    this.style.borderColor = '';
                    this.style.backgroundColor = '';
                }, 2000);

                addToConsole(`✅ Options Code ${currentValue} applied successfully!`, 'success');
            } else {
                addToConsole('❌ Please enter a valid numeric Options Code', 'error');
                updateOptionsCodeStatus('error', 'Invalid numeric value');
            }
        }
        });
    }

    // Update Options Code status function
    function updateOptionsCodeStatus(type, message) {
        const statusElement = document.getElementById('optionsCodeStatus');
        const colors = {
            info: 'text-muted',
            success: 'text-success',
            warning: 'text-warning',
            error: 'text-danger'
        };
        statusElement.className = colors[type] || 'text-muted';
        statusElement.textContent = message;
    }

    // Check if any slots are actively being used
    function checkIfSlotsAreActive() {
        const slot1 = document.getElementById('slot1')?.value || 'NOT';
        const slot2 = document.getElementById('slot2')?.value || 'NOT';
        const slot3 = document.getElementById('slot3')?.value || 'NOT';
        const craftOption = document.getElementById('craftOption')?.value || 'NOT';
        const craftHeight = parseInt(document.getElementById('craftHeight')?.value || '0');

        // Check if any slot is not 'NOT' or craft height is not 0
        const hasActiveSlots = slot1 !== 'NOT' || slot2 !== 'NOT' || slot3 !== 'NOT' ||
            craftOption !== 'NOT' || craftHeight > 0;

        if (hasActiveSlots) {
            addToConsole(
                `🔍 Active slots detected: Slot1=${slot1}, Slot2=${slot2}, Slot3=${slot3}, Craft=${craftOption}(${craftHeight})`,
                'info');
        }

        return hasActiveSlots;
    }

    // Update Options Code with real-time calculation
    function updateOptionsCodeRealTime(optionsCodeValue) {
        const optionsCodeInput = document.getElementById('optionsCode');
        const currentValue = optionsCodeInput.value;

        // Check if any slots are being used (not NOT or 0)
        const hasActiveSlots = checkIfSlotsAreActive();

        // If slots are active, force update Options Code from calculation
        if (hasActiveSlots) {
            const wasManuallyModified = userModifiedOptionsCode;

            optionsCodeInput.value = optionsCodeValue;
            lastCalculatedOptionsCodeValue = optionsCodeValue;
            userModifiedOptionsCode = false; // Reset manual flag when slots are used

            if (wasManuallyModified) {
                updateOptionsCodeStatus('success',
                    `Switched to Slots: ${optionsCodeValue} (Now using slot calculation)`);
                addToConsole(`🔄 Switched from manual to slot-based Options Code: ${optionsCodeValue}`,
                    'success');
                addToConsole(`💡 Options Code now calculated from active slots instead of manual entry`,
                'info');
            } else {
                updateOptionsCodeStatus('success',
                    `From Slots: ${optionsCodeValue} (Calculated from active slots)`);
                addToConsole(`🎯 Options Code updated from slots: ${optionsCodeValue}`, 'success');
            }

            // Add visual feedback for slot-based calculation
            optionsCodeInput.style.borderColor = '#007bff';
            optionsCodeInput.style.backgroundColor = '#e3f2fd';
            setTimeout(() => {
                optionsCodeInput.style.borderColor = '';
                optionsCodeInput.style.backgroundColor = '';
            }, 1500);

            return; // Exit early when using slot calculation
        }

        // Only update if user hasn't manually modified it or if it's empty
        if (!userModifiedOptionsCode || currentValue === '' || currentValue ===
            lastCalculatedOptionsCodeValue) {
            optionsCodeInput.value = optionsCodeValue;
            lastCalculatedOptionsCodeValue = optionsCodeValue;
            userModifiedOptionsCode = false;

            // Update status and visual feedback
            if (optionsCodeValue > 0) {
                updateOptionsCodeStatus('success', `Real-time: ${optionsCodeValue} (Options detected!)`);
                addToConsole(`✨ Options Code updated: ${optionsCodeValue}`, 'success');

                // Add visual feedback
                optionsCodeInput.style.borderColor = '#28a745';
                optionsCodeInput.style.backgroundColor = '#f8fff9';
                setTimeout(() => {
                    optionsCodeInput.style.borderColor = '';
                    optionsCodeInput.style.backgroundColor = '';
                }, 1500);
            } else {
                updateOptionsCodeStatus('info', 'Real-time: 0 (No options)');
                addToConsole(`📊 Options Code: ${optionsCodeValue} (No options)`, 'info');
            }
        } else {
            addToConsole(`⚠️ Options Code manually modified (${currentValue}), skipping auto-update`,
            'warning');
            updateOptionsCodeStatus('warning', `Manual: ${currentValue} (Auto-update skipped)`);
        }
    }

    // Reset all slots and craft options to default values
    function resetSlotsAndCraftOptions() {
        addToConsole('🔄 Resetting slots and craft options due to manual Options Code entry...', 'info');

        // Store old values for logging
        const oldValues = {
            slot1: document.getElementById('slot1').value,
            slot2: document.getElementById('slot2').value,
            slot3: document.getElementById('slot3').value,
            craftOption: document.getElementById('craftOption').value,
            craftHeight: document.getElementById('craftHeight').value
        };

        // Reset all slots to NOT
        document.getElementById('slot1').value = 'NOT';
        document.getElementById('slot2').value = 'NOT';
        document.getElementById('slot3').value = 'NOT';

        // Reset craft options
        document.getElementById('craftOption').value = 'NOT';
        document.getElementById('craftHeight').value = '0';

        // Log what was changed
        Object.keys(oldValues).forEach(key => {
            if (oldValues[key] !== 'NOT' && oldValues[key] !== '0') {
                addToConsole(`   📝 ${key}: ${oldValues[key]} → NOT/0`, 'warning');
            }
        });

        // Add visual feedback to show what was reset
        const elementsToHighlight = ['slot1', 'slot2', 'slot3', 'craftOption', 'craftHeight'];
        elementsToHighlight.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.borderColor = '#ffc107';
                element.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                    element.style.borderColor = '';
                    element.style.backgroundColor = '';
                }, 2000);
            }
        });

        addToConsole('✅ All slots and craft options reset to default values', 'warning');
        addToConsole('💡 Manual Options Code will be used as Final Results', 'info');
    }

    // Update Final Results from manually entered Options Code
    function updateFinalResultsFromOptionsCode(optionsCodeValue) {
        // Check if Final Results input exists (from previous implementation)
        const finalResultsInput = document.getElementById('finalResults');
        if (finalResultsInput) {
            finalResultsInput.value = optionsCodeValue;
            addToConsole(`📝 Final Results updated from Options Code: ${optionsCodeValue}`, 'success');

            // Add visual feedback to Final Results
            finalResultsInput.style.borderColor = '#17a2b8';
            finalResultsInput.style.backgroundColor = '#d1ecf1';
            setTimeout(() => {
                finalResultsInput.style.borderColor = '';
                finalResultsInput.style.backgroundColor = '';
            }, 2000);
        } else {
            // If Final Results doesn't exist, show in results display
            const resultsDiv = document.getElementById('resultsDisplay');
            if (resultsDiv) {
                resultsDiv.className = 'alert alert-info';
                // Get Item Code from input
                let itemId = document.getElementById('itemId')?.value || '';
                let itemCodeDisplay = (itemId && !isNaN(itemId) && Number(itemId) > 0) ? itemId :
                    '<span class="text-danger">-</span>';
                let optionsCodeDisplay = (optionsCodeValue && !isNaN(optionsCodeValue)) ? optionsCodeValue :
                    '<span class="text-danger">-</span>';
                let optionsHex = (optionsCodeValue && !isNaN(optionsCodeValue)) ? parseInt(optionsCodeValue)
                    .toString(16).toUpperCase() : '-';
                resultsDiv.innerHTML = `
                            <h6><i class="fas fa-edit mr-2"></i>Manual Options Code Entry</h6>
                            <div class="alert alert-warning mb-3">
                                <strong>Item Code:</strong> <span class="badge badge-primary">${itemCodeDisplay}</span>
                                <strong class="ml-3">Options Code:</strong> <span class="badge badge-warning">${optionsCodeDisplay}</span>
                                <small class="text-muted ml-2">Manually entered - slots reset</small>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white py-2">
                                            <h6 class="mb-0">Final Results</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Item Code:</strong> <span class="badge badge-primary">${itemCodeDisplay}</span></p>
                                            <p class="mb-1"><strong>Options Code:</strong> <span class="badge badge-info">${optionsCodeDisplay}</span></p>
                                            <p class="mb-0"><strong>Hex Value:</strong> <span class="badge badge-secondary">${optionsHex}</span></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-info-circle mr-2"></i>Note</h6>
                                        <p class="mb-0">Slots and craft options have been reset to default values. The manually entered Options Code will be used as Final Results.</p>
                                    </div>
                                </div>
                            </div>
                        `;
            }
        }
    }
    // Dec ⇄ Hex Converter
    document.getElementById('converterBtn').addEventListener('click', function() {
        addToConsole('🔄 Opening Dec ⇄ Hex Converter...', 'info');
        showConverter();
    });


    document.getElementById('clearConsoleBtn').addEventListener('click', function() {
        debugConsole.innerHTML = '';
        addToConsole('Console cleared', 'info');
    });

    // Test Toast Button
    const testToastBtn = document.getElementById('testToastBtn');
    if (testToastBtn) {
        testToastBtn.addEventListener('click', function() {
            // Show different types of toasts with icons
            showSuccessToast('✅ นี่คือ Success Toast!');

            setTimeout(() => {
                showInfoToast('ℹ️ นี่คือ Info Toast!');
            }, 500);

            setTimeout(() => {
                showWarningToast('⚠️ นี่คือ Warning Toast!');
            }, 1000);

            setTimeout(() => {
                showErrorToast('❌ นี่คือ Error Toast (ไม่หายอัตโนมัติ)');
            }, 1500);

            // Show SmartAdmin style toast with logo
            setTimeout(() => {
                showSmartAdminToast('🎉 นี่คือ SmartAdmin Toast พร้อมโลโก้!', 'success', 4000);
            }, 2000);

            addToConsole('🔔 Toast notifications displayed (with icons and logo)', 'info');
        });
    }

    // Clear Toasts Button
    const clearToastsBtn = document.getElementById('clearToastsBtn');
    if (clearToastsBtn) {
        clearToastsBtn.addEventListener('click', function() {
            clearAllToasts();
            addToConsole('🧹 All toasts cleared', 'info');
            showInfoToast('🧹 ล้าง Toast ทั้งหมดแล้ว', 2000);
        });
    }

    // Test SmartAdmin Toast Button
    const testSmartAdminToastBtn = document.getElementById('testSmartAdminToastBtn');
    if (testSmartAdminToastBtn) {
        testSmartAdminToastBtn.addEventListener('click', function() {
            // Show SmartAdmin style toasts with logo
            showSmartAdminToast('Welcome to SmartAdmin! This is a success notification.', 'success', 5000);

            setTimeout(() => {
                showSmartAdminToast('This is an informational message from SmartAdmin.', 'info', 4000);
            }, 1000);

            setTimeout(() => {
                showSmartAdminToast('Warning: Please check your settings.', 'warning', 6000);
            }, 2000);

            addToConsole('🌟 SmartAdmin style toasts displayed', 'info');
        });
    }

    // Load Item Table
    document.getElementById('loadItemTableBtn').addEventListener('click', function() {
        addToConsole('🔄 Loading item database table...', 'info');
        loadItemDatabaseForTable();
    });

    // Refresh Table
    document.getElementById('refreshTableBtn').addEventListener('click', function() {
        addToConsole('🔄 Refreshing item table...', 'info');
        loadItemDatabaseForTable();
    });

    // Calculate from form
    document.getElementById('calculateFromFormBtn').addEventListener('click', function() {
        addToConsole('🧪 Calculating from form data...', 'info');

        const itemSearch = document.getElementById('itemSearch').value;
        const itemType = document.getElementById('itemType').value;
        const bindingType = document.getElementById('binding').value;
        const duration = parseInt(document.getElementById('duration').value);

        const formData = {
            itemName: itemSearch,
            itemType: itemType,
            itemId: parseInt(document.getElementById('itemId').value),
            upgrade: parseInt(document.getElementById('upgrade').value),
            extreme: parseInt(document.getElementById('extreme').value),
            divine: parseInt(document.getElementById('divine').value),
            slot1: document.getElementById('slot1').value,
            slot2: document.getElementById('slot2').value,
            slot3: document.getElementById('slot3').value,
            craftOption: document.getElementById('craftOption').value,
            craftHeight: parseInt(document.getElementById('craftHeight').value),
            binding: bindingType,
            duration: duration
        };

        addToConsole(`🔍 Selected item type: ${itemType}`, 'info');

        if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
            const result = window.itemManager.generateCompleteItem(formData);

            // Calculate binding code if needed
            let bindingCode = 0;
            let finalItemCode = result.itemCode;

            if (bindingType !== 'none' && window.itemManager.calculateBindingCode) {
                bindingCode = window.itemManager.calculateBindingCode(bindingType);
                finalItemCode = result.itemCode + bindingCode;
                addToConsole(`🔗 Binding applied: ${bindingType} (+${bindingCode})`, 'info');
            }

            // Display results
            const resultsDiv = document.getElementById('resultsDisplay');
            if (result.success) {
                resultsDiv.className = 'alert alert-success';
                resultsDiv.innerHTML = `
                            <h6><i class="fas fa-check-circle mr-2"></i>Complete Item Calculation</h6>
                            <div class="alert alert-info mb-3">
                                <strong>Item:</strong> ${formData.itemName}
                                <span class="badge badge-info ml-2">${formData.itemType}</span>
                                <span class="badge badge-secondary ml-1">ID: ${formData.itemId}</span>
                            </div>

                            <!-- Calculation Steps -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-calculator mr-2"></i>Calculation Steps</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Step 1: Base Item Code</h6>
                                            <p class="mb-1"><strong>Item ID:</strong> ${formData.itemId}</p>
                                            <p class="mb-1"><strong>Upgrade:</strong> +${formData.upgrade}</p>
                                            <p class="mb-1"><strong>Extreme:</strong> ${formData.extreme}</p>
                                            <p class="mb-1"><strong>Divine:</strong> ${formData.divine}</p>
                                            <p class="mb-3"><strong>Result:</strong> <span class="badge badge-primary">${result.baseItemCode}</span></p>

                                            <h6 class="text-success">Step 2: Add Binding</h6>
                                            <p class="mb-1"><strong>Binding Type:</strong> ${result.binding.toUpperCase()}</p>
                                            <p class="mb-1"><strong>Binding Code:</strong> +${result.bindingCode}</p>
                                            <p class="mb-3"><strong>Final Item Code:</strong> <span class="badge badge-success">${result.itemCode}</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-warning">Step 3: Options Code (Separate)</h6>
                                            <p class="mb-1"><strong>Item Type:</strong> ${formData.itemType}</p>
                                            <p class="mb-1"><strong>Slot 1:</strong> ${formData.slot1}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot1?.hex || '0'}, Dec: ${result.slotDetails?.slot1?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Slot 2:</strong> ${formData.slot2}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot2?.hex || '0'}, Dec: ${result.slotDetails?.slot2?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Slot 3:</strong> ${formData.slot3}
                                                <small class="text-secondary">(Hex: ${result.slotDetails?.slot3?.hex || '0'}, Dec: ${result.slotDetails?.slot3?.decimal || '0'})</small>
                                            </p>
                                            <p class="mb-1"><strong>Craft Option:</strong> ${formData.craftOption} (Height: ${formData.craftHeight})
                                                <small class="text-secondary">(Craft Code: ${result.slotDetails?.craft?.hex || '00'})</small>
                                            </p>
                                            <p class="mb-3"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>

                                            <h6 class="text-info">Additional Info</h6>
                                            <p class="mb-1"><strong>Duration:</strong> ${result.duration} days</p>
                                            <p class="mb-0"><strong>Calculation Order:</strong> Item Code + Binding → Options Code</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Final Results -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary py-2">
                                            <h6 class="mb-0">Final Decimal Values</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                            <p class="mb-1 text-dark"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                                            <small class="text-secondary">Use these values in your game system</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success py-2">
                                            <h6 class="mb-0">Final Hexadecimal Values</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                                            <p class="mb-1 text-dark"><strong>Options Code:</strong> <span class="badge badge-info">${result.hexOptionsCode}</span></p>
                                            <small class="text-secondary">Hex representation for debugging</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                addToConsole(
                    `✅ Complete calculation: FinalItemCode=${finalItemCode}, OptionsCode=${result.optionsCode}, Binding=${bindingType}`,
                    'success');
            } else {
                resultsDiv.className = 'alert alert-danger';
                resultsDiv.innerHTML = `
                            <h6><i class="fas fa-exclamation-triangle mr-2"></i>Calculation Failed</h6>
                            <p>Error: ${result.error}</p>
                        `;
                addToConsole(`❌ Calculation failed: ${result.error}`, 'error');
            }
        } else {
            addToConsole('❌ ItemManager or generateCompleteItem not available', 'error');
        }
    });

    // Clear saved data
    document.getElementById('clearSavedDataBtn').addEventListener('click', function() {
        if (confirm(
            'Are you sure you want to clear all saved form data? This action cannot be undone.')) {
            clearSavedData();
            addToConsole('🗑️ All saved form data has been cleared', 'warning');
        }
    });

    // Reset form
    document.getElementById('resetFormBtn').addEventListener('click', function() {
        document.getElementById('itemSearch').value = 'Sample Battlehelm(Lv.50)';
        document.getElementById('itemId').value = '';
        document.getElementById('itemType').value = 'Helm';
        document.getElementById('upgrade').value = '15';
        document.getElementById('extreme').value = '7';
        document.getElementById('divine').value = '10';
        document.getElementById('binding').value = 'char';
        document.getElementById('duration').value = '31';
        document.getElementById('slot1').value = 'MP';
        document.getElementById('slot2').value = 'DEF';
        document.getElementById('slot3').value = 'NOT';
        document.getElementById('craftOption').value = 'NOT';
        document.getElementById('craftHeight').value = '0';

        // Update slot options for the reset item
        if (typeof updateSlotOptions === 'function') {
            updateSlotOptions();
        }

        const resultsDiv = document.getElementById('resultsDisplay');
        resultsDiv.className = 'alert alert-info';
        resultsDiv.innerHTML = `
                    <h6><i class="fas fa-info-circle mr-2"></i>Form Reset Complete</h6>
                    <p class="mb-0">All fields have been reset to default values:</p>
                    <small class="text-secondary">
                        Item: Sample Battlehelm(Lv.50) (Helm)<br>
                        Item ID: 1000, Upgrade: +15, Extreme: 7, Divine: 10<br>
                        Binding: Character, Duration: 31 days<br>
                        Slots: MP, DEF, NOT, Craft: NOT (Height: 0)
                    </small>
                `;

        addToConsole('🔄 Form reset to default values with binding settings', 'info');
    });

    // Validate Item ID Button
    document.getElementById('validateItemBtn').addEventListener('click', function() {
        addToConsole('🔍 Validating Item ID...', 'info');
        validateItemId();
    });



    // Validate Player Button
    document.getElementById('validatePlayerBtn').addEventListener('click', function() {
        addToConsole('👤 Validating player from database...', 'info');
        validatePlayerFromDatabase();
    });

    // Preview Item Button
    document.getElementById('previewItemBtn').addEventListener('click', function() {
        addToConsole('👁️ Previewing item...', 'info');
        previewItem();
    });

    // Send Item Button
    document.getElementById('sendItemBtn').addEventListener('click', function() {
        addToConsole('📤 Send Item button clicked...', 'info');
        sendItem();
    });

    // Item History Button
    document.getElementById('itemHistoryBtn').addEventListener('click', function() {
        addToConsole('📜 Opening send history...', 'info');
        showItemHistory();
    });

    // Load Player Database Button
    document.getElementById('loadPlayerDbBtn').addEventListener('click', function() {
        addToConsole('👥 Loading player database...', 'info');
        loadPlayerDatabaseManually();
    });

    // Show Player List Button
    document.getElementById('showPlayerListBtn').addEventListener('click', function() {
        addToConsole('📋 Opening player list...', 'info');

        // Test SweetAlert first
        if (typeof Swal === 'undefined') {
            console.error('SweetAlert is not available');
            addToConsole('❌ SweetAlert library is not loaded. Please refresh the page.', 'error');
            alert('SweetAlert library is not loaded. Please refresh the page.');
            return;
        }

        try {
            showPlayerList();
        } catch (error) {
            console.error('Error in showPlayerList:', error);
            addToConsole('❌ Error opening player list: ' + error.message, 'error');

            // Fallback alert
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to open player list: ' + error.message,
                confirmButtonColor: '#dc3545'
            });
        }
    });

    // Global variable to store item data
    let itemDatabase = [];
    let itemsDataTable = null;

    // LocalStorage key for saving form data
    const STORAGE_KEY = 'cabal_item_editor_data';

    // Real-time calculation state
    let isRealTimeEnabled = true;
    let realTimeTimeout = null;

    // Binding codes (compatible with manage-item.php)
    const bindingCodes = {
        none: 0,
        id: 4096, // bind-id
        char: 524288, // bind-char
        equ: 1572864 // bind-equ
    };

    // Hex/Dec conversion functions (compatible with manage-item.php)
    function hexToDec(hexValue) {
        if (!hexValue) return 0;
        return parseInt(hexValue, 16);
    }

    function decToHex(decValue) {
        if (!decValue) return '';
        return parseInt(decValue).toString(16).toUpperCase();
    }

    // Handle input change (similar to manage-item.php)
    function handleInputChange() {
        const itemCode = parseFloat(document.getElementById('itemId')?.value) || 0;
        const bindId = bindingCodes.id || 0;
        const bindChar = bindingCodes.char || 0;
        const bindEqu = bindingCodes.equ || 0;

        // Calculate binding codes
        const codeBid = itemCode + bindId;
        const codeBchar = itemCode + bindChar;
        const codeBequ = itemCode + bindEqu;

        console.log('Binding calculations:', {
            itemCode,
            codeBid,
            codeBchar,
            codeBequ
        });

        // Update debug info if needed
        addToConsole(`🔢 Binding calculations: ID=${codeBid}, Char=${codeBchar}, Equ=${codeBequ}`, 'info');
    }

    // Save form data to localStorage
    function saveFormData() {
        try {
            const formData = {
                itemSearch: document.getElementById('itemSearch')?.value || '',
                itemId: document.getElementById('itemId')?.value || '',
                itemType: document.getElementById('itemType')?.value || 'NOT',
                upgrade: document.getElementById('upgrade')?.value || '0',
                extreme: document.getElementById('extreme')?.value || '0',
                divine: document.getElementById('divine')?.value || '0',
                binding: document.getElementById('binding')?.value || 'none',
                duration: document.getElementById('duration')?.value || '31',
                slot1: document.getElementById('slot1')?.value || 'NOT',
                slot2: document.getElementById('slot2')?.value || 'NOT',
                slot3: document.getElementById('slot3')?.value || 'NOT',
                craftOption: document.getElementById('craftOption')?.value || 'NOT',
                craftHeight: document.getElementById('craftHeight')?.value || '0',
                playerUsername: document.getElementById('playerUsername')?.value || '',
                itemQuantity: document.getElementById('itemQuantity')?.value || '1',
                sendMethod: document.getElementById('sendMethod')?.value || 'inventory',
                sendNotification: document.getElementById('sendNotification')?.checked || true,
                logTransaction: document.getElementById('logTransaction')?.checked || false,
                optionsCode: document.getElementById('optionsCode')?.value || '',

                timestamp: new Date().toISOString()
            };

            localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
            console.log('💾 Form data saved to localStorage');
        } catch (error) {
            console.error('❌ Error saving form data:', error);
        }
    }

    // Load form data from localStorage
    function loadFormData() {
        try {
            const savedData = localStorage.getItem(STORAGE_KEY);
            if (!savedData) {
                console.log('📝 No saved form data found');
                return false;
            }

            const formData = JSON.parse(savedData);
            console.log('📂 Loading saved form data:', formData);

            // Restore form values
            if (document.getElementById('itemSearch')) document.getElementById('itemSearch').value = formData
                .itemSearch || '';
            if (document.getElementById('itemId')) document.getElementById('itemId').value = formData.itemId ||
                '';
            if (document.getElementById('itemType')) document.getElementById('itemType').value = formData
                .itemType || 'NOT';
            if (document.getElementById('upgrade')) document.getElementById('upgrade').value = formData
                .upgrade || '0';
            if (document.getElementById('extreme')) document.getElementById('extreme').value = formData
                .extreme || '0';
            if (document.getElementById('divine')) document.getElementById('divine').value = formData.divine ||
                '0';
            if (document.getElementById('binding')) document.getElementById('binding').value = formData
                .binding || 'none';
            if (document.getElementById('duration')) document.getElementById('duration').value = formData
                .duration || '31';
            if (document.getElementById('slot1')) document.getElementById('slot1').value = formData.slot1 ||
                'NOT';
            if (document.getElementById('slot2')) document.getElementById('slot2').value = formData.slot2 ||
                'NOT';
            if (document.getElementById('slot3')) document.getElementById('slot3').value = formData.slot3 ||
                'NOT';
            if (document.getElementById('craftOption')) document.getElementById('craftOption').value = formData
                .craftOption || 'NOT';
            if (document.getElementById('craftHeight')) document.getElementById('craftHeight').value = formData
                .craftHeight || '0';

            // Restore new fields
            if (document.getElementById('playerUsername')) document.getElementById('playerUsername').value =
                formData.playerUsername || '';
            if (document.getElementById('itemQuantity')) document.getElementById('itemQuantity').value =
                formData.itemQuantity || '1';
            if (document.getElementById('sendMethod')) document.getElementById('sendMethod').value = formData
                .sendMethod || 'inventory';
            if (document.getElementById('sendNotification')) document.getElementById('sendNotification')
                .checked = formData.sendNotification !== undefined ? formData.sendNotification : true;
            if (document.getElementById('logTransaction')) document.getElementById('logTransaction').checked =
                formData.logTransaction !== undefined ? formData.logTransaction : false;

            // Restore Options Code
            if (document.getElementById('optionsCode')) {
                document.getElementById('optionsCode').value = formData.optionsCode || '';
                if (formData.optionsCode) {
                    lastCalculatedOptionsCodeValue = formData.optionsCode;
                    userModifiedOptionsCode = true; // Assume it was manually set if saved
                    updateOptionsCodeStatus('info', `Restored: ${formData.optionsCode}`);
                }
            }

            addToConsole(`✅ Form data restored from ${formData.timestamp}`, 'success');
            return true;
        } catch (error) {
            console.error('❌ Error loading form data:', error);
            return false;
        }
    }

    // Clear saved form data
    function clearSavedData() {
        try {
            // Remove from localStorage
            localStorage.removeItem(STORAGE_KEY);

            // Reset form to default values
            document.getElementById('itemSearch').value = '';
            document.getElementById('itemId').value = '';
            document.getElementById('itemType').value = 'NOT';
            document.getElementById('upgrade').value = '0';
            document.getElementById('extreme').value = '0';
            document.getElementById('divine').value = '0';
            document.getElementById('binding').value = 'none';
            document.getElementById('duration').value = '31';
            document.getElementById('slot1').value = 'NOT';
            document.getElementById('slot2').value = 'NOT';
            document.getElementById('slot3').value = 'NOT';
            document.getElementById('craftOption').value = 'NOT';
            document.getElementById('craftHeight').value = '0';

            // Reset new fields
            document.getElementById('playerUsername').value = '';
            document.getElementById('itemQuantity').value = '1';
            document.getElementById('sendMethod').value = 'inventory';
            document.getElementById('sendNotification').checked = true;
            document.getElementById('logTransaction').checked = false;

            // Reset Options Code
            document.getElementById('optionsCode').value = '';
            userModifiedOptionsCode = false;
            lastCalculatedOptionsCodeValue = '';
            updateOptionsCodeStatus('info', 'Real-time options calculation');

            // Reset status displays
            updateItemIdStatus('info', 'Enter item ID to validate');
            updatePlayerStatus('info', 'Enter username to validate');
            disableSendButton();

            // Update slot options for the reset item type
            if (typeof updateSlotOptions === 'function') {
                updateSlotOptions();
            }

            // Clear results display
            const resultsDiv = document.getElementById('resultsDisplay');
            resultsDiv.className = 'alert alert-warning';
            resultsDiv.innerHTML = `
                        <h6><i class="fas fa-trash mr-2"></i>Saved Data Cleared</h6>
                        <p class="mb-0">All saved form data has been cleared and form reset to defaults.</p>
                        <small class="text-secondary">
                            Form reset to: Empty item, Weapon type, No upgrades, No binding, 31 days duration<br>
                            Player fields cleared, Send method: Inventory, Quantity: 1
                        </small>
                    `;

            addToConsole('🗑️ Saved form data cleared and form reset', 'warning');
        } catch (error) {
            console.error('❌ Error clearing saved data:', error);
            addToConsole(`❌ Error clearing saved data: ${error.message}`, 'error');
        }
    }

    // Auto-save form data when inputs change
    function setupAutoSave() {
        const formInputs = [
            'itemSearch', 'itemId', 'itemType', 'upgrade',
            'extreme', 'divine', 'binding', 'duration',
            'slot1', 'slot2', 'slot3', 'craftOption', 'craftHeight',
            'playerUsername', 'itemQuantity', 'sendMethod', 'optionsCode'
        ];

        const checkboxInputs = [
            'sendNotification', 'logTransaction', 'requireConfirmation'
        ];

        formInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('change', saveFormData);
                element.addEventListener('input', debounce(saveFormData,
                1000)); // Save after 1 second of no typing
            }
        });

        checkboxInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('change', saveFormData);
            }
        });

        console.log('🔄 Auto-save setup complete');
    }

    // Setup real-time calculation listeners
    function setupRealTimeCalculation() {
        const formInputs = [
            'itemSearch', 'itemId', 'itemType', 'upgrade',
            'extreme', 'divine', 'binding', 'duration',
            'slot1', 'slot2', 'slot3', 'craftOption', 'craftHeight'
        ];

        formInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                // Special handling for slot-related elements
                if (['slot1', 'slot2', 'slot3', 'craftOption', 'craftHeight'].includes(inputId)) {
                    element.addEventListener('change', function() {
                        // When slots change, reset manual Options Code flag
                        if (this.value !== 'NOT' && this.value !== '0') {
                            addToConsole(
                                `🎯 ${inputId} changed to ${this.value} - switching to slot-based calculation`,
                                'info');
                            userModifiedOptionsCode = false; // Allow auto-update from slots
                            updateOptionsCodeStatus('info',
                                'Switching to slot-based calculation...');
                        }
                        performRealTimeCalculation();
                    });

                    element.addEventListener('input', function() {
                        // For input events on slots, also reset manual flag
                        if (this.value !== 'NOT' && this.value !== '0') {
                            userModifiedOptionsCode = false;
                        }
                        performRealTimeCalculation();
                    });
                } else {
                    // Normal handling for non-slot elements
                    element.addEventListener('change', performRealTimeCalculation);
                    element.addEventListener('input', performRealTimeCalculation);
                }
            }
        });

        // Setup real-time toggle
        const realTimeCheckbox = document.getElementById('realTimeCalculation');
        if (realTimeCheckbox) {
            realTimeCheckbox.addEventListener('change', function() {
                isRealTimeEnabled = this.checked;
                if (isRealTimeEnabled) {
                    addToConsole('✅ Real-time calculation enabled', 'success');
                    performRealTimeCalculation(); // Trigger immediate calculation
                } else {
                    addToConsole('⏸️ Real-time calculation disabled', 'warning');
                    // Clear timeout
                    if (realTimeTimeout) {
                        clearTimeout(realTimeTimeout);
                    }
                }
            });
        }

        console.log('🔄 Real-time calculation setup complete');
    }

    // Setup Hex/Dec converter (compatible with manage-item.php)
    function setupHexDecConverter() {
        const hextodecInput = document.getElementById('hextodec');
        const deccodeOutput = document.getElementById('deccode');
        const dectohexInput = document.getElementById('dectohex');
        const hexcodeOutput = document.getElementById('hexcode');

        if (hextodecInput && deccodeOutput) {
            hextodecInput.addEventListener('input', function() {
                const hexValue = this.value.trim();
                if (hexValue) {
                    try {
                        const decValue = parseInt(hexValue, 16);
                        deccodeOutput.value = decValue;
                        addToConsole(`🔄 Hex to Dec: ${hexValue} → ${decValue}`, 'info');
                    } catch (error) {
                        deccodeOutput.value = '';
                        addToConsole(`❌ Invalid hex value: ${hexValue}`, 'error');
                    }
                } else {
                    deccodeOutput.value = '';
                }
            });
        }

        if (dectohexInput && hexcodeOutput) {
            dectohexInput.addEventListener('input', function() {
                const decValue = this.value.trim();
                if (decValue && !isNaN(decValue)) {
                    try {
                        const hexValue = parseInt(decValue).toString(16).toUpperCase();
                        hexcodeOutput.value = hexValue;
                        addToConsole(`🔄 Dec to Hex: ${decValue} → ${hexValue}`, 'info');
                    } catch (error) {
                        hexcodeOutput.value = '';
                        addToConsole(`❌ Invalid decimal value: ${decValue}`, 'error');
                    }
                } else {
                    hexcodeOutput.value = '';
                }
            });
        }

        console.log('🔄 Hex/Dec converter setup complete');
    }

    // Setup Binding calculator (compatible with manage-item.php)
    function setupBindingCalculator() {
        const inputItem = document.getElementById('input_Item');
        const bindIdOutput = document.getElementById('codeoutput_bid');
        const bindCharOutput = document.getElementById('codeoutput_bchar');
        const bindEquOutput = document.getElementById('codeoutput_bequ');

        if (inputItem && bindIdOutput && bindCharOutput && bindEquOutput) {
            inputItem.addEventListener('input', function() {
                const itemCode = parseFloat(this.value) || 0;

                // Calculate binding codes (same as manage-item.php)
                const codeBid = itemCode + bindingCodes.id; // +4096
                const codeBchar = itemCode + bindingCodes.char; // +524288
                const codeBequ = itemCode + bindingCodes.equ; // +1572864

                // Update outputs
                bindIdOutput.value = codeBid.toFixed(0);
                bindCharOutput.value = codeBchar.toFixed(0);
                bindEquOutput.value = codeBequ.toFixed(0);

                // Update itemId if it exists (sync with main form)
                const itemIdField = document.getElementById('itemId');
                if (itemIdField && itemCode > 0 && itemIdField.value != itemCode) {
                    itemIdField.value = itemCode;

                    // Try to find and update item name if database is loaded
                    if (itemDatabase && itemDatabase.length > 0) {
                        const foundItem = itemDatabase.find(item => item.id === itemCode);
                        const searchInput = document.getElementById('itemSearch');
                        if (foundItem && searchInput) {
                            searchInput.value = foundItem.name;
                            addToConsole(`✅ Found item: ${foundItem.name} (ID: ${itemCode})`,
                            'success');
                        } else if (searchInput && itemCode > 0) {
                            // Clear search if item not found but keep the ID
                            if (foundItem === undefined) {
                                searchInput.value = '';
                                addToConsole(`⚠️ Item ID ${itemCode} not found in database`, 'warning');
                            }
                        }
                    }
                }

                addToConsole(`🔗 Binding codes: ID=${codeBid}, Char=${codeBchar}, Equ=${codeBequ}`,
                    'info');

                // Trigger real-time calculation if enabled
                if (isRealTimeEnabled) {
                    performRealTimeCalculation();
                }
            });
        }

        console.log('🔄 Binding calculator setup complete');
    }

    // Debounce function to limit auto-save frequency
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Real-time calculation function
    function performRealTimeCalculation() {
        if (!isRealTimeEnabled) return;

        // Clear previous timeout
        if (realTimeTimeout) {
            clearTimeout(realTimeTimeout);
        }

        // Set new timeout for debounced calculation
        realTimeTimeout = setTimeout(() => {
            try {
                addToConsole('🔄 Real-time calculation triggered...', 'info');

                // Get current form data
                const itemSearch = document.getElementById('itemSearch')?.value || '';
                const itemType = document.getElementById('itemType')?.value || 'NOT';
                const bindingType = document.getElementById('binding')?.value || 'none';
                const duration = parseInt(document.getElementById('duration')?.value || '31');

                const formData = {
                    itemName: itemSearch,
                    itemType: itemType,
                    itemId: parseInt(document.getElementById('itemId')?.value || '0'),
                    upgrade: parseInt(document.getElementById('upgrade')?.value || '0'),
                    extreme: parseInt(document.getElementById('extreme')?.value || '0'),
                    divine: parseInt(document.getElementById('divine')?.value || '0'),
                    slot1: document.getElementById('slot1')?.value || 'NOT',
                    slot2: document.getElementById('slot2')?.value || 'NOT',
                    slot3: document.getElementById('slot3')?.value || 'NOT',
                    craftOption: document.getElementById('craftOption')?.value || 'NOT',
                    craftHeight: parseInt(document.getElementById('craftHeight')?.value || '0'),
                    binding: bindingType,
                    duration: duration
                };

                // Perform calculation
                if (window.itemManager && typeof window.itemManager.generateCompleteItem ===
                    'function') {
                    const result = window.itemManager.generateCompleteItem(formData);

                    // Update results display
                    updateRealTimeResults(result, formData);
                } else {
                    addToConsole('❌ ItemManager not available for real-time calculation', 'error');
                }
            } catch (error) {
                addToConsole(`❌ Real-time calculation error: ${error.message}`, 'error');
                console.error('Real-time calculation error:', error);
            }
        }, 500); // 500ms delay for debouncing
    }

    // Update real-time results display
    function updateRealTimeResults(result, formData) {
        console.log('🔍 updateRealTimeResults called with:', result);
        console.log('🔍 slotDetails:', result.slotDetails);

        if (!result.success) {
            const resultsDiv = document.getElementById('resultsDisplay');
            resultsDiv.className = 'alert alert-danger';
            resultsDiv.innerHTML = `
                        <h6><i class="fas fa-exclamation-triangle mr-2"></i>Real-time Calculation Error</h6>
                        <p>${result.error}</p>
                    `;
            return;
        }

        // Update Options Code input with real-time calculation
        updateOptionsCodeRealTime(result.optionsCode);

        // Display results with real-time indicator
        const resultsDiv = document.getElementById('resultsDisplay');
        resultsDiv.className = 'alert alert-success';
        resultsDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6><i class="fas fa-sync mr-2"></i>Real-time Item Calculation</h6>
                        <small class="text-secondary"><i class="fas fa-clock mr-1"></i>Updated: ${new Date().toLocaleTimeString()}</small>
                    </div>

                    <div class="alert alert-info mb-3">
                        <strong>Item:</strong> ${formData.itemName}
                        <span class="badge badge-info ml-2">${formData.itemType}</span>
                        <span class="badge badge-secondary ml-1">ID: ${formData.itemId}</span>
                    </div>

                    <!-- Quick Results -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white py-2">
                                    <h6 class="mb-0">Final Results</h6>
                                </div>
                                <div class="card-body py-2 bg-light">
                                    <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                    <p class="mb-0 text-dark"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white py-2">
                                    <h6 class="mb-0">Hex Values</h6>
                                </div>
                                <div class="card-body py-2 bg-light">
                                    <p class="mb-1 text-dark"><strong>Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                                    <p class="mb-0 text-dark"><strong>Options Code:</strong> <span class="badge badge-info">${result.hexOptionsCode}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slot Details -->
                    <div class="row mb-2">
                        <div class="col-md-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white py-1">
                                    <h6 class="mb-0"><small>Slot Details</small></h6>
                                </div>
                                <div class="card-body py-2 bg-light">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 1:</strong> ${formData.slot1}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot1?.hex || '0'}, Dec: ${result.slotDetails?.slot1?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 2:</strong> ${formData.slot2}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot2?.hex || '0'}, Dec: ${result.slotDetails?.slot2?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Slot 3:</strong> ${formData.slot3}<br>
                                            <span class="text-secondary">Hex: ${result.slotDetails?.slot3?.hex || '0'}, Dec: ${result.slotDetails?.slot3?.decimal || '0'}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-dark"><strong>Craft:</strong> ${formData.craftOption} (H:${formData.craftHeight})<br>
                                            <span class="text-secondary">Code: ${result.slotDetails?.craft?.hex || '00'}</span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calculation Summary -->
                    <div class="row">
                        <div class="col-md-12">
                            <small class="text-secondary">
                                <strong>Calculation:</strong>
                                Base: ${result.baseItemCode} +
                                Binding: ${result.bindingCode} (${result.binding.toUpperCase()}) =
                                <strong class="text-dark">${result.itemCode}</strong> |
                                Options: <strong class="text-dark">${result.optionsCode}</strong>
                            </small>
                        </div>
                    </div>
                `;

        addToConsole(`✅ Real-time calculation complete: Item=${result.itemCode}, Options=${result.optionsCode}`,
            'success');
    }

    // Load item data from item_fixed.dec
    async function loadItemDatabase() {
        try {
            addToConsole('📥 Loading item database...', 'info');

            // Try multiple possible paths (prioritize the correct path)
            const possiblePaths = [
                'files/game_systems/import/item_fixed.dec', // Primary path from root
                '../files/game_systems/import/item_fixed.dec',
                './files/game_systems/import/item_fixed.dec',
                'game_systems/import/item_fixed.dec',
                './game_systems/import/item_fixed.dec',
                './import/item_fixed.dec',
                'import/item_fixed.dec'
            ];

            let response = null;
            let usedPath = '';

            for (const path of possiblePaths) {
                try {
                    addToConsole(`🔍 Trying path: ${path}`, 'info');
                    response = await fetch(path);
                    addToConsole(`📡 Response status: ${response.status} for ${path}`, 'info');
                    if (response.ok) {
                        usedPath = path;
                        addToConsole(`✅ Found file at: ${path}`, 'success');
                        break;
                    } else {
                        addToConsole(`❌ Failed with status ${response.status}: ${path}`, 'warning');
                    }
                } catch (e) {
                    addToConsole(`❌ Exception for ${path}: ${e.message}`, 'warning');
                }
            }

            if (!response || !response.ok) {
                throw new Error(`Could not find item_fixed.dec in any of the expected locations`);
            }

            const text = await response.text();
            addToConsole(`📄 File loaded successfully (${text.length} characters)`, 'info');

            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(text, 'application/xml');

            // Check for parsing errors
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                throw new Error('XML parsing error: ' + parseError[0].textContent);
            }

            const items = xmlDoc.getElementsByTagName('msg');
            addToConsole(`🔍 Found ${items.length} items in XML`, 'info');

            itemDatabase = [];
            for (let i = 0; i < items.length; i++) {
                const id = items[i].getAttribute('id');
                const name = items[i].getAttribute('cont');

                if (id && name) {
                    const numericId = id.replace('item', '');
                    itemDatabase.push({
                        id: parseInt(numericId),
                        name: name,
                        fullId: id
                    });
                }
            }

            // Sort by ID for better organization
            itemDatabase.sort((a, b) => a.id - b.id);

            // Populate datalist
            const datalist = document.getElementById('itemSearchList');
            if (datalist) {
                datalist.innerHTML = '';
                itemDatabase.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.name;
                    option.setAttribute('data-id', item.id);
                    option.textContent = `${item.name} (ID: ${item.id})`;
                    datalist.appendChild(option);
                });
                addToConsole(`📋 Populated datalist with ${itemDatabase.length} options`, 'success');
            } else {
                addToConsole('⚠️ Datalist element not found', 'warning');
            }

            // Show sample items for verification
            if (itemDatabase.length > 0) {
                addToConsole(`📦 Sample items loaded:`, 'info');
                const samples = itemDatabase.slice(0, 5);
                samples.forEach(item => {
                    addToConsole(`   • ${item.name} (ID: ${item.id})`, 'info');
                });
            }

            addToConsole(`✅ Successfully loaded ${itemDatabase.length} items from ${usedPath}`, 'success');
            return itemDatabase;
        } catch (error) {
            addToConsole(`❌ Error loading item database: ${error.message}`, 'error');
            console.error('Full error:', error);
            return [];
        }
    }

    // Handle item search selection
    function handleItemSelection() {
        const searchInput = document.getElementById('itemSearch');
        const itemIdInput = document.getElementById('itemId');
        const selectedItemName = searchInput.value.trim();

        if (!selectedItemName) {
            addToConsole('⚠️ No item name entered', 'warning');
            return;
        }

        addToConsole(`🔍 Searching for item: "${selectedItemName}"`, 'info');

        // Find exact match first
        let selectedItem = itemDatabase.find(item => item.name === selectedItemName);

        // If no exact match, try partial match
        if (!selectedItem) {
            selectedItem = itemDatabase.find(item =>
                item.name.toLowerCase().includes(selectedItemName.toLowerCase())
            );
        }

        if (selectedItem) {
            itemIdInput.value = selectedItem.id;

            // Update the input_Item field for binding calculator
            const inputItemField = document.getElementById('input_Item');
            if (inputItemField) {
                inputItemField.value = selectedItem.id;
                // Trigger input event to update binding calculations
                inputItemField.dispatchEvent(new Event('input', {
                    bubbles: true
                }));
            }

            addToConsole(`✅ Selected item: ${selectedItem.name} (ID: ${selectedItem.id})`, 'success');
            addToConsole(`🔗 Updated binding calculator with item ID: ${selectedItem.id}`, 'info');

            // Update search input to exact name if it was a partial match
            if (searchInput.value !== selectedItem.name) {
                searchInput.value = selectedItem.name;
            }

            // Trigger real-time calculation if enabled
            if (typeof performRealTimeCalculation === 'function' && isRealTimeEnabled) {
                performRealTimeCalculation();
            }
        } else {
            addToConsole(`❌ Item not found: "${selectedItemName}"`, 'error');
            addToConsole(`💡 Try searching with partial name or check spelling`, 'info');

            // Show some suggestions
            const suggestions = itemDatabase
                .filter(item => item.name.toLowerCase().includes(selectedItemName.toLowerCase().substring(0,
                    3)))
                .slice(0, 5)
                .map(item => item.name);

            if (suggestions.length > 0) {
                addToConsole(`💡 Suggestions: ${suggestions.join(', ')}`, 'info');
            }
        }
    }

    // Handle item ID input (when user types ID directly)
    function handleItemIdInput(itemId) {
        if (!itemDatabase || itemDatabase.length === 0) {
            addToConsole('⚠️ Item database not loaded. Please load database first.', 'warning');
            updateItemIdStatus('warning', 'Database not loaded');
            return;
        }

        addToConsole(`🔍 Looking up item with ID: ${itemId}`, 'info');

        // Find item by ID
        const foundItem = itemDatabase.find(item => item.id === itemId);

        if (foundItem) {
            // Update item search field with the found item name
            const searchInput = document.getElementById('itemSearch');
            if (searchInput) {
                searchInput.value = foundItem.name;
            }

            // Update the input_Item field for binding calculator
            const inputItemField = document.getElementById('input_Item');
            if (inputItemField) {
                inputItemField.value = itemId;
                // Trigger input event to update binding calculations
                inputItemField.dispatchEvent(new Event('input', {
                    bubbles: true
                }));
            }

            addToConsole(`✅ Found item: ${foundItem.name} (ID: ${itemId})`, 'success');
            addToConsole(`🔗 Updated binding calculator with item ID: ${itemId}`, 'info');
            updateItemIdStatus('success', `Valid: ${foundItem.name}`);

            // Highlight the item in DataTable if it exists
            if (itemsDataTable) {
                // Remove previous highlights
                $('#itemsTable tbody tr').removeClass('table-success');

                // Find and highlight the row
                itemsDataTable.rows().every(function() {
                    const data = this.data();
                    if (data && data.id === itemId) {
                        $(this.node()).addClass('table-success');
                        return false; // Break the loop
                    }
                });
            }

            // Enable send button
            enableSendButton();

            // Trigger real-time calculation if enabled
            if (typeof performRealTimeCalculation === 'function' && isRealTimeEnabled) {
                performRealTimeCalculation();
            }
        } else {
            addToConsole(`❌ Item with ID ${itemId} not found in database`, 'error');
            updateItemIdStatus('error', 'ID not found in database');

            // Clear item search field since ID doesn't exist
            const searchInput = document.getElementById('itemSearch');
            if (searchInput) {
                searchInput.value = '';
            }

            // Still update input_Item for binding calculator (user might want to use custom ID)
            const inputItemField = document.getElementById('input_Item');
            if (inputItemField) {
                inputItemField.value = itemId;
                // Trigger input event to update binding calculations
                inputItemField.dispatchEvent(new Event('input', {
                    bubbles: true
                }));
            }

            addToConsole(`🔗 Updated binding calculator with custom ID: ${itemId}`, 'info');
            addToConsole(`💡 Note: This ID is not in the database, but you can still use it for calculations`,
                'warning');

            // Enable send button (allow custom IDs)
            enableSendButton();

            // Trigger real-time calculation if enabled
            if (typeof performRealTimeCalculation === 'function' && isRealTimeEnabled) {
                performRealTimeCalculation();
            }
        }
    }



    // Global variable to store player database
    let playerDatabase = [];

    // Load player database from server
    async function loadPlayerDatabase() {
        try {
            addToConsole('📥 Loading account database...', 'info');

            // Try multiple possible paths for account database
            const possiblePaths = [
                '_data/account_data.json',
                '../_data/account_data.json',
                './_data/account_data.json'
            ];

            let response = null;
            let usedPath = '';

            for (const path of possiblePaths) {
                try {
                    addToConsole(`🔍 Trying account database path: ${path}`, 'info');
                    response = await fetch(path);
                    addToConsole(`📡 Response status: ${response.status} for ${path}`, 'info');
                    if (response.ok) {
                        usedPath = path;
                        addToConsole(`✅ Found account database at: ${path}`, 'success');
                        break;
                    } else {
                        addToConsole(`❌ Failed with status ${response.status}: ${path}`, 'warning');
                    }
                } catch (e) {
                    addToConsole(`❌ Exception for ${path}: ${e.message}`, 'warning');
                }
            }

            if (!response || !response.ok) {
                // If no database file found, create mock data
                addToConsole('⚠️ Account database file not found, using mock data', 'warning');
                playerDatabase = generateMockPlayerData();
                addToConsole(`✅ Generated ${playerDatabase.length} mock accounts`, 'success');
                return playerDatabase;
            }

            const data = await response.json();
            addToConsole(
                `📄 Account database loaded successfully (${JSON.stringify(data).length} characters)`,
                'info');

            // Process account data - convert from account_data.json format to player format
            let accountData = [];
            if (Array.isArray(data)) {
                accountData = data;
            } else if (data.data && Array.isArray(data.data)) {
                accountData = data.data;
            } else {
                throw new Error('Invalid account database format');
            }

            // Convert account data to player format
            playerDatabase = accountData.map(account => {
                const lastLogin = account.LoginTime ? new Date(account.LoginTime) : null;
                const createDate = account.createDate ? new Date(account.createDate) : null;
                const playTimeHours = Math.floor((account.PlayTime || 0) /
                3600); // Convert seconds to hours

                return {
                    id: account.UserNum,
                    username: account.ID,
                    online: account.Login === 1,
                    lastLogin: lastLogin ? lastLogin.toISOString() : null,
                    guild: null, // Not available in account data
                    honor: Math.floor((account.PlayTime || 0) /
                    10), // Estimate honor from play time
                    nation: Math.random() > 0.5 ? 'Capella' : 'Procyon', // Random nation
                    email: account.Email,
                    playTime: account.PlayTime || 0,
                    lastIp: account.LastIp ? account.LastIp.trim() : null,
                    createDate: createDate ? createDate.toISOString() : null,
                    authType: account.AuthType
                };
            });

            // Sort by username for better organization
            playerDatabase.sort((a, b) => a.username.localeCompare(b.username));

            addToConsole(`✅ Successfully loaded ${playerDatabase.length} accounts from ${usedPath}`,
                'success');
            addToConsole(`📊 Account data converted to player format`, 'info');
            return playerDatabase;
        } catch (error) {
            addToConsole(`❌ Error loading account database: ${error.message}`, 'error');
            console.error('Full error:', error);

            // Fallback to mock data
            addToConsole('🔄 Falling back to mock account data', 'warning');
            playerDatabase = generateMockPlayerData();
            addToConsole(`✅ Generated ${playerDatabase.length} mock accounts`, 'success');
            return playerDatabase;
        }
    }



    // Generate player statistics
    function generatePlayerStats(players) {
        if (!players || players.length === 0) {
            return {
                onlineCount: 0,
                averagePlayTime: 0,
                maxPlayTime: 0
            };
        }

        const stats = {
            onlineCount: players.filter(p => p.online).length,
            averagePlayTime: Math.round(players.reduce((sum, p) => sum + (p.playTime || 0), 0) / players
                .length / 3600), // Convert to hours
            maxPlayTime: Math.round(Math.max(...players.map(p => p.playTime || 0)) /
                3600) // Convert to hours
        };

        return stats;
    }

    // Generate mock player data for testing
    function generateMockPlayerData() {
        const mockPlayers = [];
        const prefixes = ['Dark', 'Light', 'Fire', 'Ice', 'Storm', 'Shadow', 'Golden', 'Silver', 'Blood',
            'Divine'
        ];
        const suffixes = ['Knight', 'Master', 'Lord', 'King', 'Queen', 'Hunter', 'Slayer', 'Blade', 'Mage',
            'Archer'
        ];

        // Generate 100 mock players
        for (let i = 1; i <= 100; i++) {
            const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
            const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
            const username = `${prefix}${suffix}${i.toString().padStart(2, '0')}`;

            mockPlayers.push({
                id: i,
                username: username,
                online: Math.random() > 0.3,
                lastLogin: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
                guild: Math.random() > 0.5 ? `Guild${Math.floor(Math.random() * 20) + 1}` : null,
                honor: Math.floor(Math.random() * 10000),
                nation: ['Capella', 'Procyon'][Math.floor(Math.random() * 2)]
            });
        }
        return mockPlayers;
    }

    // Validate Player from Database function
    function validatePlayerFromDatabase() {
        const usernameInput = document.getElementById('playerUsername');
        const username = usernameInput.value.trim();

        if (!username) {
            updatePlayerStatus('error', 'Username required');
            addToConsole('❌ Please enter a player username', 'error');
            disableSendButton();
            return false;
        }

        if (username.length < 3) {
            updatePlayerStatus('error', 'Username too short');
            addToConsole('❌ Username must be at least 3 characters', 'error');
            disableSendButton();
            return false;
        }

        if (username.length > 20) {
            updatePlayerStatus('error', 'Username too long');
            addToConsole('❌ Username cannot exceed 20 characters', 'error');
            disableSendButton();
            return false;
        }

        // Check if account database is loaded
        if (!playerDatabase || playerDatabase.length === 0) {
            addToConsole('⚠️ Account database not loaded. Loading now...', 'warning');
            updatePlayerStatus('info', 'Loading database...');

            loadPlayerDatabase().then(() => {
                // Retry validation after loading database
                validatePlayerFromDatabase();
            });
            return;
        }

        addToConsole(`🔍 Searching for account: ${username} in database...`, 'info');
        updatePlayerStatus('info', 'Searching...');

        // Search for account in database (case-insensitive)
        const foundPlayer = playerDatabase.find(player =>
            player.username.toLowerCase() === username.toLowerCase()
        );

        if (foundPlayer) {
            updatePlayerStatus('success', `Found: ${foundPlayer.username}`);
            addToConsole(`✅ Account found in database: ${foundPlayer.username}`, 'success');

            // Update username field with exact case from database
            usernameInput.value = foundPlayer.username;

            // Show detailed player info
            addToConsole(`📊 Account Details:`, 'info');
            addToConsole(`   • Username: ${foundPlayer.username}`, 'info');
            addToConsole(`   • Status: ${foundPlayer.online ? 'Online' : 'Offline'}`, 'info');
            addToConsole(`   • Email: ${foundPlayer.email}`, 'info');
            addToConsole(
                `   • Play Time: ${Math.floor(foundPlayer.playTime / 3600)}h ${Math.floor((foundPlayer.playTime % 3600) / 60)}m`,
                'info');
            addToConsole(`   • Honor: ${foundPlayer.honor} (estimated)`, 'info');
            addToConsole(`   • Last IP: ${foundPlayer.lastIp || 'Unknown'}`, 'info');

            if (foundPlayer.lastLogin) {
                const lastLogin = new Date(foundPlayer.lastLogin);
                addToConsole(`   • Last Login: ${lastLogin.toLocaleString()}`, 'info');
            }

            if (foundPlayer.createDate) {
                const createDate = new Date(foundPlayer.createDate);
                addToConsole(`   • Account Created: ${createDate.toLocaleString()}`, 'info');
            }

            // Show detailed popup
            // แสดงข้อมูลสั้นๆ ใน console แทน popup
            addToConsole(
                `✅ Account validated: ${foundPlayer.username} (${foundPlayer.online ? 'Online' : 'Offline'})`,
                'success');

            // แสดง toast notification แบบเบาๆ แทน
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });

            Toast.fire({
                icon: 'success',
                title: `Account Found: ${foundPlayer.username}`,
                text: `${foundPlayer.online ? 'Online' : 'Offline'} • ${Math.floor(foundPlayer.playTime / 3600)}h playtime`
            });

            enableSendButton();
            return true;
        } else {
            updatePlayerStatus('error', 'Account not found');
            addToConsole(`❌ Account "${username}" not found in database`, 'error');

            // Show suggestions for similar usernames
            const suggestions = findSimilarUsernames(username);
            if (suggestions.length > 0) {
                addToConsole(`💡 Similar usernames found:`, 'info');
                suggestions.forEach(suggestion => {
                    addToConsole(`   • ${suggestion}`, 'info');
                });

                // Show suggestions popup
                Swal.fire({
                    title: 'Account Not Found',
                    html: `
                                <div class="text-left">
                                    <p>Account "<strong>${username}</strong>" was not found in the database.</p>
                                    <hr>
                                    <h6>Similar usernames:</h6>
                                    <ul>
                                        ${suggestions.map(s => `<li><a href="#" onclick="selectSuggestedPlayer('${s}')">${s}</a></li>`).join('')}
                                    </ul>
                                    <hr>
                                    <p><small class="text-muted">Click on a username to select it, or check the spelling and try again.</small></p>
                                </div>
                            `,
                    icon: 'warning',
                    confirmButtonColor: '#ffc107',
                    confirmButtonText: 'OK',
                    width: '400px'
                });
            } else {
                Swal.fire({
                    title: 'Account Not Found',
                    text: `Account "${username}" was not found in the database. Please check the spelling and try again.`,
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            }

            disableSendButton();
            return false;
        }
    }

    // Find similar usernames for suggestions
    function findSimilarUsernames(searchUsername) {
        if (!playerDatabase || playerDatabase.length === 0) return [];

        const search = searchUsername.toLowerCase();
        const suggestions = [];

        // Find usernames that start with the search term
        const startsWith = playerDatabase
            .filter(player => player.username.toLowerCase().startsWith(search))
            .slice(0, 3)
            .map(player => player.username);

        // Find usernames that contain the search term
        const contains = playerDatabase
            .filter(player =>
                player.username.toLowerCase().includes(search) &&
                !player.username.toLowerCase().startsWith(search)
            )
            .slice(0, 3)
            .map(player => player.username);

        // Combine and limit to 5 suggestions
        return [...startsWith, ...contains].slice(0, 5);
    }

    // Global function for selecting suggested player (called from SweetAlert)
    window.selectSuggestedPlayer = function(username) {
        document.getElementById('playerUsername').value = username;
        addToConsole(`✅ Selected suggested player: ${username}`, 'success');
        Swal.close();

        // Auto-validate the selected player
        setTimeout(() => {
            validatePlayerFromDatabase();
        }, 100);
    };

    // Load Player Database Manually
    function loadPlayerDatabaseManually() {
        addToConsole('👥 Manually loading player database...', 'info');

        // Show loading indicator
        const loadBtn = document.getElementById('loadPlayerDbBtn');
        const originalHtml = loadBtn.innerHTML;
        loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        loadBtn.disabled = true;

        loadPlayerDatabase().then(database => {
            // Restore button
            loadBtn.innerHTML = originalHtml;
            loadBtn.disabled = false;

            if (database.length > 0) {
                addToConsole(`✅ Player database loaded: ${database.length} players`, 'success');

                // Populate player datalist
                populatePlayerDatalist();

                // Generate statistics
                const stats = generatePlayerStats(database);

                // แสดง toast notification แทน popup ใหญ่
                const Toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });

                Toast.fire({
                    icon: 'success',
                    title: 'Database Loaded',
                    text: `${database.length} accounts loaded (${stats.onlineCount} online)`
                });
            } else {
                addToConsole('❌ Failed to load player database', 'error');
                Swal.fire({
                    icon: 'error',
                    title: 'Database Load Failed',
                    text: 'Failed to load player database. Please try again.',
                    confirmButtonColor: '#dc3545'
                });
            }
        }).catch(error => {
            // Restore button
            loadBtn.innerHTML = originalHtml;
            loadBtn.disabled = false;

            addToConsole(`❌ Error loading player database: ${error.message}`, 'error');
        });
    }

    // Populate player datalist for autocomplete
    function populatePlayerDatalist() {
        const datalist = document.getElementById('playerSearchList');
        if (!datalist) {
            addToConsole('⚠️ Player datalist element not found', 'warning');
            return;
        }

        datalist.innerHTML = '';

        if (!playerDatabase || playerDatabase.length === 0) {
            addToConsole('⚠️ No player data to populate datalist', 'warning');
            return;
        }

        playerDatabase.forEach(player => {
            const option = document.createElement('option');
            option.value = player.username;
            option.setAttribute('data-id', player.id);
            datalist.appendChild(option);
        });

        addToConsole(`📋 Populated player datalist with ${playerDatabase.length} options`, 'success');
    }

    // Search players by partial username
    function searchPlayers(query) {
        if (!query || query.length < 2) return [];
        if (!playerDatabase || playerDatabase.length === 0) return [];

        const lowerQuery = query.toLowerCase();
        return playerDatabase
            .filter(player => player.username.toLowerCase().includes(lowerQuery))
            .slice(0, 10); // Limit to 10 results
    }

    // Handle player search selection
    function handlePlayerSelection() {
        const searchInput = document.getElementById('playerUsername');
        const selectedUsername = searchInput.value.trim();

        if (!selectedUsername) {
            addToConsole('⚠️ No username entered', 'warning');
            return;
        }

        addToConsole(`🔍 Searching for player: "${selectedUsername}"`, 'info');

        // Find exact match first
        let selectedPlayer = playerDatabase.find(player => player.username === selectedUsername);

        // If no exact match, try case-insensitive match
        if (!selectedPlayer) {
            selectedPlayer = playerDatabase.find(player =>
                player.username.toLowerCase() === selectedUsername.toLowerCase()
            );
        }

        if (selectedPlayer) {
            // Update the input with exact username from database
            searchInput.value = selectedPlayer.username;

            addToConsole(`✅ Selected player: ${selectedPlayer.username}`, 'success');
            updatePlayerStatus('success', `Selected: ${selectedPlayer.username}`);

            // Auto-validate the selected player
            setTimeout(() => {
                validatePlayerFromDatabase();
            }, 100);
        } else {
            addToConsole(`❌ Player not found: "${selectedUsername}"`, 'error');
            updatePlayerStatus('error', 'Player not found');
        }
    }

    // Show Player List function
    function showPlayerList() {
        if (!playerDatabase || playerDatabase.length === 0) {
            // Try to load the database first
            loadPlayerDatabase().then(database => {
                if (database && database.length > 0) {
                    showPlayerList(); // Retry after loading
                } else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'No Account Data',
                        text: 'Account database could not be loaded. Please check if the account_data.json file exists.',
                        confirmButtonColor: '#ffc107'
                    });
                    addToConsole('⚠️ Account database could not be loaded', 'warning');
                }
            }).catch(error => {
                console.error('Error loading database:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Database Error',
                    text: 'Failed to load account database: ' + error.message,
                    confirmButtonColor: '#dc3545'
                });
                addToConsole('❌ Failed to load account database: ' + error.message, 'error');
            });
            return;
        }

        addToConsole('📋 Generating account list...', 'info');

        try {
            // Generate statistics
            const stats = generatePlayerStats(playerDatabase);

            // Initialize with all players
            window.currentPlayerList = [...playerDatabase];
            window.currentSearchFilters = {
                search: '',
                status: 'all',
                sortBy: 'username',
                sortOrder: 'asc'
            };

            showPlayerListModal(stats);
        } catch (error) {
            console.error('Error in showPlayerList:', error);
            addToConsole('❌ Error generating player list: ' + error.message, 'error');
            Swal.fire({
                icon: 'error',
                title: 'Display Error',
                text: 'Failed to generate player list: ' + error.message,
                confirmButtonColor: '#dc3545'
            });
        }
    }

    // Show Player List Modal with search functionality
    function showPlayerListModal(stats) {
        try {
            const filteredPlayers = filterAndSortPlayers();
            const playerTable = generatePlayerTable(filteredPlayers);

            // Check if SweetAlert is available
            if (typeof Swal === 'undefined') {
                console.error('SweetAlert is not loaded');
                addToConsole('❌ SweetAlert library is not loaded', 'error');
                return;
            }

            Swal.fire({
                title: 'Account Database',
                html: `
                        <div class="text-left">
                            <!-- Search and Filter Controls -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-search mr-2"></i>Search & Filter</h6>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label small">Search Username/Email:</label>
                                            <input type="text" id="playerSearchInput" class="form-control form-control-sm" placeholder="Type to search..." onkeyup="filterPlayerList()">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">Status:</label>
                                            <select id="statusFilter" class="form-control form-control-sm" onchange="filterPlayerList()">
                                                <option value="all">All</option>
                                                <option value="online">Online</option>
                                                <option value="offline">Offline</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">Sort By:</label>
                                            <select id="sortByFilter" class="form-control form-control-sm" onchange="filterPlayerList()">
                                                <option value="username">Username</option>
                                                <option value="playTime">Play Time</option>
                                                <option value="lastLogin">Last Login</option>
                                                <option value="createDate">Created</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">Order:</label>
                                            <select id="sortOrderFilter" class="form-control form-control-sm" onchange="filterPlayerList()">
                                                <option value="desc">Descending</option>
                                                <option value="asc">Ascending</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-12">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearPlayerFilters()">
                                                <i class="fas fa-times mr-1"></i>Clear Filters
                                            </button>
                                            <span id="filterResults" class="ml-3 small text-muted">Showing ${filteredPlayers.length} of ${playerDatabase.length} accounts</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Statistics Card -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-chart-bar mr-2"></i>Account Statistics</h6>
                                </div>
                                <div class="card-body py-2" id="statisticsContainer">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <small><strong>Total:</strong> <span id="statTotal">${playerDatabase.length}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small><strong>Online:</strong> <span id="statOnline">${stats.onlineCount}</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small><strong>Avg Play Time:</strong> <span id="statAvgPlayTime">${stats.averagePlayTime}h</span></small>
                                        </div>
                                        <div class="col-md-3">
                                            <small><strong>Max Play Time:</strong> <span id="statMaxPlayTime">${stats.maxPlayTime}h</span></small>
                                        </div>
                                    </div>
                                    <div class="row mt-2" id="filteredStatsRow" style="display: none;">
                                        <div class="col-md-12">
                                            <hr class="my-2">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <small class="text-primary"><strong>Filtered:</strong> <span id="statFiltered">0</span></small>
                                                </div>
                                                <div class="col-md-3">
                                                    <small class="text-primary"><strong>Online (F):</strong> <span id="statFilteredOnline">0</span></small>
                                                </div>
                                                <div class="col-md-3">
                                                    <small class="text-primary"><strong>Avg Play Time (F):</strong> <span id="statFilteredAvgPlayTime">0h</span></small>
                                                </div>
                                                <div class="col-md-3">
                                                    <small class="text-primary"><strong>Max Play Time (F):</strong> <span id="statFilteredMaxPlayTime">0h</span></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Table -->
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-striped table-sm" id="playerListTable">
                                    <thead class="thead-dark sticky-top">
                                        <tr>
                                            <th>#</th>
                                            <th>Username</th>
                                            <th>Status</th>
                                            <th>Play Time</th>
                                            <th>Email</th>
                                            <th>Last Login</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody id="playerTableBody">
                                        ${playerTable}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Actions -->
                            <div class="mt-3 text-center">
                                <button type="button" class="btn btn-primary btn-sm" onclick="refreshPlayerList()">
                                    <i class="fas fa-sync mr-1"></i>Refresh
                                </button>
                                <button type="button" class="btn btn-info btn-sm ml-2" onclick="exportPlayerList()">
                                    <i class="fas fa-download mr-1"></i>Export All
                                </button>
                                <button type="button" class="btn btn-success btn-sm ml-2" onclick="exportFilteredPlayerList()">
                                    <i class="fas fa-filter mr-1"></i>Export Filtered
                                </button>
                                <button type="button" class="btn btn-warning btn-sm ml-2" onclick="showAdvancedSearch()">
                                    <i class="fas fa-search-plus mr-1"></i>Advanced Search
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm ml-2" onclick="showSavedSearches()">
                                    <i class="fas fa-bookmark mr-1"></i>Saved Searches
                                </button>
                            </div>

                            <hr>
                            <p><small class="text-muted">
                                <i class="fas fa-info-circle mr-1"></i>
                                Click on a username to select it for item sending.
                                Online accounts are highlighted in green.
                            </small></p>
                            <p><small class="text-muted">
                                <i class="fas fa-keyboard mr-1"></i>
                                <strong>Shortcuts:</strong> Ctrl+F (Focus search), Ctrl+Shift+F (Advanced search), Ctrl+S (Save search)
                            </small></p>
                        </div>
                    `,
                width: '1200px',
                confirmButtonText: 'Close',
                confirmButtonColor: '#6c757d',
                onOpen: () => {
                    // Add keyboard shortcuts for the modal
                    const modal = Swal.getContainer();
                    if (modal) {
                        modal.addEventListener('keydown', handlePlayerListKeyboard);
                    }

                    // Focus on search input
                    const searchInput = document.getElementById('playerSearchInput');
                    if (searchInput) {
                        searchInput.focus();
                    }
                },
                willClose: () => {
                    // Remove keyboard event listener
                    const modal = Swal.getContainer();
                    if (modal) {
                        modal.removeEventListener('keydown', handlePlayerListKeyboard);
                    }
                }
            });

            addToConsole(`📊 Account list displayed: ${playerDatabase.length} accounts`, 'success');
        } catch (error) {
            console.error('Error in showPlayerListModal:', error);
            addToConsole('❌ Error in showPlayerListModal: ' + error.message, 'error');
            Swal.fire({
                icon: 'error',
                title: 'Modal Error',
                text: 'Failed to display player list modal: ' + error.message,
                confirmButtonColor: '#dc3545'
            });
        }
    }

    // Handle keyboard shortcuts in player list modal
    function handlePlayerListKeyboard(e) {
        // Ctrl+F - Focus search input
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('playerSearchInput');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl+Shift+F - Open advanced search
        if (e.ctrlKey && e.shiftKey && e.key === 'F') {
            e.preventDefault();
            showAdvancedSearch();
        }

        // Ctrl+S - Save current search
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveCurrentSearch();
        }

        // Ctrl+R - Refresh list
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshPlayerList();
        }

        // Ctrl+E - Export filtered results
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportFilteredPlayerList();
        }

        // Escape - Clear search
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('playerSearchInput');
            if (searchInput && searchInput.value) {
                e.preventDefault();
                searchInput.value = '';
                filterPlayerList();
            }
        }
    }

    // Filter and sort players based on current filters
    function filterAndSortPlayers() {
        if (!window.currentPlayerList) {
            if (!playerDatabase || playerDatabase.length === 0) {
                return [];
            }
            window.currentPlayerList = [...playerDatabase];
        }

        let filtered = [...window.currentPlayerList];
        const filters = window.currentSearchFilters || {};

        // Apply search filter
        if (filters.search && filters.search.trim()) {
            const searchTerm = filters.search.toLowerCase().trim();
            filtered = filtered.filter(player =>
                player.username.toLowerCase().includes(searchTerm) ||
                (player.email && player.email.toLowerCase().includes(searchTerm))
            );
        }

        // Apply status filter
        if (filters.status && filters.status !== 'all') {
            filtered = filtered.filter(player => {
                if (filters.status === 'online') return player.online;
                if (filters.status === 'offline') return !player.online;
                return true;
            });
        }



        // Apply sorting
        const sortBy = filters.sortBy || 'username';
        const sortOrder = filters.sortOrder || 'asc';

        filtered.sort((a, b) => {
            let valueA, valueB;

            switch (sortBy) {
                case 'username':
                    valueA = a.username.toLowerCase();
                    valueB = b.username.toLowerCase();
                    break;
                case 'playTime':
                    valueA = a.playTime || 0;
                    valueB = b.playTime || 0;
                    break;
                case 'lastLogin':
                    valueA = a.lastLogin ? new Date(a.lastLogin).getTime() : 0;
                    valueB = b.lastLogin ? new Date(b.lastLogin).getTime() : 0;
                    break;
                case 'createDate':
                    valueA = a.createDate ? new Date(a.createDate).getTime() : 0;
                    valueB = b.createDate ? new Date(b.createDate).getTime() : 0;
                    break;
                default:
                    valueA = a.username.toLowerCase();
                    valueB = b.username.toLowerCase();
            }

            if (typeof valueA === 'string') {
                return sortOrder === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(
                valueA);
            } else {
                return sortOrder === 'asc' ? valueA - valueB : valueB - valueA;
            }
        });

        return filtered;
    }

    // Generate player table HTML
    function generatePlayerTable(players) {
        if (!players || players.length === 0) {
            return '<tr><td colspan="7" class="text-center text-muted">No accounts found</td></tr>';
        }

        try {
            return players.map((player, index) => {
                const lastLogin = player.lastLogin ? new Date(player.lastLogin).toLocaleDateString() :
                    'Never';
                const createDate = player.createDate ? new Date(player.createDate)
                .toLocaleDateString() : 'Unknown';
                const playTimeHours = Math.floor(player.playTime / 3600);
                const playTimeMinutes = Math.floor((player.playTime % 3600) / 60);

                return `
                        <tr class="${player.online ? 'table-success' : ''}">
                            <td>${index + 1}</td>
                            <td>
                                <a href="#" onclick="selectPlayerFromList('${player.username}')" class="text-decoration-none">
                                    ${player.username}
                                </a>
                            </td>
                            <td>
                                <span class="badge badge-${player.online ? 'success' : 'secondary'}">
                                    ${player.online ? 'Online' : 'Offline'}
                                </span>
                            </td>
                            <td>${playTimeHours}h ${playTimeMinutes}m</td>
                            <td><small>${player.email}</small></td>
                            <td><small>${lastLogin}</small></td>
                            <td><small>${createDate}</small></td>
                        </tr>
                    `;
            }).join('');
        } catch (error) {
            console.error('Error generating player table:', error);
            return '<tr><td colspan="7" class="text-center text-danger">Error generating table</td></tr>';
        }
    }

    // Update filtered statistics display
    function updateFilteredStatistics(filteredPlayers) {
        const filteredStatsRow = document.getElementById('filteredStatsRow');
        const statFiltered = document.getElementById('statFiltered');
        const statFilteredOnline = document.getElementById('statFilteredOnline');

        if (!filteredStatsRow || !statFiltered) return;

        // Check if any filters are active
        const filters = window.currentSearchFilters || {};
        const hasActiveFilters = filters.search ||
            (filters.status && filters.status !== 'all');

        if (hasActiveFilters && filteredPlayers.length < playerDatabase.length) {
            // Show filtered statistics
            filteredStatsRow.style.display = 'block';

            const filteredStats = generatePlayerStats(filteredPlayers);
            const statFilteredAvgPlayTime = document.getElementById('statFilteredAvgPlayTime');
            const statFilteredMaxPlayTime = document.getElementById('statFilteredMaxPlayTime');

            statFiltered.textContent = filteredPlayers.length;
            statFilteredOnline.textContent = filteredStats.onlineCount;
            if (statFilteredAvgPlayTime) statFilteredAvgPlayTime.textContent = filteredStats.averagePlayTime +
                'h';
            if (statFilteredMaxPlayTime) statFilteredMaxPlayTime.textContent = filteredStats.maxPlayTime + 'h';
        } else {
            // Hide filtered statistics
            filteredStatsRow.style.display = 'none';
        }
    }

    // Global function for filtering player list (called from SweetAlert)
    window.filterPlayerList = function() {
        // Get current filter values
        const searchInput = document.getElementById('playerSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const sortByFilter = document.getElementById('sortByFilter');
        const sortOrderFilter = document.getElementById('sortOrderFilter');

        if (!searchInput || !statusFilter || !sortByFilter || !sortOrderFilter) {
            console.warn('Filter elements not found');
            return;
        }

        // Update current filters
        window.currentSearchFilters = {
            search: searchInput.value,
            status: statusFilter.value,
            sortBy: sortByFilter.value,
            sortOrder: sortOrderFilter.value
        };

        // Apply filters and update table
        const filteredPlayers = filterAndSortPlayers();
        const newTableHTML = generatePlayerTable(filteredPlayers);

        // Update table body
        const tableBody = document.getElementById('playerTableBody');
        if (tableBody) {
            tableBody.innerHTML = newTableHTML;
        }

        // Update results count
        const filterResults = document.getElementById('filterResults');
        if (filterResults) {
            filterResults.textContent =
                `Showing ${filteredPlayers.length} of ${playerDatabase.length} accounts`;
        }

        // Update filtered statistics
        updateFilteredStatistics(filteredPlayers);

        // Log search activity
        if (window.currentSearchFilters.search) {
            addToConsole(
                `🔍 Search results: ${filteredPlayers.length} accounts found for "${window.currentSearchFilters.search}"`,
                'info');
        }
    };

    // Global function for clearing filters (called from SweetAlert)
    window.clearPlayerFilters = function() {
        // Reset filter controls
        const searchInput = document.getElementById('playerSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const sortByFilter = document.getElementById('sortByFilter');
        const sortOrderFilter = document.getElementById('sortOrderFilter');

        if (searchInput) searchInput.value = '';
        if (statusFilter) statusFilter.value = 'all';
        if (sortByFilter) sortByFilter.value = 'username';
        if (sortOrderFilter) sortOrderFilter.value = 'asc';

        // Reset filters and update table
        window.currentSearchFilters = {
            search: '',
            status: 'all',
            sortBy: 'username',
            sortOrder: 'asc'
        };

        filterPlayerList();
        addToConsole('🔄 Search filters cleared', 'info');
    };

    // Global function for selecting player from list (called from SweetAlert)
    window.selectPlayerFromList = function(username) {
        document.getElementById('playerUsername').value = username;
        addToConsole(`✅ Selected account from list: ${username}`, 'success');
        Swal.close();

        // Auto-validate the selected player
        setTimeout(() => {
            validatePlayerFromDatabase();
        }, 100);
    };

    // Global function for refreshing player list (called from SweetAlert)
    window.refreshPlayerList = function() {
        Swal.close();
        loadPlayerDatabaseManually().then(() => {
            setTimeout(() => {
                showPlayerList();
            }, 500);
        });
    };

    // Global function for exporting player list (called from SweetAlert)
    window.exportPlayerList = function() {
        try {
            if (!playerDatabase || playerDatabase.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Data',
                    text: 'No player data to export.',
                    confirmButtonColor: '#ffc107'
                });
                return;
            }

            // Convert to CSV format
            const csvHeader =
                'Username,Account ID,Status,Play Time (Hours),Email,Last Login,Created,Last IP\n';
            const csvData = playerDatabase.map(player => {
                const lastLogin = player.lastLogin ? new Date(player.lastLogin).toLocaleString() :
                    'Never';
                const createDate = player.createDate ? new Date(player.createDate)
                .toLocaleString() : 'Unknown';
                const playTimeHours = Math.floor(player.playTime / 3600);
                return `"${player.username}","${player.id}","${player.online ? 'Online' : 'Offline'}","${playTimeHours}","${player.email}","${lastLogin}","${createDate}","${player.lastIp || 'Unknown'}"`;
            }).join('\n');

            const csvContent = csvHeader + csvData;

            // Create and download file
            const blob = new Blob([csvContent], {
                type: 'text/csv;charset=utf-8;'
            });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `account_list_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            addToConsole(`📁 Exported ${playerDatabase.length} accounts to CSV`, 'success');

            Swal.fire({
                icon: 'success',
                title: 'Export Complete',
                text: `Exported ${playerDatabase.length} accounts to CSV file.`,
                timer: 2000,
                showConfirmButton: false
            });
        } catch (error) {
            addToConsole(`❌ Export failed: ${error.message}`, 'error');
            Swal.fire({
                icon: 'error',
                title: 'Export Failed',
                text: 'Failed to export player data.',
                confirmButtonColor: '#dc3545'
            });
        }
    };

    // Global function for exporting filtered player list (called from SweetAlert)
    window.exportFilteredPlayerList = function() {
        try {
            const filteredPlayers = filterAndSortPlayers();

            if (filteredPlayers.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Data',
                    text: 'No accounts match the current filters.',
                    confirmButtonColor: '#ffc107'
                });
                return;
            }

            // Convert to CSV format
            const csvHeader =
                'Username,Account ID,Status,Play Time (Hours),Email,Last Login,Created,Last IP\n';
            const csvData = filteredPlayers.map(player => {
                const lastLogin = player.lastLogin ? new Date(player.lastLogin).toLocaleString() :
                    'Never';
                const createDate = player.createDate ? new Date(player.createDate)
                .toLocaleString() : 'Unknown';
                const playTimeHours = Math.floor(player.playTime / 3600);
                return `"${player.username}","${player.id}","${player.online ? 'Online' : 'Offline'}","${playTimeHours}","${player.email}","${lastLogin}","${createDate}","${player.lastIp || 'Unknown'}"`;
            }).join('\n');

            const csvContent = csvHeader + csvData;

            // Create and download file
            const blob = new Blob([csvContent], {
                type: 'text/csv;charset=utf-8;'
            });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download',
            `filtered_accounts_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            addToConsole(`📁 Exported ${filteredPlayers.length} filtered accounts to CSV`, 'success');

            Swal.fire({
                icon: 'success',
                title: 'Export Complete',
                text: `Exported ${filteredPlayers.length} filtered accounts to CSV file.`,
                timer: 2000,
                showConfirmButton: false
            });
        } catch (error) {
            addToConsole(`❌ Export failed: ${error.message}`, 'error');
            Swal.fire({
                icon: 'error',
                title: 'Export Failed',
                text: 'Failed to export filtered account data.',
                confirmButtonColor: '#dc3545'
            });
        }
    };

    // Global function for advanced search (called from SweetAlert)
    window.showAdvancedSearch = function() {
        Swal.fire({
            title: 'Advanced Search',
            html: `
                        <div class="text-left">
                            <div class="form-group">
                                <label class="form-label">Search in multiple fields:</label>
                                <input type="text" id="advSearchText" class="form-control" placeholder="Search username, email, or ID...">
                                <small class="text-muted">Search across username, email, and account ID</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Play Time (Hours):</label>
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="number" id="playTimeMin" class="form-control" placeholder="Min" min="0">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" id="playTimeMax" class="form-control" placeholder="Max" min="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Last Login:</label>
                                        <select id="lastLoginFilter" class="form-control">
                                            <option value="">Any time</option>
                                            <option value="today">Today</option>
                                            <option value="week">This week</option>
                                            <option value="month">This month</option>
                                            <option value="never">Never logged in</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Account Created:</label>
                                        <select id="createDateFilter" class="form-control">
                                            <option value="">Any time</option>
                                            <option value="today">Today</option>
                                            <option value="week">This week</option>
                                            <option value="month">This month</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="hasEmailFilter">
                                    <label class="form-check-label" for="hasEmailFilter">
                                        Only accounts with email addresses
                                    </label>
                                </div>
                            </div>
                        </div>
                    `,
            showCancelButton: true,
            confirmButtonText: 'Apply Search',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#007bff',
            width: '600px',
            preConfirm: () => {
                return applyAdvancedSearch();
            }
        });
    };

    // Apply advanced search filters
    function applyAdvancedSearch() {
        const searchText = document.getElementById('advSearchText')?.value || '';
        const playTimeMin = parseInt(document.getElementById('playTimeMin')?.value) || null;
        const playTimeMax = parseInt(document.getElementById('playTimeMax')?.value) || null;
        const lastLoginFilter = document.getElementById('lastLoginFilter')?.value || '';
        const createDateFilter = document.getElementById('createDateFilter')?.value || '';
        const hasEmailFilter = document.getElementById('hasEmailFilter')?.checked || false;

        let filtered = [...playerDatabase];

        // Apply text search across multiple fields
        if (searchText.trim()) {
            const searchTerm = searchText.toLowerCase().trim();
            filtered = filtered.filter(player =>
                player.username.toLowerCase().includes(searchTerm) ||
                (player.email && player.email.toLowerCase().includes(searchTerm)) ||
                player.id.toString().includes(searchTerm)
            );
        }
        // Apply play time range filter
        if (playTimeMin !== null) {
            filtered = filtered.filter(player => Math.floor(player.playTime / 3600) >= playTimeMin);
        }
        if (playTimeMax !== null) {
            filtered = filtered.filter(player => Math.floor(player.playTime / 3600) <= playTimeMax);
        }

        // Apply last login filter
        if (lastLoginFilter) {
            const now = new Date();
            filtered = filtered.filter(player => {
                if (lastLoginFilter === 'never') {
                    return !player.lastLogin;
                }
                if (!player.lastLogin) return false;

                const loginDate = new Date(player.lastLogin);
                const diffTime = now - loginDate;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                switch (lastLoginFilter) {
                    case 'today':
                        return diffDays <= 1;
                    case 'week':
                        return diffDays <= 7;
                    case 'month':
                        return diffDays <= 30;
                    default:
                        return true;
                }
            });
        }

        // Apply create date filter
        if (createDateFilter) {
            const now = new Date();
            filtered = filtered.filter(player => {
                if (!player.createDate) return false;

                const createDate = new Date(player.createDate);
                const diffTime = now - createDate;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                switch (createDateFilter) {
                    case 'today':
                        return diffDays <= 1;
                    case 'week':
                        return diffDays <= 7;
                    case 'month':
                        return diffDays <= 30;
                    default:
                        return true;
                }
            });
        }

        // Apply email filter
        if (hasEmailFilter) {
            filtered = filtered.filter(player => player.email && player.email.trim());
        }

        // Update current player list and refresh display
        window.currentPlayerList = filtered;

        // Close advanced search and refresh the main list
        setTimeout(() => {
            showPlayerList();
            addToConsole(`🔍 Advanced search applied: ${filtered.length} accounts found`, 'success');
        }, 100);

        return true;
    }

    // Global function for showing saved searches (called from SweetAlert)
    window.showSavedSearches = function() {
        const savedSearches = JSON.parse(localStorage.getItem('savedPlayerSearches') || '[]');

        if (savedSearches.length === 0) {
            Swal.fire({
                icon: 'info',
                title: 'No Saved Searches',
                text: 'You haven\'t saved any searches yet. Use the "Save Current Search" option to save your filters.',
                confirmButtonColor: '#007bff'
            });
            return;
        }

        const searchList = savedSearches.map((search, index) => {
            const date = new Date(search.timestamp).toLocaleDateString();
            return `
                        <div class="card mb-2">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="mb-1">${search.name}</h6>
                                        <small class="text-muted">Saved on ${date}</small>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-info">${search.resultCount || 0} results</small>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-primary btn-sm mr-1" onclick="loadSavedSearch(${index})">
                                            <i class="fas fa-play mr-1"></i>Load
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteSavedSearch(${index})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">
                                        Filters: ${Object.entries(search.filters).filter(([key, value]) => value && value !== 'all' && value !== '').map(([key, value]) => `${key}: ${value}`).join(', ') || 'None'}
                                    </small>
                                </div>
                            </div>
                        </div>
                    `;
        }).join('');

        Swal.fire({
            title: 'Saved Searches',
            html: `
                        <div class="text-left">
                            <div class="mb-3">
                                <button type="button" class="btn btn-success btn-sm" onclick="saveCurrentSearch()">
                                    <i class="fas fa-save mr-1"></i>Save Current Search
                                </button>
                                <button type="button" class="btn btn-warning btn-sm ml-2" onclick="clearAllSavedSearches()">
                                    <i class="fas fa-trash mr-1"></i>Clear All
                                </button>
                            </div>
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${searchList}
                            </div>
                        </div>
                    `,
            width: '700px',
            confirmButtonText: 'Close',
            confirmButtonColor: '#6c757d'
        });
    };

    // Save current search filters
    window.saveCurrentSearch = function() {
        const currentFilters = window.currentSearchFilters || {};

        Swal.fire({
            title: 'Save Search',
            input: 'text',
            inputLabel: 'Search Name',
            inputPlaceholder: 'Enter a name for this search...',
            showCancelButton: true,
            confirmButtonText: 'Save',
            cancelButtonText: 'Cancel',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please enter a name for the search!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const savedSearches = JSON.parse(localStorage.getItem('savedPlayerSearches') ||
                    '[]');
                const filteredPlayers = filterAndSortPlayers();

                const newSearch = {
                    name: result.value,
                    filters: {
                        ...currentFilters
                    },
                    resultCount: filteredPlayers.length,
                    timestamp: new Date().toISOString()
                };

                savedSearches.push(newSearch);
                localStorage.setItem('savedPlayerSearches', JSON.stringify(savedSearches));

                addToConsole(
                    `💾 Search saved: "${result.value}" (${filteredPlayers.length} results)`,
                    'success');

                Swal.fire({
                    icon: 'success',
                    title: 'Search Saved!',
                    text: `Search "${result.value}" has been saved successfully.`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    };

    // Load a saved search
    window.loadSavedSearch = function(index) {
        const savedSearches = JSON.parse(localStorage.getItem('savedPlayerSearches') || '[]');
        if (index >= 0 && index < savedSearches.length) {
            const search = savedSearches[index];

            // Update current filters
            window.currentSearchFilters = {
                ...search.filters
            };

            // Close saved searches dialog and refresh main list
            Swal.close();
            setTimeout(() => {
                showPlayerList();
                addToConsole(`📂 Loaded saved search: "${search.name}"`, 'success');
            }, 100);
        }
    };

    // Delete a saved search
    window.deleteSavedSearch = function(index) {
        const savedSearches = JSON.parse(localStorage.getItem('savedPlayerSearches') || '[]');
        if (index >= 0 && index < savedSearches.length) {
            const searchName = savedSearches[index].name;

            Swal.fire({
                title: 'Delete Search?',
                text: `Are you sure you want to delete the search "${searchName}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    savedSearches.splice(index, 1);
                    localStorage.setItem('savedPlayerSearches', JSON.stringify(savedSearches));

                    addToConsole(`🗑️ Deleted saved search: "${searchName}"`, 'warning');

                    // Refresh the saved searches dialog
                    showSavedSearches();
                }
            });
        }
    };

    // Clear all saved searches
    window.clearAllSavedSearches = function() {
        Swal.fire({
            title: 'Clear All Searches?',
            text: 'This will permanently delete all saved searches. This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, clear all!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                localStorage.removeItem('savedPlayerSearches');
                addToConsole('🗑️ All saved searches cleared', 'warning');

                Swal.fire({
                    icon: 'success',
                    title: 'Searches Cleared',
                    text: 'All saved searches have been deleted.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    };

    // Legacy validate player function (for backward compatibility)
    function validatePlayer() {
        addToConsole('🔄 Redirecting to database validation...', 'info');
        validatePlayerFromDatabase();
    }

    // Validate Item ID function
    function validateItemId() {
        const itemIdInput = document.getElementById('itemId');
        const itemId = parseInt(itemIdInput.value);

        if (!itemId || itemId <= 0) {
            updateItemIdStatus('error', 'Invalid ID');
            addToConsole('❌ Please enter a valid item ID', 'error');
            disableSendButton();
            return false;
        }

        if (itemId > 999999) {
            updateItemIdStatus('error', 'ID too large');
            addToConsole('❌ Item ID cannot exceed 999999', 'error');
            disableSendButton();
            return false;
        }

        // Check if database is loaded
        if (!itemDatabase || itemDatabase.length === 0) {
            updateItemIdStatus('warning', 'Database not loaded');
            addToConsole('⚠️ Item database not loaded. ID validation limited.', 'warning');
            enableSendButton(); // Allow sending even without database
            return true;
        }

        // Find item in database
        const foundItem = itemDatabase.find(item => item.id === itemId);
        if (foundItem) {
            updateItemIdStatus('success', `Valid: ${foundItem.name}`);
            addToConsole(`✅ Item ID ${itemId} is valid: ${foundItem.name}`, 'success');
            enableSendButton();
            return true;
        } else {
            updateItemIdStatus('warning', 'Custom ID (not in database)');
            addToConsole(`⚠️ Item ID ${itemId} not found in database, but can be used as custom ID`, 'warning');
            enableSendButton(); // Allow custom IDs
            return true;
        }
    }

    // Preview Item function
    function previewItem() {
        addToConsole('👁️ Generating item preview...', 'info');

        // Get current form data
        const formData = {
            itemName: document.getElementById('itemSearch')?.value || '',
            itemType: document.getElementById('itemType')?.value || 'NOT',
            itemId: parseInt(document.getElementById('itemId')?.value || '0'),
            upgrade: parseInt(document.getElementById('upgrade')?.value || '0'),
            extreme: parseInt(document.getElementById('extreme')?.value || '0'),
            divine: parseInt(document.getElementById('divine')?.value || '0'),
            slot1: document.getElementById('slot1')?.value || 'NOT',
            slot2: document.getElementById('slot2')?.value || 'NOT',
            slot3: document.getElementById('slot3')?.value || 'NOT',
            craftOption: document.getElementById('craftOption')?.value || 'NOT',
            craftHeight: parseInt(document.getElementById('craftHeight')?.value || '0'),
            binding: document.getElementById('binding')?.value || 'none',
            duration: parseInt(document.getElementById('duration')?.value || '31'),
            quantity: parseInt(document.getElementById('itemQuantity')?.value || '1'),
            sendMethod: document.getElementById('sendMethod')?.value || 'inventory',
            manualOptionsCode: document.getElementById('optionsCode')?.value || ''
        };

        // Validate required fields
        if (!formData.itemId || formData.itemId <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Item ID',
                text: 'Please enter a valid Item ID before previewing.',
                confirmButtonColor: '#dc3545'
            });
            return;
        }

        // Calculate item codes
        if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
            const result = window.itemManager.generateCompleteItem(formData);

            if (result.success) {
                // ตรวจสอบ Options Code ที่จะใช้
                const finalOptionsCode = formData.manualOptionsCode && formData.manualOptionsCode.trim() !==
                    '' ?
                    formData.manualOptionsCode.trim() :
                    result.optionsCode;
                const isManualOptionsCode = formData.manualOptionsCode && formData.manualOptionsCode.trim() !==
                    '';
                const finalHexOptionsCode = parseInt(finalOptionsCode).toString(16).toUpperCase();

                // Show preview dialog
                Swal.fire({
                    title: 'Item Preview',
                    html: `
                                <div class="text-left">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="fas fa-box mr-2"></i>Item Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Name:</strong> ${formData.itemName || 'Custom Item'}</p>
                                                    <p><strong>ID:</strong> ${formData.itemId}</p>
                                                    <p><strong>Type:</strong> ${formData.itemType}</p>
                                                    <p><strong>Upgrade:</strong> +${formData.upgrade}</p>
                                                    <p><strong>Extreme:</strong> ${formData.extreme}</p>
                                                    <p><strong>Divine:</strong> ${formData.divine}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Binding:</strong> ${formData.binding}</p>
                                                    <p><strong>Duration:</strong> ${formData.duration} days</p>
                                                    <p><strong>Quantity:</strong> ${formData.quantity}</p>
                                                    <p><strong>Send Method:</strong> ${formData.sendMethod}</p>
                                                    <p><strong>Slot 1:</strong> ${formData.slot1}</p>
                                                    <p><strong>Slot 2:</strong> ${formData.slot2}</p>
                                                    <p><strong>Slot 3:</strong> ${formData.slot3}</p>
                                                    <p><strong>Craft:</strong> ${formData.craftOption} (${formData.craftHeight})</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card mt-2">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0"><i class="fas fa-code mr-2"></i>Final Codes (Will be sent)</h6>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                            <p><strong>Options Code:</strong>
                                                <span class="badge badge-${isManualOptionsCode ? 'warning' : 'info'}">${finalOptionsCode}</span>
                                                <small class="text-muted">(${isManualOptionsCode ? 'Manual Entry' : 'Auto Calculated'})</small>
                                            </p>
                                            <p><strong>Hex Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                                            <p><strong>Hex Options Code:</strong> <span class="badge badge-secondary">${finalHexOptionsCode}</span></p>
                                            ${isManualOptionsCode ? '<div class="alert alert-warning mt-2"><small><i class="fas fa-exclamation-triangle mr-1"></i>Using manually entered Options Code. Slots have been reset.</small></div>' : ''}
                                        </div>
                                    </div>
                                </div>
                            `,
                    icon: 'info',
                    confirmButtonColor: '#007bff',
                    confirmButtonText: '<i class="fas fa-check mr-2"></i>OK',
                    width: '600px'
                });

                addToConsole(`✅ Item preview generated: ${formData.itemName || 'Custom Item'}`, 'success');
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Preview Error',
                    text: result.error || 'Failed to generate item preview.',
                    confirmButtonColor: '#dc3545'
                });
                addToConsole(`❌ Preview failed: ${result.error}`, 'error');
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'System Error',
                text: 'ItemManager not available. Please refresh the page.',
                confirmButtonColor: '#dc3545'
            });
            addToConsole('❌ Cannot preview: ItemManager not available', 'error');
        }
    }

    // Show Item History function
    function showItemHistory() {
        addToConsole('📜 Opening send history page...', 'info');

        // Open the game systems index page which contains the send history
        window.open('?url=game_systems/index', '_blank');

        addToConsole('✅ Send history page opened in new tab', 'success');
    }



    // Update Item ID status display
    function updateItemIdStatus(type, message) {
        const statusElement = document.getElementById('itemIdStatus');
        if (!statusElement) return;

        const classes = {
            success: 'text-success',
            error: 'text-danger',
            warning: 'text-warning',
            info: 'text-info'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        statusElement.className = `small ${classes[type] || 'text-muted'}`;
        statusElement.innerHTML = `<i class="${icons[type] || 'fas fa-info-circle'} mr-1"></i>${message}`;
    }

    // Update Player status display
    function updatePlayerStatus(type, message) {
        const statusElement = document.getElementById('playerStatus');
        if (!statusElement) return;

        const classes = {
            success: 'text-success',
            error: 'text-danger',
            warning: 'text-warning',
            info: 'text-info'
        };

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        statusElement.className = `small ${classes[type] || 'text-muted'}`;
        statusElement.innerHTML = `<i class="${icons[type] || 'fas fa-info-circle'} mr-1"></i>${message}`;
    }

    // Enable send button
    function enableSendButton() {
        const sendBtn = document.getElementById('sendItemBtn');
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.classList.remove('btn-secondary');
            sendBtn.classList.add('btn-success');
        }
    }

    // Disable send button
    function disableSendButton() {
        const sendBtn = document.getElementById('sendItemBtn');
        if (sendBtn) {
            sendBtn.disabled = true;
            sendBtn.classList.remove('btn-success');
            sendBtn.classList.add('btn-secondary');
        }
    }

    // Clear Item History function (global function for SweetAlert)
    window.clearItemHistory = function() {
        Swal.fire({
            title: 'Clear History?',
            text: 'This will permanently delete all send history. This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, clear it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                try {
                    localStorage.removeItem('itemSendHistory');
                    addToConsole('🗑️ Send history cleared', 'warning');

                    Swal.fire({
                        icon: 'success',
                        title: 'History Cleared',
                        text: 'All send history has been deleted.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } catch (error) {
                    addToConsole(`❌ Failed to clear history: ${error.message}`, 'error');
                }
            }
        });
    };

    // Send item function
    function sendItem() {
        addToConsole('📤 Preparing to send item...', 'info');

        // Get current form data
        const formData = {
            itemName: document.getElementById('itemSearch')?.value || '',
            itemType: document.getElementById('itemType')?.value || 'NOT',
            itemId: parseInt(document.getElementById('itemId')?.value || '0'),
            upgrade: parseInt(document.getElementById('upgrade')?.value || '0'),
            extreme: parseInt(document.getElementById('extreme')?.value || '0'),
            divine: parseInt(document.getElementById('divine')?.value || '0'),
            slot1: document.getElementById('slot1')?.value || 'NOT',
            slot2: document.getElementById('slot2')?.value || 'NOT',
            slot3: document.getElementById('slot3')?.value || 'NOT',
            craftOption: document.getElementById('craftOption')?.value || 'NOT',
            craftHeight: parseInt(document.getElementById('craftHeight')?.value || '0'),
            binding: document.getElementById('binding')?.value || 'none',
            duration: parseInt(document.getElementById('duration')?.value || '31'),
            playerUsername: document.getElementById('playerUsername')?.value || '',
            quantity: parseInt(document.getElementById('itemQuantity')?.value || '1'),
            sendMethod: document.getElementById('sendMethod')?.value || 'inventory',
            sendNotification: document.getElementById('sendNotification')?.checked || false,
            logTransaction: document.getElementById('logTransaction')?.checked || false,
            requireConfirmation: document.getElementById('requireConfirmation')?.checked || true,
            // เพิ่ม Options Code ที่ผู้ใช้กรอกเอง
            manualOptionsCode: document.getElementById('optionsCode')?.value || ''
        };

        // Validate required fields
        if (!formData.itemId || formData.itemId <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Item ID',
                text: 'Please enter a valid Item ID before sending.',
                confirmButtonColor: '#dc3545'
            });
            addToConsole('❌ Cannot send item: Invalid Item ID', 'error');
            return;
        }

        if (!formData.playerUsername) {
            Swal.fire({
                icon: 'error',
                title: 'Player Required',
                text: 'Please enter a player username before sending.',
                confirmButtonColor: '#dc3545'
            });
            addToConsole('❌ Cannot send item: Player username required', 'error');
            return;
        }

        // Calculate item codes
        if (window.itemManager && typeof window.itemManager.generateCompleteItem === 'function') {
            const result = window.itemManager.generateCompleteItem(formData);

            if (result.success) {
                // แสดงข้อมูลที่จะส่ง
                const finalOptionsCode = formData.manualOptionsCode && formData.manualOptionsCode.trim() !==
                    '' ?
                    formData.manualOptionsCode.trim() :
                    result.optionsCode;

                addToConsole(`📊 Sending item with codes:`, 'info');
                addToConsole(`   • Item Code: ${result.itemCode}`, 'info');
                addToConsole(
                    `   • Options Code: ${finalOptionsCode} ${formData.manualOptionsCode ? '(Manual)' : '(Calculated)'}`,
                    'info');
                addToConsole(`   • Player: ${formData.playerUsername}`, 'info');
                addToConsole(`   • Quantity: ${formData.quantity}`, 'info');

                // Show confirmation dialog if required
                // ส่งไอเทมทันทีโดยไม่ต้องยืนยัน
                simulateSendItem(formData, result);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Calculation Error',
                    text: result.error || 'Failed to calculate item codes.',
                    confirmButtonColor: '#dc3545'
                });
                addToConsole(`❌ Cannot send item: ${result.error}`, 'error');
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'System Error',
                text: 'ItemManager not available. Please refresh the page.',
                confirmButtonColor: '#dc3545'
            });
            addToConsole('❌ Cannot send item: ItemManager not available', 'error');
        }
    }

    // Send item to database via PHP API
    function simulateSendItem(formData, calculationResult) {
        // แสดง loading แบบง่าย
        Swal.fire({
            title: 'กำลังส่งไอเทม...',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            onOpen: () => {
                Swal.showLoading();
            }
        });

        // Send to PHP API
        sendItemToDatabase(formData, calculationResult);
    }

    // Send item to database via PHP API
    async function sendItemToDatabase(formData, calculationResult) {
        try {
            // ตรวจสอบว่าผู้ใช้กรอก Options Code เองหรือไม่
            let finalOptionsCode = '';
            if (formData.manualOptionsCode && formData.manualOptionsCode.trim() !== '') {
                // ใช้ Options Code ที่ผู้ใช้กรอกเอง
                finalOptionsCode = formData.manualOptionsCode.trim();
                addToConsole(`📝 Using manual Options Code: ${finalOptionsCode}`, 'info');
            } else {
                // ใช้ Options Code ที่คำนวณได้
                finalOptionsCode = calculationResult.optionsCode || '';
                addToConsole(`🔄 Using calculated Options Code: ${finalOptionsCode}`, 'info');
            }

            // Prepare data for API - only send required fields
            const apiData = {
                playerUsername: formData.playerUsername,
                itemCode: calculationResult.itemCode || '',
                optionsCode: finalOptionsCode,
                quantity: formData.quantity,
                duration: formData.duration || 0,
                sendMethod: formData.sendMethod,
                adminUsername: '<?php echo $_SESSION['userLogin'] ?? 'System'; ?>' // Get from session
            };

            // ลบการแสดงผลรายละเอียดออก
            addToConsole('📤 กำลังส่งไอเทม...', 'info');

            // Send to PHP API
            const response = await fetch('files/game_systems/send_item.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(apiData)
            });

            // ลบการแสดงผลรายละเอียดออก
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                // Success response
                handleSendSuccess(result, formData, calculationResult);
            } else {
                // Error response
                handleSendError(result, formData);
            }

        } catch (error) {
            console.error('Error sending item:', error);
            handleSendError({
                error: error.message
            }, formData);
        }
    }

    // Handle successful item send
    function handleSendSuccess(result, formData, calculationResult) {
        Swal.close();


        // ตรวจสอบ Options Code ที่ส่งไป
        let sentOptionsCode = result.data.optionsCode;
        let sentItemCode = result.data.itemCode;
        const isManualOptionsCode = formData.manualOptionsCode && formData.manualOptionsCode.trim() !== '';

        // Fallback: ถ้า itemCode ไม่ถูกส่งกลับจาก API ให้ใช้ค่าจาก calculationResult หรือ formData
        if (sentItemCode === undefined || sentItemCode === null || sentItemCode === '' || sentItemCode ===
            '-') {
            if (calculationResult && calculationResult.itemCode !== undefined && calculationResult.itemCode !==
                null && calculationResult.itemCode !== '') {
                sentItemCode = calculationResult.itemCode;
            } else if (formData && formData.itemId !== undefined && formData.itemId !== null && formData
                .itemId !== '') {
                sentItemCode = formData.itemId;
            } else {
                sentItemCode = '-';
            }
        }
        // Fallback: ถ้า optionsCode ไม่ถูกส่งกลับจาก API ให้ใช้ค่าจาก calculationResult หรือ formData
        if (sentOptionsCode === undefined || sentOptionsCode === null || sentOptionsCode === '') {
            if (isManualOptionsCode) {
                sentOptionsCode = formData.manualOptionsCode;
            } else if (calculationResult && calculationResult.optionsCode !== undefined && calculationResult
                .optionsCode !== null && calculationResult.optionsCode !== '') {
                sentOptionsCode = calculationResult.optionsCode;
            } else {
                sentOptionsCode = '0';
            }
        }

        // Helper สำหรับแสดงค่า (0, "0" ให้แสดง 0, อื่น ๆ แสดงค่าจริง, ว่าง/undefined/null เป็น -)
        function displayValue(val) {
            if (val === 0 || val === '0') return '0';
            if (val === undefined || val === null || val === '') return '-';
            return val;
        }

        // แสดงแจ้งเตือนสำเร็จแบบจัดกลุ่มรายละเอียด
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            html: `
                        <p>ส่งไอเท็ม <b>${result.data.quantity}</b> ชิ้น ให้ <b>${formData.playerUsername}</b> เรียบร้อยแล้ว</p>
                        <div style="text-align:left;margin-top:1em">
                            <div style="margin-bottom:0.5em"><b>รายละเอียดรหัส:</b></div>
                            <ul style="list-style:none;padding-left:0">
                                <li><span style="display:inline-block;width:110px">Item Code:</span> <b>${displayValue(sentItemCode)}</b></li>
                                <li><span style="display:inline-block;width:110px">Options Code:</span> <b>${displayValue(sentOptionsCode)}</b> <span class="badge badge-${isManualOptionsCode ? 'info' : 'secondary'}" style="font-size:11px;vertical-align:middle">${isManualOptionsCode ? 'Manual' : 'Calculated'}</span></li>
                            </ul>
                        </div>
                    `,
            timer: 3500,
            showConfirmButton: false
        });

        // แสดงข้อความสำเร็จพร้อมรายละเอียด
        addToConsole(`✅ ส่งไอเทม ${result.data.quantity} ชิ้น ให้ ${formData.playerUsername} สำเร็จ`,
        'success');

        // Refresh notification แบบเงียบ ๆ
        if (window.notificationManager) {
            setTimeout(() => {
                window.notificationManager.loadNotifications();
            }, 1000);
        }
    }

    // Handle failed item send
    function handleSendError(result, formData) {
        Swal.close();

        // แสดงแจ้งเตือนผิดพลาดแบบง่าย
        Swal.fire({
            icon: 'error',
            title: 'ผิดพลาด!',
            text: `ไม่สามารถส่งไอเทมให้ ${formData.playerUsername} ได้`,
            timer: 3000,
            showConfirmButton: false
        });

        // แสดงข้อความผิดพลาดแบบง่าย
        addToConsole(`❌ ส่งไอเทม ${formData.quantity} ชิ้น ให้ ${formData.playerUsername} ไม่สำเร็จ`, 'error');
    }

    // Log item send action
    function logItemSend(formData, calculationResult, dbResult = null) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            action: 'ITEM_SENT',
            itemName: formData.itemName || 'Custom Item',
            itemId: formData.itemId,
            itemType: formData.itemType,
            upgrade: formData.upgrade,
            extreme: formData.extreme,
            divine: formData.divine,
            binding: formData.binding,
            duration: formData.duration,
            playerUsername: formData.playerUsername,
            quantity: formData.quantity,
            sendMethod: formData.sendMethod,
            sendNotification: formData.sendNotification,
            logTransaction: formData.logTransaction,
            itemCode: calculationResult.itemCode,
            optionsCode: calculationResult.optionsCode,
            hexItemCode: calculationResult.hexItemCode,
            hexOptionsCode: calculationResult.hexOptionsCode,
            slot1: formData.slot1,
            slot2: formData.slot2,
            slot3: formData.slot3,
            craftOption: formData.craftOption,
            craftHeight: formData.craftHeight,
            // Add database result info if available
            sendId: dbResult ? dbResult.send_id : null,
            dbStatus: dbResult ? dbResult.status : null,
            dbTimestamp: dbResult ? dbResult.timestamp : null
        };

        // Store in localStorage for history
        try {
            const history = JSON.parse(localStorage.getItem('itemSendHistory') || '[]');
            history.unshift(logEntry); // Add to beginning

            // Keep only last 100 entries (increased from 50)
            if (history.length > 100) {
                history.splice(100);
            }

            localStorage.setItem('itemSendHistory', JSON.stringify(history));
            addToConsole('📝 Item send logged to history', 'info');
        } catch (error) {
            addToConsole('⚠️ Failed to log item send to history', 'warning');
        }
    }

    // Search items by partial name
    function searchItems(query) {
        if (!query || query.length < 2) return [];

        const lowerQuery = query.toLowerCase();
        return itemDatabase
            .filter(item => item.name.toLowerCase().includes(lowerQuery))
            .slice(0, 10); // Limit to 10 results
    }

    // Initialize DataTables for items
    function initializeItemsTable() {
        if (itemsDataTable) {
            itemsDataTable.destroy();
        }

        itemsDataTable = $('#itemsTable').DataTable({
            data: itemDatabase,
            columns: [{
                    data: 'id',
                    title: 'ID',
                    width: '10%'
                },
                {
                    data: 'name',
                    title: 'Item Name',
                    width: '65%'
                },
                {
                    data: null,
                    title: 'Actions',
                    width: '25%',
                    orderable: false,
                    render: function(data, type, row) {
                        return `
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-primary select-item-btn"
                                                data-id="${row.id}"
                                                data-name="${row.name}"
                                                title="Select this item">
                                            <i class="fas fa-check"></i> Select
                                        </button>
                                        <button class="btn btn-sm btn-outline-info copy-id-btn"
                                                data-id="${row.id}"
                                                title="Copy ID to clipboard">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                `;
                    }
                }
            ],
            pageLength: 25,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            order: [
                [0, 'asc']
            ],
            responsive: true,
            language: {
                search: "",
                searchPlaceholder: "Search items...",
                lengthMenu: "Show _MENU_ items",
                info: "_START_ to _END_ of _TOTAL_ items",
                infoEmpty: "No items available",
                infoFiltered: "(filtered from _MAX_ total)",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            },
            dom: '<"row"<"col-sm-6"f><"col-sm-6 text-right"l>>rtip',
            buttons: [{
                    extend: 'pageLength',
                    className: 'btn-outline-secondary'
                },
                {
                    text: '<i class="fas fa-sync"></i> Refresh',
                    className: 'btn-outline-primary',
                    action: function(e, dt, node, config) {
                        loadItemDatabaseForTable();
                    }
                }
            ]
        });

        // Handle item selection
        $('#itemsTable tbody').off('click', '.select-item-btn');
        $('#itemsTable tbody').on('click', '.select-item-btn', function() {
            const itemId = $(this).data('id');
            const itemName = $(this).data('name');

            // Update form fields
            document.getElementById('itemId').value = itemId;
            document.getElementById('itemSearch').value = itemName;

            // Update the input_Item field for binding calculator
            const inputItemField = document.getElementById('input_Item');
            if (inputItemField) {
                inputItemField.value = itemId;
                // Trigger input event to update binding calculations
                inputItemField.dispatchEvent(new Event('input', {
                    bubbles: true
                }));
            }

            addToConsole(`✅ Selected item: ${itemName} (ID: ${itemId})`, 'success');
            addToConsole(`🔗 Updated binding calculator with item ID: ${itemId}`, 'info');

            // Trigger real-time calculation if enabled
            if (typeof performRealTimeCalculation === 'function') {
                performRealTimeCalculation();
            }

            // Highlight selected row
            $('#itemsTable tbody tr').removeClass('table-success');
            $(this).closest('tr').addClass('table-success');
        });

        // Handle copy ID to clipboard
        $('#itemsTable tbody').off('click', '.copy-id-btn');
        $('#itemsTable tbody').on('click', '.copy-id-btn', function() {
            const itemId = $(this).data('id');

            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(itemId).then(() => {
                    addToConsole(`📋 Copied ID to clipboard: ${itemId}`, 'success');

                    // Visual feedback
                    const btn = $(this);
                    const originalHtml = btn.html();
                    btn.html('<i class="fas fa-check"></i>');
                    btn.removeClass('btn-outline-info').addClass('btn-success');

                    setTimeout(() => {
                        btn.html(originalHtml);
                        btn.removeClass('btn-success').addClass('btn-outline-info');
                    }, 1000);
                }).catch(err => {
                    addToConsole(`❌ Failed to copy ID: ${err.message}`, 'error');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = itemId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                addToConsole(`📋 Copied ID to clipboard: ${itemId}`, 'success');
            }
        });

        // Custom styling for DataTables elements
        setTimeout(() => {
            // Style the search input (left side)
            $('#itemsTable_filter input').addClass('form-control form-control-sm').attr('placeholder',
                'Search items...');
            $('#itemsTable_filter label').addClass('mb-0 d-flex align-items-center');
            $('#itemsTable_filter').removeClass('text-right').addClass('text-left');

            // Style the length select (right side)
            $('#itemsTable_length select').addClass(
                'form-control form-control-sm d-inline-block w-auto');
            $('#itemsTable_length label').addClass(
            'mb-0 d-flex align-items-center justify-content-end');
            $('#itemsTable_length').addClass('text-right');

            // Style the info text
            $('#itemsTable_info').addClass('text-muted small');

            // Style pagination
            $('#itemsTable_paginate .paginate_button').addClass(
            'btn btn-sm btn-outline-secondary mx-1');
            $('#itemsTable_paginate .paginate_button.current').removeClass('btn-outline-secondary')
                .addClass('btn-primary');
        }, 100);

        addToConsole(`📊 DataTable initialized with ${itemDatabase.length} items`, 'success');
    }

    // Load item database specifically for DataTable
    async function loadItemDatabaseForTable() {
        try {
            addToConsole('📥 Loading item database for table...', 'info');

            // Show loading state
            document.getElementById('itemCountBadge').textContent = 'Loading...';
            document.getElementById('itemCountBadge').className = 'badge badge-warning';

            // Use existing loadItemDatabase function
            const database = await loadItemDatabase();

            if (database && database.length > 0) {
                // Update badge
                document.getElementById('itemCountBadge').textContent = `${database.length} items loaded`;
                document.getElementById('itemCountBadge').className = 'badge badge-success';

                // Initialize or refresh DataTable
                initializeItemsTable();

                addToConsole(`✅ Table loaded with ${database.length} items`, 'success');
            } else {
                document.getElementById('itemCountBadge').textContent = 'No items loaded';
                document.getElementById('itemCountBadge').className = 'badge badge-danger';
                addToConsole('❌ No items loaded for table', 'error');
            }
        } catch (error) {
            document.getElementById('itemCountBadge').textContent = 'Load failed';
            document.getElementById('itemCountBadge').className = 'badge badge-danger';
            addToConsole(`❌ Error loading table: ${error.message}`, 'error');
        }
    }

    // Show Dec ⇄ Hex Converter
    function showConverter() {
        const converterHTML = `
                    <div class="converter-tool">
                        <h5><i class="fas fa-exchange-alt mr-2"></i>Decimal ⇄ Hexadecimal Converter</h5>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">Decimal to Hexadecimal</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Decimal Value:</label>
                                            <input type="number" id="decInput" class="form-control" placeholder="Enter decimal number..." min="0">
                                        </div>
                                        <div class="form-group">
                                            <label>Hexadecimal Result:</label>
                                            <input type="text" id="hexOutput" class="form-control" readonly placeholder="Hex result will appear here">
                                        </div>
                                        <button type="button" id="convertDecToHex" class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right mr-1"></i>Convert Dec → Hex
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">Hexadecimal to Decimal</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>Hexadecimal Value:</label>
                                            <input type="text" id="hexInput" class="form-control" placeholder="Enter hex number (e.g., FF, 1A2B)" style="text-transform: uppercase;">
                                        </div>
                                        <div class="form-group">
                                            <label>Decimal Result:</label>
                                            <input type="text" id="decOutput" class="form-control" readonly placeholder="Decimal result will appear here">
                                        </div>
                                        <button type="button" id="convertHexToDec" class="btn btn-success btn-sm">
                                            <i class="fas fa-arrow-left mr-1"></i>Convert Hex → Dec
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Quick Reference</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Common Values:</h6>
                                                <small class="text-muted">
                                                    • 0 = 0x0<br>
                                                    • 255 = 0xFF<br>
                                                    • 256 = 0x100<br>
                                                    • 4096 = 0x1000<br>
                                                    • 65536 = 0x10000
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Binding Codes:</h6>
                                                <small class="text-muted">
                                                    • Account ID: 4096 = 0x1000<br>
                                                    • Character: 524288 = 0x80000<br>
                                                    • Bind on Equip: 1572864 = 0x180000
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div id="converterResults" class="alert alert-light" style="display: none;">
                                <!-- Conversion results will appear here -->
                            </div>
                        </div>
                    </div>
                `;

        // Update results display
        const resultsDiv = document.getElementById('resultsDisplay');
        resultsDiv.className = 'alert alert-info';
        resultsDiv.innerHTML = converterHTML;

        // Set up event listeners
        setupConverterListeners();

        addToConsole('✅ Dec ⇄ Hex Converter opened', 'success');
    }

    // Setup converter event listeners
    function setupConverterListeners() {
        // Decimal to Hex conversion
        const decInput = document.getElementById('decInput');
        const hexOutput = document.getElementById('hexOutput');
        const convertDecToHexBtn = document.getElementById('convertDecToHex');

        function convertDecToHex() {
            const decValue = parseInt(decInput.value);
            if (isNaN(decValue) || decValue < 0) {
                hexOutput.value = '';
                showConverterResult('❌ Please enter a valid positive decimal number', 'error');
                return;
            }

            const hexValue = decValue.toString(16).toUpperCase();
            hexOutput.value = hexValue;

            showConverterResult(`✅ ${decValue} (decimal) = ${hexValue} (hex) = 0x${hexValue}`, 'success');
            addToConsole(`🔄 Converted: ${decValue} → 0x${hexValue}`, 'info');
        }

        // Hex to Decimal conversion
        const hexInput = document.getElementById('hexInput');
        const decOutput = document.getElementById('decOutput');
        const convertHexToDecBtn = document.getElementById('convertHexToDec');

        function convertHexToDec() {
            let hexValue = hexInput.value.trim().toUpperCase();

            // Remove 0x prefix if present
            if (hexValue.startsWith('0X')) {
                hexValue = hexValue.substring(2);
            }

            // Validate hex input
            if (!/^[0-9A-F]+$/.test(hexValue)) {
                decOutput.value = '';
                showConverterResult('❌ Please enter a valid hexadecimal number (0-9, A-F)', 'error');
                return;
            }

            const decValue = parseInt(hexValue, 16);
            if (isNaN(decValue)) {
                decOutput.value = '';
                showConverterResult('❌ Invalid hexadecimal number', 'error');
                return;
            }

            decOutput.value = decValue;

            showConverterResult(`✅ ${hexValue} (hex) = ${decValue} (decimal) = 0x${hexValue}`, 'success');
            addToConsole(`🔄 Converted: 0x${hexValue} → ${decValue}`, 'info');
        }

        // Event listeners
        convertDecToHexBtn.addEventListener('click', convertDecToHex);
        convertHexToDecBtn.addEventListener('click', convertHexToDec);

        // Real-time conversion on input
        decInput.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                convertDecToHex();
            } else {
                hexOutput.value = '';
            }
        });

        hexInput.addEventListener('input', function() {
            // Auto uppercase
            this.value = this.value.toUpperCase();

            if (this.value.trim() !== '') {
                convertHexToDec();
            } else {
                decOutput.value = '';
            }
        });

        // Enter key support
        decInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                convertDecToHex();
            }
        });

        hexInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                convertHexToDec();
            }
        });
    }

    // Show converter result
    function showConverterResult(message, type) {
        const resultsDiv = document.getElementById('converterResults');
        if (resultsDiv) {
            resultsDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'}`;
            resultsDiv.innerHTML = message;
            resultsDiv.style.display = 'block';

            // Auto hide after 3 seconds
            setTimeout(() => {
                resultsDiv.style.display = 'none';
            }, 3000);
        }
    }

    // Update slot options based on item type
    function updateSlotOptions() {
        const itemType = document.getElementById('itemType').value;

        // Special handling for "NOT" item type
        if (itemType === 'NOT') {
            ['slot1', 'slot2', 'slot3', 'craftOption'].forEach(slotId => {
                const select = document.getElementById(slotId);

                // For "NOT" item type, only show "NOT" option and disable the select
                select.innerHTML = '<option value="NOT">NOT</option>';
                select.value = 'NOT';
                select.disabled = true;
                select.style.backgroundColor = '#f8f9fa';
                select.style.color = '#6c757d';
            });

            // Also handle Craft Height for "NOT" item type
            const craftHeightInput = document.getElementById('craftHeight');
            if (craftHeightInput) {
                craftHeightInput.value = '0';
                craftHeightInput.disabled = true;
                craftHeightInput.style.backgroundColor = '#f8f9fa';
                craftHeightInput.style.color = '#6c757d';
            }

            addToConsole('🔒 Item Type "NOT" selected - All slots and craft height locked', 'warning');
            return;
        }

        // For other item types, enable selects and populate options
        ['slot1', 'slot2', 'slot3', 'craftOption'].forEach(slotId => {
            const select = document.getElementById(slotId);
            select.disabled = false;
            select.style.backgroundColor = '';
            select.style.color = '';
        });

        // Also re-enable Craft Height for other item types
        const craftHeightInput = document.getElementById('craftHeight');
        if (craftHeightInput) {
            craftHeightInput.disabled = false;
            craftHeightInput.style.backgroundColor = '';
            craftHeightInput.style.color = '';
        }

        if (window.itemManager && typeof window.itemManager.getSlotOptions === 'function') {
            const slotOptions = window.itemManager.getSlotOptions(itemType);

            // Update all slot dropdowns including craft option
            ['slot1', 'slot2', 'slot3', 'craftOption'].forEach(slotId => {
                const select = document.getElementById(slotId);
                const currentValue = select.value;

                // Clear existing options except NOT
                select.innerHTML = '<option value="NOT">NOT</option>';

                // Add new options
                slotOptions.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    select.appendChild(optionElement);
                });

                // Restore previous value if it exists in new options
                if (slotOptions.includes(currentValue)) {
                    select.value = currentValue;
                }
            });

            addToConsole(`🔄 Updated slot options for ${itemType} (${slotOptions.length} options)`, 'info');
        }
    }

    // Initialize slot options and set up listeners
    if (window.itemManager) {
        addToConsole('✅ ItemManager is available', 'success');
        addToConsole(`📦 ItemManager version: ${window.itemManager.version || 'Unknown'}`, 'info');

        // Load item database
        loadItemDatabase();

        // Load account database
        loadPlayerDatabase().then(database => {
            if (database.length > 0) {
                addToConsole(`👥 Account database loaded: ${database.length} accounts`, 'success');
                populatePlayerDatalist();
            } else {
                addToConsole('⚠️ Account database not loaded, using mock data', 'warning');
            }
        });

        // Load saved form data
        const dataLoaded = loadFormData();
        if (dataLoaded) {
            addToConsole('📂 Previous form data restored', 'info');
        } else {
            addToConsole('📝 Starting with fresh form', 'info');
        }

        // Setup auto-save
        setupAutoSave();

        // Setup real-time calculation
        setupRealTimeCalculation();

        // Setup Hex/Dec converter (compatible with manage-item.php)
        setupHexDecConverter();

        // Setup Binding calculator (compatible with manage-item.php)
        setupBindingCalculator();

        // Set up item type change listener
        const itemTypeSelect = document.getElementById('itemType');
        if (itemTypeSelect) {
            itemTypeSelect.addEventListener('change', function() {
                updateSlotOptions();

                // Trigger real-time calculation after slot options update
                if (isRealTimeEnabled) {
                    setTimeout(performRealTimeCalculation, 100);
                }

                // Log the change
                addToConsole(`🔄 Item Type changed to: ${this.value}`, 'info');
            });
        }

        // Set up item search listener
        const itemSearchInput = document.getElementById('itemSearch');
        if (itemSearchInput) {
            // Real-time search as user types
            itemSearchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    const matches = searchItems(query);
                    if (matches.length > 0) {
                        addToConsole(`🔍 Found ${matches.length} matches for "${query}"`, 'info');
                    }
                }
            });

            // Handle selection when user finishes typing or selects from datalist
            itemSearchInput.addEventListener('change', handleItemSelection);
            itemSearchInput.addEventListener('blur', handleItemSelection);
        }

        // Set up item ID input listener
        const itemIdInput = document.getElementById('itemId');
        if (itemIdInput) {
            itemIdInput.addEventListener('input', function() {
                const itemId = parseInt(this.value);
                if (itemId && itemId > 0) {
                    handleItemIdInput(itemId);
                } else {
                    updateItemIdStatus('info', 'Enter item ID to validate');
                    disableSendButton();
                }
            });

            itemIdInput.addEventListener('change', function() {
                const itemId = parseInt(this.value);
                if (itemId && itemId > 0) {
                    handleItemIdInput(itemId);
                }
            });
        }

        // Set up player username input listener
        const playerUsernameInput = document.getElementById('playerUsername');
        if (playerUsernameInput) {
            // Real-time search as user types
            playerUsernameInput.addEventListener('input', function() {
                const username = this.value.trim();

                if (username.length >= 2) {
                    // Show matching players in console
                    const matches = searchPlayers(username);
                    if (matches.length > 0) {
                        addToConsole(`🔍 Found ${matches.length} matching players for "${username}"`,
                            'info');
                        updatePlayerStatus('info', `${matches.length} matches found`);
                    } else {
                        updatePlayerStatus('warning', 'No matches found');
                    }
                } else if (username.length > 0) {
                    updatePlayerStatus('warning', 'Type more characters');
                    disableSendButton();
                } else {
                    updatePlayerStatus('info', 'Enter username to validate');
                    disableSendButton();
                }
            });

            // Handle selection when user finishes typing or selects from datalist
            playerUsernameInput.addEventListener('change', function() {
                const username = this.value.trim();
                if (username.length >= 3) {
                    handlePlayerSelection();
                }
            });

            // Handle blur event (when user clicks away)
            playerUsernameInput.addEventListener('blur', function() {
                const username = this.value.trim();
                if (username.length >= 3) {
                    // Delay to allow datalist selection to complete
                    setTimeout(() => {
                        handlePlayerSelection();
                    }, 200);
                }
            });

            // Handle Enter key
            playerUsernameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const username = this.value.trim();
                    if (username.length >= 3) {
                        validatePlayerFromDatabase();
                    } else {
                        addToConsole('❌ Username must be at least 3 characters', 'error');
                    }
                }
            });
        }

        // Set up quantity input validation
        const quantityInput = document.getElementById('itemQuantity');
        if (quantityInput) {
            quantityInput.addEventListener('input', function() {
                const quantity = parseInt(this.value);
                if (quantity < 1) {
                    this.value = 1;
                } else if (quantity > 999) {
                    this.value = 999;
                }
            });
        }

        // Set up send method change listener
        const sendMethodSelect = document.getElementById('sendMethod');
        if (sendMethodSelect) {
            sendMethodSelect.addEventListener('change', function() {
                const method = this.value;
                addToConsole(`📦 Send method changed to: ${method}`, 'info');

                // Update UI based on send method
                if (method === 'mail') {
                    addToConsole('📧 Mail system selected - item will be sent to in-game mail', 'info');
                } else if (method === 'event_inventory') {
                    addToConsole('🏪 Event Inventory selected - item will be added to event inventory',
                        'info');
                } else {
                    addToConsole('💰 Cash Inventory selected - item will be added to cash inventory',
                        'info');
                }
            });
        }

        // Initialize slot options (with delay to ensure form data is loaded first)
        setTimeout(() => {
            updateSlotOptions();
            addToConsole('🔄 Form initialization complete', 'success');

            // Perform initial real-time calculation
            if (isRealTimeEnabled) {
                setTimeout(performRealTimeCalculation, 500);
            }

            // Show initialization summary
            showInitializationSummary();
        }, 200);
    } else {
        addToConsole('❌ ItemManager not found', 'error');
    }

    // Show initialization summary
    function showInitializationSummary() {
        const history = JSON.parse(localStorage.getItem('itemSendHistory') || '[]');
        const savedData = localStorage.getItem(STORAGE_KEY);

        addToConsole('📊 System Status Summary:', 'info');
        addToConsole(`   • ItemManager: ${window.itemManager ? 'Available' : 'Not Available'}`, 'info');
        addToConsole(
            `   • Item Database: ${itemDatabase.length > 0 ? itemDatabase.length + ' items loaded' : 'Not loaded'}`,
            'info');
        addToConsole(
            `   • Account Database: ${playerDatabase.length > 0 ? playerDatabase.length + ' accounts loaded' : 'Not loaded'}`,
            'info');
        addToConsole(`   • Send History: ${history.length} entries`, 'info');
        addToConsole(`   • Saved Form Data: ${savedData ? 'Available' : 'None'}`, 'info');
        addToConsole(`   • Real-time Calculation: ${isRealTimeEnabled ? 'Enabled' : 'Disabled'}`, 'info');
        addToConsole(`   • SweetAlert: ${typeof Swal !== 'undefined' ? 'Available' : 'Not Available'}`, 'info');
        addToConsole('🚀 Advanced Item Editor ready for use!', 'success');

        // Test SweetAlert availability
        if (typeof Swal === 'undefined') {
            addToConsole('⚠️ Warning: SweetAlert library is not loaded. Some features may not work.',
            'warning');
        } else {
            addToConsole('✅ SweetAlert library loaded successfully', 'success');
        }
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+Enter to calculate
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('calculateFromFormBtn').click();
            addToConsole('⌨️ Keyboard shortcut: Calculate (Ctrl+Enter)', 'info');
        }

        // Ctrl+Shift+S to send item
        if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            const sendBtn = document.getElementById('sendItemBtn');
            if (!sendBtn.disabled) {
                sendBtn.click();
                addToConsole('⌨️ Keyboard shortcut: Send Item (Ctrl+Shift+S)', 'info');
            } else {
                addToConsole('⌨️ Send button is disabled', 'warning');
            }
        }

        // Ctrl+R to reset form
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            document.getElementById('resetFormBtn').click();
            addToConsole('⌨️ Keyboard shortcut: Reset Form (Ctrl+R)', 'info');
        }

        // F5 to refresh database
        if (e.key === 'F5' && !e.ctrlKey) {
            e.preventDefault();
            document.getElementById('databaseLoadBtn').click();
            addToConsole('⌨️ Keyboard shortcut: Reload Database (F5)', 'info');
        }
    });

    // Show keyboard shortcuts help
    addToConsole('⌨️ Keyboard Shortcuts Available:', 'info');
    addToConsole('   • Ctrl+Enter: Calculate item', 'info');
    addToConsole('   • Ctrl+Shift+S: Send item', 'info');
    addToConsole('   • Ctrl+R: Reset form', 'info');
    addToConsole('   • F5: Reload database', 'info');
});
</script>

<!-- Notification System -->
<script src="files/game_systems/notification_manager.js"></script>