<?php $zpanel->checkSession(true); ?>

<header class="page-header">
    <h2>สถิติบัญชีรวม (Admin) <span class="label label-success">Real-time</span></h2>
    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>Manager Account</span></li>
            <li><span>Statistics</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>

<?php
// Time periods for statistics
$periods = [
    '7' => '7 วันล่าสุด',
    '30' => '30 วันล่าสุด',
    '90' => '3 เดือนล่าสุด',
    '365' => '1 ปีล่าสุด'
];

$selectedPeriod = isset($_GET['period']) ? $_GET['period'] : '30';
$comparisonMode = isset($_GET['comparison']) ? $_GET['comparison'] : 'none';
$exportFormat = isset($_GET['export']) ? $_GET['export'] : '';

// Handle export requests
if ($exportFormat) {
    switch ($exportFormat) {
        case 'csv':
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="statistics_' . date('Y-m-d') . '.csv"');
            // Export logic will be added here
            exit;
        case 'pdf':
            // PDF export logic will be added here
            exit;
        case 'excel':
            // Excel export logic will be added here
            exit;
    }
}

// Overall statistics
$selectOverallStats = "SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as active_accounts,
    COUNT(CASE WHEN createDate >= DATEADD(day, -$selectedPeriod, GETDATE()) THEN 1 END) as new_accounts,
    AVG(PlayTime) as avg_playtime,
    SUM(PlayTime) as total_playtime,
    COUNT(CASE WHEN Login = 1 THEN 1 END) as currently_online
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
$overallQuery = sqlsrv_query($conn, $selectOverallStats);
$overallStats = sqlsrv_fetch_array($overallQuery, SQLSRV_FETCH_ASSOC);

// Financial statistics
$selectFinancialStats = "SELECT 
    SUM(CAST(c.Cash as BIGINT)) as total_cash,
    SUM(CAST(c.CashBonus as BIGINT)) as total_cash_bonus,
    SUM(CAST(c.CashTotal as BIGINT)) as total_cash_all,
    SUM(CAST(c.Rpoint as BIGINT)) as total_rewards,
    COUNT(CASE WHEN c.Cash > 0 THEN 1 END) as accounts_with_cash,
    COUNT(p.UserNum) as total_purchases
    FROM [".DATABASE_CCA."].[dbo].CashAccount c
    LEFT JOIN WEB_MyPurchases p ON c.UserNum = p.UserNum";
$financialStatsQuery = sqlsrv_query($conn, $selectFinancialStats);
$financialStats = sqlsrv_fetch_array($financialStatsQuery, SQLSRV_FETCH_ASSOC);

// Character level distribution
$selectLevelDist = "SELECT 
    CASE 
        WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
        WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
        WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
        WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
        ELSE '200+'
    END as level_range,
    COUNT(*) as character_count
    FROM [".DATABASE_SV."].[dbo].cabal_character_table 
    WHERE LEV > 0
    GROUP BY 
        CASE 
            WHEN LEV BETWEEN 1 AND 50 THEN '1-50'
            WHEN LEV BETWEEN 51 AND 100 THEN '51-100'
            WHEN LEV BETWEEN 101 AND 150 THEN '101-150'
            WHEN LEV BETWEEN 151 AND 200 THEN '151-200'
            ELSE '200+'
        END
    ORDER BY level_range";
$levelDistQuery = sqlsrv_query($conn, $selectLevelDist);
$levelDistribution = array();
while ($row = sqlsrv_fetch_array($levelDistQuery, SQLSRV_FETCH_ASSOC)) {
    $levelDistribution[] = $row;
}

// Peak hours analysis
$selectPeakHours = "SELECT 
    DATEPART(hour, LoginTime) as hour_of_day,
    COUNT(*) as login_count,
    COUNT(DISTINCT UserNum) as unique_players
    FROM [".DATABASE_ACC."].[dbo].cabal_auth_table 
    WHERE LoginTime >= DATEADD(day, -$selectedPeriod, GETDATE())
    GROUP BY DATEPART(hour, LoginTime)
    ORDER BY login_count DESC";
$peakHoursQuery = sqlsrv_query($conn, $selectPeakHours);
$peakHours = array();
while ($row = sqlsrv_fetch_array($peakHoursQuery, SQLSRV_FETCH_ASSOC)) {
    $peakHours[] = $row;
}
?>

<div class="row">
    <!-- Period Selector -->
    <div class="col-md-12">
        <section class="panel">
            <header class="panel-heading">
                <h2 class="panel-title">ตัวควบคุม</h2>
            </header>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <label>ช่วงเวลา:</label>
                        <div class="btn-group" role="group" style="margin-bottom: 10px;">
                            <?php foreach ($periods as $period => $label): ?>
                                <a href="?url=manager_account/statistics-simple&period=<?php echo $period; ?>&comparison=<?php echo $comparisonMode; ?>" 
                                   class="btn btn-sm <?php echo $selectedPeriod == $period ? 'btn-primary' : 'btn-default'; ?>">
                                    <?php echo $label; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label>ส่งออกข้อมูล:</label>
                        <div class="btn-group" role="group" style="margin-bottom: 10px;">
                            <button class="btn btn-success btn-sm" onclick="exportData('csv')">
                                <i class="fa fa-file-text-o"></i> CSV
                            </button>
                            <button class="btn btn-info btn-sm" onclick="exportData('excel')">
                                <i class="fa fa-file-excel-o"></i> Excel
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="exportData('pdf')">
                                <i class="fa fa-file-pdf-o"></i> PDF
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <label>โหมดเปรียบเทียบ:</label>
                        <div class="btn-group" role="group">
                            <a href="?url=manager_account/statistics-simple&period=<?php echo $selectedPeriod; ?>&comparison=none" 
                               class="btn btn-sm <?php echo $comparisonMode == 'none' ? 'btn-primary' : 'btn-default'; ?>">
                                ปกติ
                            </a>
                            <a href="?url=manager_account/statistics-simple&period=<?php echo $selectedPeriod; ?>&comparison=previous" 
                               class="btn btn-sm <?php echo $comparisonMode == 'previous' ? 'btn-primary' : 'btn-default'; ?>">
                                เปรียบเทียบช่วงก่อน
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label>อัปเดตอัตโนมัติ:</label>
                        <label class="checkbox-inline">
                            <input type="checkbox" id="autoRefresh" checked> ทุก 30 วินาที
                        </label>
                        <button class="btn btn-warning btn-sm" onclick="refreshStatistics()">
                            <i class="fa fa-refresh"></i> รีเฟรช
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<?php if ($comparisonMode != 'none'): ?>
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <h4><i class="fa fa-bar-chart"></i> โหมดเปรียบเทียบ: 
                <?php 
                echo $comparisonMode == 'previous' ? 'เปรียบเทียบกับช่วงก่อนหน้า' : 'เปรียบเทียบกับปีก่อน';
                ?>
            </h4>
            <p>ข้อมูลจะแสดงการเปรียบเทียบระหว่างช่วงเวลาที่เลือกกับช่วงเวลาอ้างอิง พร้อมแสดงเปอร์เซ็นต์การเปลี่ยนแปลง</p>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- Overall Statistics -->
    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-primary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-primary">
                            <i class="fa fa-users"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">บัญชีทั้งหมด</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['total_accounts']); ?></strong>
                                <span class="text-primary">บัญชี</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-success"><?php echo $overallStats['currently_online']; ?> ออนไลน์</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-secondary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-secondary">
                            <i class="fa fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">ผู้เล่นที่ใช้งาน</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['active_accounts']); ?></strong>
                                <span class="text-secondary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-tertiary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-tertiary">
                            <i class="fa fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">สมาชิกใหม่</h4>
                            <div class="info">
                                <strong class="amount"><?php echo number_format($overallStats['new_accounts']); ?></strong>
                                <span class="text-tertiary">คน</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">ใน <?php echo $periods[$selectedPeriod]; ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div class="col-xl-3 col-lg-6">
        <section class="panel panel-featured-left panel-featured-quaternary">
            <div class="panel-body">
                <div class="widget-summary">
                    <div class="widget-summary-col widget-summary-col-icon">
                        <div class="summary-icon bg-quaternary">
                            <i class="fa fa-clock"></i>
                        </div>
                    </div>
                    <div class="widget-summary-col">
                        <div class="summary">
                            <h4 class="title">เวลาเล่นเฉลี่ย</h4>
                            <div class="info">
                                <strong class="amount"><?php echo round($overallStats['avg_playtime'] / 60, 1); ?></strong>
                                <span class="text-quaternary">ชั่วโมง</span>
                            </div>
                        </div>
                        <div class="summary-footer">
                            <span class="text-muted">รวม <?php echo round($overallStats['total_playtime'] / 60, 0); ?> ชั่วโมง</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
