<?php
// Test Admin Username Fix
$zpanel->checkSession(true);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Username Fix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-user-check me-2"></i>Test Admin Username Fix</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Fix Applied</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Admin Username Fixed:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-times text-danger me-2"></i>
                                <s>adminUsername: 'Admin'</s> (Hard-coded)
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success me-2"></i>
                                <code>adminUsername: '<?php echo $_SESSION['userLogin'] ?? 'System'; ?>'</code>
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">🔧 Current Session Info:</h6>
                        <div class="bg-light p-3 rounded">
                            <p><strong>Session Status:</strong> 
                                <span class="badge bg-<?php echo isset($_SESSION['userLogin']) ? 'success' : 'danger'; ?>">
                                    <?php echo isset($_SESSION['userLogin']) ? 'Active' : 'Not Found'; ?>
                                </span>
                            </p>
                            <p><strong>User Login:</strong> 
                                <code><?php echo $_SESSION['userLogin'] ?? 'Not Set'; ?></code>
                            </p>
                            <p><strong>User ID:</strong> 
                                <code><?php echo $_SESSION['userID'] ?? 'Not Set'; ?></code>
                            </p>
                            <p class="mb-0"><strong>Is Developer:</strong> 
                                <span class="badge bg-<?php echo ($_SESSION['IsDeveloper'] ?? false) ? 'success' : 'warning'; ?>">
                                    <?php echo ($_SESSION['IsDeveloper'] ?? false) ? 'Yes' : 'No'; ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Admin Username</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า adminUsername ใช้ user ที่ login เข้าระบบจริง</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testCurrentUser" class="btn btn-primary">
                                <i class="fas fa-user me-2"></i>Test Current User
                            </button>
                            
                            <button id="simulateSendItem" class="btn btn-success">
                                <i class="fas fa-paper-plane me-2"></i>Simulate Send Item
                            </button>
                            
                            <a href="advanced-editor.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Hard-coded):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Fixed admin username
adminUsername: 'Admin'

// Problems:
// - Always shows 'Admin' in logs
// - Cannot track real user
// - No accountability</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (Dynamic):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Dynamic admin username from session
adminUsername: '<?php echo $_SESSION['userLogin'] ?? 'System'; ?>'

// Benefits:
// - Shows real logged-in user
// - Proper audit trail
// - User accountability</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-database me-2"></i>Admin Logs Impact</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📊 Before Fix:</h6>
                                <div class="bg-light p-3 rounded">
                                    <table class="table table-sm mb-0">
                                        <thead>
                                            <tr>
                                                <th>Admin Username</th>
                                                <th>Action</th>
                                                <th>Target</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>Admin</code></td>
                                                <td>Send Item</td>
                                                <td>player1</td>
                                            </tr>
                                            <tr>
                                                <td><code>Admin</code></td>
                                                <td>Send Item</td>
                                                <td>player2</td>
                                            </tr>
                                            <tr>
                                                <td><code>Admin</code></td>
                                                <td>Send Item</td>
                                                <td>player3</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <small class="text-danger">❌ Cannot identify real user</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📊 After Fix:</h6>
                                <div class="bg-light p-3 rounded">
                                    <table class="table table-sm mb-0">
                                        <thead>
                                            <tr>
                                                <th>Admin Username</th>
                                                <th>Action</th>
                                                <th>Target</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code><?php echo $_SESSION['userLogin'] ?? 'user1'; ?></code></td>
                                                <td>Send Item</td>
                                                <td>player1</td>
                                            </tr>
                                            <tr>
                                                <td><code><?php echo $_SESSION['userLogin'] ?? 'user2'; ?></code></td>
                                                <td>Send Item</td>
                                                <td>player2</td>
                                            </tr>
                                            <tr>
                                                <td><code><?php echo $_SESSION['userLogin'] ?? 'user3'; ?></code></td>
                                                <td>Send Item</td>
                                                <td>player3</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <small class="text-success">✅ Real user identification</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Benefits of Real User Tracking:</h6>
                            <ul class="mb-0">
                                <li>✅ <strong>Accountability:</strong> Know who performed each action</li>
                                <li>✅ <strong>Audit Trail:</strong> Proper logging for security</li>
                                <li>✅ <strong>User Management:</strong> Track individual admin activity</li>
                                <li>✅ <strong>Debugging:</strong> Easier to trace issues to specific users</li>
                                <li>✅ <strong>Compliance:</strong> Meet audit requirements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>How to Verify</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Verification Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Check Current User:</strong> Verify session shows correct user above</li>
                                <li><strong>Open Advanced Editor:</strong> Use the link above</li>
                                <li><strong>Send Test Item:</strong> Send an item to any player</li>
                                <li><strong>Check Admin Logs:</strong> Verify logs show your username, not 'Admin'</li>
                                <li><strong>Test with Different Users:</strong> Login as different users and verify</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Expected Results:</h6>
                            <ul class="mb-0">
                                <li>🔍 Admin logs show real username: <code><?php echo $_SESSION['userLogin'] ?? 'your-username'; ?></code></li>
                                <li>📊 Activity feed shows correct user</li>
                                <li>🔒 Proper audit trail maintained</li>
                                <li>👤 User accountability established</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testCurrentUser').click(function() {
            const currentUser = '<?php echo $_SESSION['userLogin'] ?? 'Not Set'; ?>';
            const userID = '<?php echo $_SESSION['userID'] ?? 'Not Set'; ?>';
            const isDev = <?php echo ($_SESSION['IsDeveloper'] ?? false) ? 'true' : 'false'; ?>;
            
            showResult('Current User Information', `
                <p><strong>Username:</strong> <code>${currentUser}</code></p>
                <p><strong>User ID:</strong> <code>${userID}</code></p>
                <p><strong>Developer Status:</strong> <span class="badge bg-${isDev ? 'success' : 'warning'}">${isDev ? 'Yes' : 'No'}</span></p>
                <p class="mb-0"><strong>Admin Username in Logs:</strong> <code>${currentUser}</code></p>
            `, currentUser !== 'Not Set' ? 'success' : 'warning');
        });

        $('#simulateSendItem').click(function() {
            const currentUser = '<?php echo $_SESSION['userLogin'] ?? 'System'; ?>';
            
            showResult('Simulated Send Item Data', `
                <p>When sending an item, the following data will be logged:</p>
                <div class="bg-light p-3 rounded">
                    <pre class="small mb-0">{
    "adminUsername": "${currentUser}",
    "action": "Send Item",
    "target_player": "example_player",
    "details": {
        "item_id": 123,
        "quantity": 1,
        "method": "inventory"
    },
    "ip_address": "<?php echo $_SERVER['REMOTE_ADDR'] ?? 'unknown'; ?>",
    "created_at": "${new Date().toISOString()}"
}</pre>
                </div>
                <p class="mb-0 mt-2"><strong>Result:</strong> Admin logs will show <code>${currentUser}</code> instead of <code>Admin</code></p>
            `, 'success');
        });

        // Auto-run current user test
        setTimeout(() => {
            $('#testCurrentUser').click();
        }, 1000);
    </script>
</body>
</html>
