<?php
// Include existing configuration files
require_once("../../_app/dbinfo.inc.php");
require_once("../../_app/general_config.inc.php");

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

/**
 * Notifications API
 * Manages notifications for the game system
 */

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}



function createNotification($conn, $data) {
    try {
        // Use admin_logs table instead of notifications table
        $sql = "INSERT INTO admin_logs (
            admin_username, action, target_player, details, ip_address, created_at
        ) VALUES (?, ?, ?, ?, ?, GETDATE())";

        $params = [
            $data['admin_username'] ?? 'System',
            $data['action'] ?? 'Notification',
            $data['target_player'] ?? null,
            $data['message'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        $stmt = sqlsrv_prepare($conn, $sql, $params);
        if ($stmt === false) {
            throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
        }

        $result = sqlsrv_execute($stmt);
        if ($result === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }

        // Get the inserted ID
        $idQuery = "SELECT SCOPE_IDENTITY() as id";
        $idResult = sqlsrv_query($conn, $idQuery);
        $idRow = sqlsrv_fetch_array($idResult, SQLSRV_FETCH_ASSOC);

        return $idRow['id'];

    } catch (Exception $e) {
        error_log("Create Notification Error: " . $e->getMessage());
        throw $e;
    }
}

function getNotifications($conn, $filters = []) {
    try {
        // Use admin_logs table instead of notifications table
        $sql = "SELECT TOP 50 id, admin_username, action, target_player, details,
                ip_address, created_at
                FROM admin_logs";

        $whereConditions = [];
        $params = [];

        if (!empty($filters['admin_username'])) {
            $whereConditions[] = "admin_username = ?";
            $params[] = $filters['admin_username'];
        }

        if (!empty($filters['target_player'])) {
            $whereConditions[] = "target_player = ?";
            $params[] = $filters['target_player'];
        }

        if (!empty($filters['action'])) {
            $whereConditions[] = "action LIKE ?";
            $params[] = '%' . $filters['action'] . '%';
        }

        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $sql .= " ORDER BY created_at DESC";
        
        if (empty($params)) {
            $stmt = sqlsrv_query($conn, $sql);
        } else {
            $stmt = sqlsrv_prepare($conn, $sql, $params);
            if ($stmt === false) {
                throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
            }
            $stmt = sqlsrv_execute($stmt) ? $stmt : false;
        }
        
        if ($stmt === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        $notifications = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // Format datetime fields safely
            if ($row['created_at']) {
                if (is_object($row['created_at']) && method_exists($row['created_at'], 'format')) {
                    $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
                } else {
                    $row['created_at'] = (string)$row['created_at'];
                }
            }

            // Map admin_logs fields to notification format
            $notification = [
                'id' => $row['id'],
                'title' => $row['action'],
                'message' => $row['details'],
                'admin_username' => $row['admin_username'],
                'target_player' => $row['target_player'],
                'ip_address' => $row['ip_address'],
                'created_at' => $row['created_at'],
                'type' => 'admin_log'
            ];

            $notifications[] = $notification;
        }
        
        return $notifications;
        
    } catch (Exception $e) {
        error_log("Get Notifications Error: " . $e->getMessage());
        throw $e;
    }
}

function markAsRead($conn, $notificationId, $userId) {
    // admin_logs table doesn't support read status
    // Return success for compatibility
    return [
        'success' => true,
        'message' => 'Mark as read not supported for admin logs'
    ];
}

function deleteNotification($conn, $notificationId) {
    // admin_logs table shouldn't be deleted for audit purposes
    // Return success for compatibility
    return [
        'success' => true,
        'message' => 'Delete not supported for admin logs (audit trail)'
    ];
}

// Main API handler
try {

    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    $currentUser = $_SESSION['userLogin'] ?? 'Unknown';
    
    if ($method === 'POST') {
        // Create new notification
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $input['created_by'] = $currentUser;
        
        $notificationId = createNotification($conn, $input);
        
        echo json_encode([
            'success' => true,
            'message' => 'Notification created successfully',
            'notification_id' => $notificationId
        ]);
        
    } elseif ($method === 'GET') {
        // Get notifications
        $filters = [
            'target_user' => $_GET['target_user'] ?? $currentUser,
            'target_role' => $_GET['target_role'] ?? null,
            'type' => $_GET['type'] ?? null,
            'is_read' => $_GET['is_read'] ?? null,
            'priority' => $_GET['priority'] ?? null
        ];
        
        // Remove empty filters
        $filters = array_filter($filters, function($value) {
            return !empty($value);
        });
        
        $notifications = getNotifications($conn, $filters);
        
        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'count' => count($notifications)
        ]);
        
    } elseif ($method === 'PUT') {
        // Mark as read
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['notification_id'])) {
            throw new Exception('Notification ID required');
        }
        
        $success = markAsRead($conn, $input['notification_id'], $currentUser);
        
        echo json_encode([
            'success' => $success,
            'message' => 'Notification marked as read'
        ]);
        
    } elseif ($method === 'DELETE') {
        // Delete notification
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['notification_id'])) {
            throw new Exception('Notification ID required');
        }
        
        $success = deleteNotification($conn, $input['notification_id']);
        
        echo json_encode([
            'success' => $success,
            'message' => 'Notification deleted successfully'
        ]);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>
