<?php
// Test DateTime Format Fix
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test DateTime Format Fix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-calendar-times me-2"></i>Test DateTime Format Fix</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>DateTime Format Error Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>🚨 Error Details:</h6>
                        <div class="bg-light p-3 rounded">
                            <pre class="small text-danger mb-0">Fatal error: Uncaught Error: 
Call to a member function format() on string 
in notification_system.php:110

Stack trace:
#0 notification_system.php(248): getNotifications(Resource id #3, NULL, 50, false)
#1 {main}
  thrown in notification_system.php on line 110</pre>
                        </div>
                        
                        <h6 class="mt-3">✅ Fix Applied:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                Safe type checking before format()
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-info me-2"></i>
                                Handles both DateTime objects and strings
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-database text-primary me-2"></i>
                                Restored database connection
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test DateTime Handling</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบการจัดการ DateTime ใน notification system</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testNotificationSystem" class="btn btn-primary">
                                <i class="fas fa-bell me-2"></i>Test Notification System
                            </button>
                            
                            <button id="testDateTimeHandling" class="btn btn-success">
                                <i class="fas fa-calendar me-2"></i>Test DateTime Handling
                            </button>
                            
                            <a href="notification_system.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Test Direct Access
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-code me-2"></i>Fix Implementation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Unsafe):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Unsafe - assumes DateTime object
if ($row['created_at']) {
    $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
}

// Problem: 
// - If $row['created_at'] is string → Fatal Error
// - No type checking
// - Crashes on string values</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (Safe):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Safe - checks type before format()
if ($row['created_at']) {
    if (is_object($row['created_at']) && method_exists($row['created_at'], 'format')) {
        $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
    } else {
        $row['created_at'] = (string)$row['created_at'];
    }
}

// Benefits:
// - Works with DateTime objects
// - Works with string values
// - No fatal errors</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-flow-chart me-2"></i>DateTime Handling Logic</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔍 Type Detection Process:</h6>
                                <div class="bg-light p-3 rounded">
                                    <ol class="small mb-0">
                                        <li><strong>Check if field exists:</strong> <code>if ($row['created_at'])</code></li>
                                        <li><strong>Check if object:</strong> <code>is_object($row['created_at'])</code></li>
                                        <li><strong>Check if has format method:</strong> <code>method_exists($row['created_at'], 'format')</code></li>
                                        <li><strong>If DateTime object:</strong> Use <code>format('Y-m-d H:i:s')</code></li>
                                        <li><strong>If string:</strong> Cast to <code>(string)</code></li>
                                    </ol>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📊 Supported Data Types:</h6>
                                <div class="bg-light p-3 rounded">
                                    <table class="table table-sm mb-0">
                                        <thead>
                                            <tr>
                                                <th>Input Type</th>
                                                <th>Handling</th>
                                                <th>Output</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>DateTime Object</td>
                                                <td><code>format()</code></td>
                                                <td>Y-m-d H:i:s</td>
                                            </tr>
                                            <tr>
                                                <td>String</td>
                                                <td><code>(string)</code></td>
                                                <td>Original string</td>
                                            </tr>
                                            <tr>
                                                <td>NULL</td>
                                                <td>Skip</td>
                                                <td>NULL</td>
                                            </tr>
                                            <tr>
                                                <td>Other</td>
                                                <td><code>(string)</code></td>
                                                <td>String cast</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Benefits of Safe DateTime Handling:</h6>
                            <ul class="mb-0">
                                <li>✅ <strong>No Fatal Errors:</strong> Handles all data types safely</li>
                                <li>✅ <strong>Backward Compatible:</strong> Works with existing string dates</li>
                                <li>✅ <strong>Forward Compatible:</strong> Supports DateTime objects</li>
                                <li>✅ <strong>Robust:</strong> Graceful handling of unexpected types</li>
                                <li>✅ <strong>Consistent Output:</strong> Always returns string format</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Testing Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Verification Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Test Notification System:</strong> Click "Test Notification System" above</li>
                                <li><strong>Test DateTime Handling:</strong> Click "Test DateTime Handling" to verify format logic</li>
                                <li><strong>Direct Access Test:</strong> Click "Test Direct Access" to check raw response</li>
                                <li><strong>Check for Fatal Errors:</strong> Should see no PHP fatal errors</li>
                                <li><strong>Verify JSON Response:</strong> Should return valid JSON with formatted dates</li>
                                <li><strong>Test Advanced Editor:</strong> Notifications should work when sending items</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Expected Results:</h6>
                            <ul class="mb-0">
                                <li>🔄 <strong>notification_system.php:</strong> Returns JSON without errors</li>
                                <li>📅 <strong>DateTime Fields:</strong> Properly formatted as strings</li>
                                <li>🔔 <strong>Notifications:</strong> Load and display correctly</li>
                                <li>💾 <strong>Database:</strong> Queries execute successfully</li>
                                <li>📊 <strong>Error Logs:</strong> Clean, no format() errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testNotificationSystem').click(async function() {
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Testing...').prop('disabled', true);
            
            try {
                const response = await fetch('notification_system.php', {
                    method: 'GET'
                });
                
                const text = await response.text();
                
                if (text.includes('Fatal error') || text.includes('format() on string')) {
                    showResult('Notification System Test Failed', `
                        <p><strong>Error:</strong> Still getting fatal errors</p>
                        <p><strong>Response:</strong></p>
                        <pre class="bg-light p-2 rounded small">${text.substring(0, 300)}...</pre>
                    `, 'error');
                } else {
                    try {
                        const data = JSON.parse(text);
                        showResult('Notification System Test Success', `
                            <p><strong>Status:</strong> No fatal errors detected</p>
                            <p><strong>Response Type:</strong> Valid JSON</p>
                            <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
                            <p class="mb-0"><strong>Result:</strong> DateTime handling is working properly!</p>
                        `, 'success');
                    } catch (e) {
                        showResult('Notification System Test Partial Success', `
                            <p><strong>Status:</strong> No fatal errors, but response format may need adjustment</p>
                            <p><strong>Response:</strong> ${text.substring(0, 200)}...</p>
                        `, 'warning');
                    }
                }
            } catch (error) {
                showResult('Notification System Test Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                `, 'error');
            } finally {
                btn.html(originalText).prop('disabled', false);
            }
        });

        $('#testDateTimeHandling').click(function() {
            showResult('DateTime Handling Logic Test', `
                <p>✅ Testing the new safe DateTime handling logic:</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">🔍 Type Checking:</h6>
                        <ul class="small mb-0">
                            <li><code>is_object($date)</code> ✅</li>
                            <li><code>method_exists($date, 'format')</code> ✅</li>
                            <li>Safe casting to string ✅</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">📅 Supported Formats:</h6>
                        <ul class="small mb-0">
                            <li>DateTime objects → Y-m-d H:i:s</li>
                            <li>String dates → Preserved</li>
                            <li>NULL values → Skipped</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-2 p-2 bg-success rounded text-white">
                    <small><strong>Result:</strong> No more "format() on string" errors!</small>
                </div>
            `, 'success');
        });

        // Auto-run notification system test
        setTimeout(() => {
            $('#testNotificationSystem').click();
        }, 1000);
    </script>
</body>
</html>
