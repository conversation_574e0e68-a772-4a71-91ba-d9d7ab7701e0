<?php
// Create Notifications Table
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
require_once '../config/database.php';

// Check if user has permission
if (!isset($_SESSION['admin_username'])) {
    die('Access denied. Admin login required.');
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Notifications Table</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h1><i class="fas fa-database me-2 text-primary"></i>Create Notifications Table</h1>
        <p class="lead">สร้างตาราง notifications สำหรับระบบแจ้งเตือน</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-table me-2"></i>Notifications Table Setup</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_table'])) {
                            try {
                                // Check if table already exists
                                $check_sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'notifications'";
                                $check_result = sqlsrv_query($conn, $check_sql);
                                
                                if ($check_result === false) {
                                    throw new Exception("Unable to check table existence");
                                }
                                
                                $table_exists = sqlsrv_fetch_array($check_result, SQLSRV_FETCH_ASSOC);
                                
                                if ($table_exists['count'] > 0) {
                                    echo '<div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Table "notifications" already exists!
                                          </div>';
                                } else {
                                    // Create notifications table
                                    $create_sql = "
                                        CREATE TABLE notifications (
                                            notification_id INT IDENTITY(1,1) PRIMARY KEY,
                                            title NVARCHAR(255) NOT NULL,
                                            message NVARCHAR(MAX) NOT NULL,
                                            type NVARCHAR(50) NOT NULL DEFAULT 'info',
                                            priority NVARCHAR(20) NOT NULL DEFAULT 'normal',
                                            details NVARCHAR(MAX) NULL,
                                            admin_username NVARCHAR(100) NULL,
                                            created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
                                            is_read BIT NOT NULL DEFAULT 0,
                                            read_at DATETIME2 NULL,
                                            expires_at DATETIME2 NOT NULL DEFAULT DATEADD(day, 30, GETDATE())
                                        )
                                    ";
                                    
                                    $create_result = sqlsrv_query($conn, $create_sql);
                                    
                                    if ($create_result === false) {
                                        $errors = sqlsrv_errors();
                                        throw new Exception("Failed to create table: " . print_r($errors, true));
                                    }
                                    
                                    // Create indexes
                                    $index_sqls = [
                                        "CREATE INDEX IX_notifications_admin_username ON notifications (admin_username)",
                                        "CREATE INDEX IX_notifications_created_at ON notifications (created_at DESC)",
                                        "CREATE INDEX IX_notifications_is_read ON notifications (is_read)",
                                        "CREATE INDEX IX_notifications_expires_at ON notifications (expires_at)",
                                        "CREATE INDEX IX_notifications_type ON notifications (type)"
                                    ];
                                    
                                    foreach ($index_sqls as $index_sql) {
                                        $index_result = sqlsrv_query($conn, $index_sql);
                                        if ($index_result === false) {
                                            error_log("Warning: Failed to create index: " . $index_sql);
                                        }
                                    }
                                    
                                    echo '<div class="alert alert-success">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong>Success!</strong> Notifications table created successfully with indexes.
                                          </div>';
                                    
                                    // Insert sample notification
                                    $sample_sql = "
                                        INSERT INTO notifications (title, message, type, priority, admin_username, details)
                                        VALUES (?, ?, ?, ?, ?, ?)
                                    ";
                                    
                                    $sample_title = "Welcome to Notification System";
                                    $sample_message = "Notification system has been successfully set up and is ready to use.";
                                    $sample_type = "success";
                                    $sample_priority = "normal";
                                    $sample_admin = $_SESSION['admin_username'];
                                    $sample_details = json_encode([
                                        'setup_date' => date('Y-m-d H:i:s'),
                                        'version' => '1.0',
                                        'features' => ['real-time notifications', 'admin filtering', 'read status tracking']
                                    ]);
                                    
                                    $sample_params = [
                                        &$sample_title, &$sample_message, &$sample_type, 
                                        &$sample_priority, &$sample_admin, &$sample_details
                                    ];
                                    
                                    $sample_stmt = sqlsrv_prepare($conn, $sample_sql, $sample_params);
                                    if ($sample_stmt && sqlsrv_execute($sample_stmt)) {
                                        echo '<div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Sample notification added successfully.
                                              </div>';
                                    }
                                }
                                
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                                      </div>';
                            }
                        }
                        ?>
                        
                        <h6>Table Structure:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Column</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>notification_id</code></td>
                                        <td>INT IDENTITY</td>
                                        <td>Primary key, auto-increment</td>
                                    </tr>
                                    <tr>
                                        <td><code>title</code></td>
                                        <td>NVARCHAR(255)</td>
                                        <td>Notification title</td>
                                    </tr>
                                    <tr>
                                        <td><code>message</code></td>
                                        <td>NVARCHAR(MAX)</td>
                                        <td>Notification message content</td>
                                    </tr>
                                    <tr>
                                        <td><code>type</code></td>
                                        <td>NVARCHAR(50)</td>
                                        <td>Type: info, success, warning, error, item_send</td>
                                    </tr>
                                    <tr>
                                        <td><code>priority</code></td>
                                        <td>NVARCHAR(20)</td>
                                        <td>Priority: low, normal, high, urgent</td>
                                    </tr>
                                    <tr>
                                        <td><code>details</code></td>
                                        <td>NVARCHAR(MAX)</td>
                                        <td>JSON details (optional)</td>
                                    </tr>
                                    <tr>
                                        <td><code>admin_username</code></td>
                                        <td>NVARCHAR(100)</td>
                                        <td>Target admin (null = all admins)</td>
                                    </tr>
                                    <tr>
                                        <td><code>created_at</code></td>
                                        <td>DATETIME2</td>
                                        <td>Creation timestamp</td>
                                    </tr>
                                    <tr>
                                        <td><code>is_read</code></td>
                                        <td>BIT</td>
                                        <td>Read status (0 = unread, 1 = read)</td>
                                    </tr>
                                    <tr>
                                        <td><code>read_at</code></td>
                                        <td>DATETIME2</td>
                                        <td>Read timestamp (nullable)</td>
                                    </tr>
                                    <tr>
                                        <td><code>expires_at</code></td>
                                        <td>DATETIME2</td>
                                        <td>Expiration date (default: 30 days)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <form method="POST" class="mt-3">
                            <button type="submit" name="create_table" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Notifications Table
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-info-circle me-2"></i>Information</h5>
                    </div>
                    <div class="card-body">
                        <h6>Features:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Auto-expiring notifications</li>
                            <li><i class="fas fa-check text-success me-2"></i>Read/unread status tracking</li>
                            <li><i class="fas fa-check text-success me-2"></i>Priority levels</li>
                            <li><i class="fas fa-check text-success me-2"></i>Admin-specific filtering</li>
                            <li><i class="fas fa-check text-success me-2"></i>JSON details support</li>
                            <li><i class="fas fa-check text-success me-2"></i>Optimized indexes</li>
                        </ul>
                        
                        <h6 class="mt-3">Indexes Created:</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-database text-primary me-2"></i>admin_username</li>
                            <li><i class="fas fa-database text-primary me-2"></i>created_at (DESC)</li>
                            <li><i class="fas fa-database text-primary me-2"></i>is_read</li>
                            <li><i class="fas fa-database text-primary me-2"></i>expires_at</li>
                            <li><i class="fas fa-database text-primary me-2"></i>type</li>
                        </ul>
                        
                        <div class="alert alert-warning mt-3">
                            <small>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Note:</strong> This will create the notifications table in your database. 
                                Make sure you have proper backup before proceeding.
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Connection</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            if ($conn) {
                                echo '<div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Database connection: <strong>OK</strong>
                                      </div>';
                                
                                // Check if table exists
                                $check_sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'notifications'";
                                $check_result = sqlsrv_query($conn, $check_sql);
                                
                                if ($check_result) {
                                    $table_exists = sqlsrv_fetch_array($check_result, SQLSRV_FETCH_ASSOC);
                                    if ($table_exists['count'] > 0) {
                                        echo '<div class="alert alert-info">
                                                <i class="fas fa-table me-2"></i>
                                                Notifications table: <strong>EXISTS</strong>
                                              </div>';
                                    } else {
                                        echo '<div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                Notifications table: <strong>NOT FOUND</strong>
                                              </div>';
                                    }
                                }
                            } else {
                                echo '<div class="alert alert-danger">
                                        <i class="fas fa-times-circle me-2"></i>
                                        Database connection: <strong>FAILED</strong>
                                      </div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    Error: ' . htmlspecialchars($e->getMessage()) . '
                                  </div>';
                        }
                        ?>
                        
                        <a href="notification_system.php?admin=<?php echo urlencode($_SESSION['admin_username']); ?>&limit=5" 
                           class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Test API
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-arrow-left me-2"></i>Next Steps</h5>
                    </div>
                    <div class="card-body">
                        <p>After creating the notifications table:</p>
                        <ol>
                            <li><strong>Test the notification system</strong> by visiting the Advanced Editor</li>
                            <li><strong>Check console</strong> for "Failed to load notifications" errors</li>
                            <li><strong>Verify API response</strong> using the "Test API" button above</li>
                            <li><strong>Add notifications</strong> using the notification system functions</li>
                        </ol>
                        
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="advanced-editor.php" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>Go to Advanced Editor
                            </a>
                            <a href="test_notifications.php" class="btn btn-success">
                                <i class="fas fa-test-tube me-2"></i>Test Notifications
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
