<?php
// API for getting dashboard statistics
require_once '../../../_app/dbinfo.inc.php';

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    $conn = db_connect();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    $stats = [
        'total_items_sent' => 0,
        'total_admin_logs' => 0,
        'total_players' => 0,
        'system_status' => 'online',
        'last_activity' => null,
        'today_items' => 0,
        'today_logs' => 0,
        'uptime' => '99.9%',
        'response_time' => '< 1s'
    ];
    
    // Get total items sent
    $itemsQuery = "SELECT COUNT(*) as count FROM item_sends";
    $itemsResult = sqlsrv_query($conn, $itemsQuery);
    if ($itemsResult) {
        $itemsRow = sqlsrv_fetch_array($itemsResult, SQLSRV_FETCH_ASSOC);
        $stats['total_items_sent'] = (int)$itemsRow['count'];
    }
    
    // Get total admin logs
    $logsQuery = "SELECT COUNT(*) as count FROM admin_logs";
    $logsResult = sqlsrv_query($conn, $logsQuery);
    if ($logsResult) {
        $logsRow = sqlsrv_fetch_array($logsResult, SQLSRV_FETCH_ASSOC);
        $stats['total_admin_logs'] = (int)$logsRow['count'];
    }
    
    // Get total players (if cabal_auth_table exists)
    $playersQuery = "SELECT COUNT(*) as count FROM cabal_auth_table";
    $playersResult = sqlsrv_query($conn, $playersQuery);
    if ($playersResult) {
        $playersRow = sqlsrv_fetch_array($playersResult, SQLSRV_FETCH_ASSOC);
        $stats['total_players'] = (int)$playersRow['count'];
    }
    
    // Get last activity
    $lastActivityQuery = "SELECT TOP 1 created_at FROM admin_logs ORDER BY id DESC, created_at DESC";
    $lastActivityResult = sqlsrv_query($conn, $lastActivityQuery);
    if ($lastActivityResult) {
        $lastActivityRow = sqlsrv_fetch_array($lastActivityResult, SQLSRV_FETCH_ASSOC);
        if ($lastActivityRow && $lastActivityRow['created_at']) {
            $stats['last_activity'] = is_object($lastActivityRow['created_at']) ? 
                $lastActivityRow['created_at']->format('Y-m-d H:i:s') : 
                $lastActivityRow['created_at'];
        }
    }
    
    // Get today's items sent
    $todayItemsQuery = "SELECT COUNT(*) as count FROM item_sends WHERE CAST(created_at AS DATE) = CAST(GETDATE() AS DATE)";
    $todayItemsResult = sqlsrv_query($conn, $todayItemsQuery);
    if ($todayItemsResult) {
        $todayItemsRow = sqlsrv_fetch_array($todayItemsResult, SQLSRV_FETCH_ASSOC);
        $stats['today_items'] = (int)$todayItemsRow['count'];
    }
    
    // Get today's admin logs
    $todayLogsQuery = "SELECT COUNT(*) as count FROM admin_logs WHERE CAST(created_at AS DATE) = CAST(GETDATE() AS DATE)";
    $todayLogsResult = sqlsrv_query($conn, $todayLogsQuery);
    if ($todayLogsResult) {
        $todayLogsRow = sqlsrv_fetch_array($todayLogsResult, SQLSRV_FETCH_ASSOC);
        $stats['today_logs'] = (int)$todayLogsRow['count'];
    }
    
    // Calculate system health
    $healthScore = 100;
    if ($stats['total_admin_logs'] > 0) {
        // Check for recent errors (last 24 hours)
        $errorQuery = "SELECT COUNT(*) as count FROM admin_logs 
                      WHERE action LIKE '%ERROR%' 
                      AND created_at >= DATEADD(hour, -24, GETDATE())";
        $errorResult = sqlsrv_query($conn, $errorQuery);
        if ($errorResult) {
            $errorRow = sqlsrv_fetch_array($errorResult, SQLSRV_FETCH_ASSOC);
            $errorCount = (int)$errorRow['count'];
            if ($errorCount > 0) {
                $healthScore -= min(50, $errorCount * 5); // Reduce health based on errors
            }
        }
    }
    
    $stats['health_score'] = $healthScore;
    $stats['system_status'] = $healthScore >= 90 ? 'excellent' : 
                             ($healthScore >= 70 ? 'good' : 
                             ($healthScore >= 50 ? 'warning' : 'critical'));
    
    // Add timestamp
    $stats['timestamp'] = date('Y-m-d H:i:s');
    $stats['timezone'] = date_default_timezone_get();
    
    sqlsrv_close($conn);
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'message' => 'Statistics retrieved successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Get Stats API Error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'stats' => [
            'total_items_sent' => 0,
            'total_admin_logs' => 0,
            'total_players' => 0,
            'system_status' => 'error',
            'last_activity' => null,
            'today_items' => 0,
            'today_logs' => 0,
            'health_score' => 0,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
}
?>
