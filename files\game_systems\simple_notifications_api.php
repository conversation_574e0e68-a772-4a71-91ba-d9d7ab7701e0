<?php
// Simple Notifications API using admin_logs table
require_once("../../_app/dbinfo.inc.php");

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function createNotification($conn, $data) {
    try {
        // Use admin_logs table for notifications
        $sql = "INSERT INTO admin_logs (
            admin_username, action, target_player, details, ip_address, created_at
        ) VALUES (?, ?, ?, ?, ?, GETDATE())";
        
        $params = [
            $data['admin_username'] ?? 'System',
            $data['action'] ?? 'Notification',
            $data['target_player'] ?? null,
            $data['message'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
        
        $stmt = sqlsrv_prepare($conn, $sql, $params);
        if ($stmt === false) {
            throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        $result = sqlsrv_execute($stmt);
        if ($result === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        return [
            'success' => true,
            'message' => 'Notification logged successfully'
        ];
        
    } catch (Exception $e) {
        error_log("Create Notification Error: " . $e->getMessage());
        throw $e;
    }
}

function getNotifications($conn, $limit = 10) {
    try {
        // Get recent admin logs as notifications
        $sql = "SELECT TOP ? id, admin_username, action, target_player, details, 
                ip_address, created_at
                FROM admin_logs 
                ORDER BY created_at DESC";
        
        $stmt = sqlsrv_prepare($conn, $sql, [$limit]);
        if ($stmt === false) {
            throw new Exception('Failed to prepare statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        $result = sqlsrv_execute($stmt);
        if ($result === false) {
            throw new Exception('Failed to execute statement: ' . print_r(sqlsrv_errors(), true));
        }
        
        $notifications = [];
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            // Convert datetime to string safely
            if ($row['created_at']) {
                if (is_object($row['created_at']) && method_exists($row['created_at'], 'format')) {
                    $row['created_at'] = $row['created_at']->format('Y-m-d H:i:s');
                } else {
                    $row['created_at'] = (string)$row['created_at'];
                }
            }
            
            $notifications[] = [
                'id' => $row['id'],
                'title' => $row['action'],
                'message' => $row['details'],
                'admin_username' => $row['admin_username'],
                'target_player' => $row['target_player'],
                'created_at' => $row['created_at'],
                'type' => 'admin_log'
            ];
        }
        
        return [
            'success' => true,
            'notifications' => $notifications,
            'count' => count($notifications)
        ];
        
    } catch (Exception $e) {
        error_log("Get Notifications Error: " . $e->getMessage());
        throw $e;
    }
}

// Handle different request methods
try {
    // Get database connection
    $conn = db_connect();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Create notification
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        $result = createNotification($conn, $data);
        echo json_encode($result);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get notifications
        $limit = (int)($_GET['limit'] ?? 10);
        $limit = max(1, min($limit, 100)); // Limit between 1 and 100
        
        $result = getNotifications($conn, $limit);
        echo json_encode($result);
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn) && $conn) {
        sqlsrv_close($conn);
    }
}
?>
