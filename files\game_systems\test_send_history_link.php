<?php
// Test Send History Link
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Send History Link</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-history me-2"></i>Test Send History Link</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Changes Made</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Send History Button Updated:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-edit text-success me-2"></i>
                                Modified <code>showItemHistory()</code> function
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-link text-info me-2"></i>
                                Now opens <code>?url=game_systems/index</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-external-link-alt text-primary me-2"></i>
                                Opens in new tab with <code>_blank</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-trash text-warning me-2"></i>
                                Removed old SweetAlert popup code
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Send History</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่าปุ่ม "Send History" เปิดไปที่หน้า Game Systems Index</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testHistoryLink" class="btn btn-primary">
                                <i class="fas fa-history me-2"></i>Test Send History Link
                            </button>
                            
                            <a href="advanced-editor.php" class="btn btn-success" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                            </a>
                            
                            <a href="?url=game_systems/index" class="btn btn-info" target="_blank">
                                <i class="fas fa-list me-2"></i>Open Game Systems Index
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Old Code):</h6>
                                <pre class="bg-light p-3 rounded small">function showItemHistory() {
    addToConsole('📜 Loading send history...', 'info');
    
    try {
        const history = JSON.parse(localStorage.getItem('itemSendHistory') || '[]');
        
        if (history.length === 0) {
            Swal.fire({
                icon: 'info',
                title: 'No History',
                text: 'No item send history found.',
                confirmButtonColor: '#007bff'
            });
            return;
        }
        
        // Generate history table...
        // SweetAlert popup with table...
    } catch (error) {
        // Error handling...
    }
}</pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (New Code):</h6>
                                <pre class="bg-light p-3 rounded small">function showItemHistory() {
    addToConsole('📜 Opening send history page...', 'info');
    
    // Open the game systems index page which contains the send history
    window.open('?url=game_systems/index', '_blank');
    
    addToConsole('✅ Send history page opened in new tab', 'success');
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Expected Behavior</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>When clicking "Send History" button:</h6>
                            <ol class="mb-0">
                                <li>✅ Console shows: "📜 Opening send history page..."</li>
                                <li>✅ New tab opens with URL: <code>?url=game_systems/index</code></li>
                                <li>✅ Console shows: "✅ Send history page opened in new tab"</li>
                                <li>✅ Game Systems Index page loads with send history</li>
                                <li>✅ No SweetAlert popup appears</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6>How to Test:</h6>
                            <ol class="mb-0">
                                <li>เปิด Advanced Editor</li>
                                <li>คลิกปุ่ม "Send History" (ไอคอน history)</li>
                                <li>ตรวจสอบว่าเปิด tab ใหม่ไปที่ Game Systems Index</li>
                                <li>ดู Console messages ใน Advanced Editor</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-benefits me-2"></i>Benefits</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">✅ Advantages:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-tachometer-alt text-success me-2"></i>
                                        Faster loading (no localStorage processing)
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-window-restore text-info me-2"></i>
                                        Opens in new tab (doesn't interrupt workflow)
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-database text-primary me-2"></i>
                                        Shows real database data (not just localStorage)
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-tools text-warning me-2"></i>
                                        Access to full management features
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">📋 Features Available:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-search text-primary me-2"></i>
                                        Advanced search and filtering
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-sort text-success me-2"></i>
                                        Sortable columns
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-download text-info me-2"></i>
                                        Export functionality
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-edit text-warning me-2"></i>
                                        Edit/Delete capabilities
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#testHistoryLink').click(function() {
                const resultsDiv = $('#testResults');
                
                // Simulate the function behavior
                console.log('📜 Opening send history page...');
                
                // Open the link
                window.open('?url=game_systems/index', '_blank');
                
                console.log('✅ Send history page opened in new tab');
                
                // Show result
                resultsDiv.html(`
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check me-2"></i>Test Successful!</h6>
                        <p><strong>Action:</strong> Opened Game Systems Index in new tab</p>
                        <p><strong>URL:</strong> <code>?url=game_systems/index</code></p>
                        <p class="mb-0"><strong>Status:</strong> ✅ Link working correctly</p>
                    </div>
                `);
            });
        });
    </script>
</body>
</html>
