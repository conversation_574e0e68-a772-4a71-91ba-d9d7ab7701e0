<?php
// Test Notifications API
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Try to find dbinfo.inc.php
$dbinfo_paths = [
    '../../_app/dbinfo.inc.php',
    '../../../_app/dbinfo.inc.php',
    dirname(__DIR__, 2) . '/_app/dbinfo.inc.php'
];

$dbinfo_loaded = false;
foreach ($dbinfo_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $dbinfo_loaded = true;
        break;
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notifications API</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-bell me-2"></i>Test Notifications API</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Issues Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Notifications API Fixes:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                Fixed session_start() conflict
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-folder text-info me-2"></i>
                                Added multiple path detection for dbinfo.inc.php
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-database text-primary me-2"></i>
                                Improved database connection handling
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                Enhanced error handling and logging
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">🔧 System Status:</h6>
                        <div class="bg-light p-3 rounded">
                            <p><strong>Session Status:</strong> 
                                <span class="badge bg-<?php echo session_status() === PHP_SESSION_ACTIVE ? 'success' : 'danger'; ?>">
                                    <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?>
                                </span>
                            </p>
                            <p><strong>Database Config:</strong> 
                                <span class="badge bg-<?php echo $dbinfo_loaded ? 'success' : 'danger'; ?>">
                                    <?php echo $dbinfo_loaded ? 'Loaded' : 'Not Found'; ?>
                                </span>
                            </p>
                            <p class="mb-0"><strong>DB Function:</strong> 
                                <span class="badge bg-<?php echo function_exists('db_connect') ? 'success' : 'danger'; ?>">
                                    <?php echo function_exists('db_connect') ? 'Available' : 'Not Available'; ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test API</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า Notifications API ทำงานได้โดยไม่มี error</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testApiConnection" class="btn btn-primary">
                                <i class="fas fa-link me-2"></i>Test API Connection
                            </button>
                            
                            <button id="testCreateNotification" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Test Create Notification
                            </button>
                            
                            <button id="testGetNotifications" class="btn btn-info">
                                <i class="fas fa-list me-2"></i>Test Get Notifications
                            </button>
                            
                            <a href="notifications_api.php" class="btn btn-warning" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open API Direct
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Problems):</h6>
                                <pre class="bg-light p-3 rounded small">// Session conflict
session_start();

// Fixed path only
require_once '../../_app/dbinfo.inc.php';

// Direct db_connect call
$conn = db_connect();</pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (Fixed):</h6>
                                <pre class="bg-light p-3 rounded small">// Safe session start
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Multiple path detection
$dbinfo_paths = [...];
foreach ($dbinfo_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        break;
    }
}

// Safe connection function
function getDbConnection() {
    try {
        if (function_exists('db_connect')) {
            $conn = db_connect();
            if (!$conn) {
                throw new Exception('Connection failed');
            }
            return $conn;
        }
    } catch (Exception $e) {
        error_log("DB error: " . $e->getMessage());
        return false;
    }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Expected Behavior</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6>After Fixes:</h6>
                            <ul class="mb-0">
                                <li>✅ No "session already active" warnings</li>
                                <li>✅ No "Failed to open stream" errors</li>
                                <li>✅ No "Fatal error" messages</li>
                                <li>✅ API responds with proper JSON</li>
                                <li>✅ Database connection works</li>
                                <li>✅ Error handling is graceful</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6>How to Verify:</h6>
                            <ol class="mb-0">
                                <li>เปิดหน้า Dashboard ใหม่ (ไม่ควรมี error)</li>
                                <li>ทดสอบ API ด้วยปุ่มด้านบน</li>
                                <li>ตรวจสอบ Browser Console (ไม่ควรมี error)</li>
                                <li>ดู PHP Error Log (ไม่ควรมี fatal error)</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-tools me-2"></i>Troubleshooting</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔍 If Still Having Issues:</h6>
                                <ul class="small">
                                    <li>Check if dbinfo.inc.php exists in any of these paths:
                                        <ul>
                                            <li><code>../../_app/dbinfo.inc.php</code></li>
                                            <li><code>../../../_app/dbinfo.inc.php</code></li>
                                            <li><code>/adminpanel-cabal35/_app/dbinfo.inc.php</code></li>
                                        </ul>
                                    </li>
                                    <li>Verify db_connect() function is defined</li>
                                    <li>Check PHP error logs for details</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>📋 Debug Information:</h6>
                                <div class="bg-dark text-light p-3 rounded small">
                                    <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                                    <strong>Session ID:</strong> <?php echo session_id() ?: 'None'; ?><br>
                                    <strong>Current Path:</strong> <?php echo __DIR__; ?><br>
                                    <strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        $(document).ready(function() {
            $('#testApiConnection').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing API connection...</div>');
                
                try {
                    const response = await fetch('notifications_api.php', {
                        method: 'GET'
                    });
                    
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>API Connection Successful!</h6>
                                <p><strong>Status:</strong> ${response.status}</p>
                                <p class="mb-0"><strong>Response:</strong> API is responding properly</p>
                            </div>
                        `);
                        showNotification('API connection test successful!', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('API test error:', error);
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>API Connection Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                    showNotification('API connection test failed', 'error');
                }
            });
            
            $('#testCreateNotification').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing create notification...</div>');
                
                try {
                    const testData = {
                        title: 'Test Notification',
                        message: 'This is a test notification from the API test',
                        type: 'info',
                        created_by: 'test_user'
                    };
                    
                    const response = await fetch('notifications_api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(testData)
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Create Notification Test Successful!</h6>
                                <p><strong>Success:</strong> ${data.success}</p>
                                <p class="mb-0"><strong>Message:</strong> ${data.message || 'Notification created'}</p>
                            </div>
                        `);
                        showNotification('Create notification test successful!', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>Create Notification Test Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                    showNotification('Create notification test failed', 'error');
                }
            });
            
            $('#testGetNotifications').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing get notifications...</div>');
                
                try {
                    const response = await fetch('notifications_api.php?user=test_user', {
                        method: 'GET'
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Get Notifications Test Successful!</h6>
                                <p><strong>Success:</strong> ${data.success}</p>
                                <p class="mb-0"><strong>Count:</strong> ${data.count || 0} notifications</p>
                            </div>
                        `);
                        showNotification('Get notifications test successful!', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times me-2"></i>Get Notifications Test Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                    showNotification('Get notifications test failed', 'error');
                }
            });
            
            // Auto-run connection test
            setTimeout(() => {
                $('#testApiConnection').click();
            }, 1000);
        });
    </script>
</body>
</html>
