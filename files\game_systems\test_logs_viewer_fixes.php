<?php
// Test Logs Viewer Fixes
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logs Viewer Fixes</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-bug me-2"></i>Test Logs Viewer Fixes</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Issues Fixed</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Advanced Editor Logs Viewer Fixes:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-filter text-success me-2"></i>
                                Removed restrictive filter condition
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-sort text-info me-2"></i>
                                Fixed ORDER BY to show latest records first
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-sync text-primary me-2"></i>
                                Added auto-refresh every 30 seconds
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-clock text-warning me-2"></i>
                                Added "Last updated" timestamp display
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Logs API</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่า Logs API ส่งข้อมูลล่าสุดได้ถูกต้อง</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="testLogsAPI" class="btn btn-primary">
                                <i class="fas fa-api me-2"></i>Test Logs API
                            </button>
                            
                            <button id="testLatestLogs" class="btn btn-success">
                                <i class="fas fa-clock me-2"></i>Test Latest 5 Logs
                            </button>
                            
                            <a href="advanced_editor_logs_viewer.php" class="btn btn-info" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Logs Viewer
                            </a>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Code Changes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Problems):</h6>
                                <pre class="bg-light p-3 rounded small">// Restrictive filter
'filter_condition' => "details LIKE '%log_level%'"

// Basic ORDER BY
$sql .= " ORDER BY created_at DESC";

// No auto-refresh
// No last updated display</pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (Fixed):</h6>
                                <pre class="bg-light p-3 rounded small">// No filter restriction
'filter_condition' => null

// Better ORDER BY
$sql .= " ORDER BY id DESC, created_at DESC";

// Auto-refresh every 30 seconds
setInterval(loadLogs, 30000);

// Last updated timestamp
$('#lastUpdated').text(now);</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-database me-2"></i>Recent Admin Logs</h5>
                    </div>
                    <div class="card-body">
                        <div id="recentLogs">
                            <?php
                            try {
                                $conn = db_connect();
                                if ($conn) {
                                    $query = "SELECT TOP 10 id, admin_username, action, target_player, details, ip_address, created_at 
                                             FROM admin_logs 
                                             ORDER BY id DESC, created_at DESC";
                                    $result = sqlsrv_query($conn, $query);
                                    
                                    if ($result && sqlsrv_has_rows($result)) {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead class="table-dark">';
                                        echo '<tr><th>ID</th><th>User ID</th><th>Action</th><th>Target</th><th>Details</th><th>IP</th><th>Created At</th></tr>';
                                        echo '</thead><tbody>';
                                        
                                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                            $createdAt = is_object($row['created_at']) ? 
                                                $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'];
                                            $details = $row['details'] ? 
                                                (strlen($row['details']) > 30 ? substr($row['details'], 0, 30) . '...' : $row['details']) : '-';
                                            
                                            echo '<tr>';
                                            echo '<td><strong>' . htmlspecialchars($row['id']) . '</strong></td>';
                                            echo '<td><code>' . htmlspecialchars($row['admin_username']) . '</code></td>';
                                            echo '<td>' . htmlspecialchars($row['action']) . '</td>';
                                            echo '<td>' . htmlspecialchars($row['target_player'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars($details) . '</small></td>';
                                            echo '<td>' . htmlspecialchars($row['ip_address'] ?: '-') . '</td>';
                                            echo '<td>' . htmlspecialchars($createdAt) . '</td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody></table>';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-warning">No admin logs found</div>';
                                    }
                                    
                                    sqlsrv_close($conn);
                                } else {
                                    echo '<div class="alert alert-danger">Database connection failed</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Expected Improvements</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">✅ Now Shows:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-database text-success me-2"></i>
                                        All admin_logs records (not filtered)
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-sort-numeric-down text-info me-2"></i>
                                        Latest records first (ORDER BY id DESC)
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-sync text-primary me-2"></i>
                                        Auto-refreshes every 30 seconds
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-clock text-warning me-2"></i>
                                        Last updated timestamp
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">📋 How to Verify:</h6>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">เปิด Advanced Editor Logs Viewer</li>
                                    <li class="list-group-item">ตรวจสอบว่าแสดงข้อมูลล่าสุด</li>
                                    <li class="list-group-item">ดู "Last updated" timestamp</li>
                                    <li class="list-group-item">รอ 30 วินาที ดูการ auto-refresh</li>
                                    <li class="list-group-item">ส่งไอเทมใน Advanced Editor</li>
                                    <li class="list-group-item">ตรวจสอบว่าปรากฏใน logs ทันที</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#testLogsAPI').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing logs API...</div>');
                
                try {
                    const response = await fetch('log_advanced_editor.php?limit=10');
                    const data = await response.json();
                    
                    if (data.success) {
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>API Test Successful!</h6>
                                <p><strong>Records Found:</strong> ${data.logs.length}</p>
                                <p><strong>Latest Record ID:</strong> ${data.logs[0]?.id || 'None'}</p>
                                <p><strong>Latest Action:</strong> ${data.logs[0]?.action || 'None'}</p>
                                <p class="mb-0"><strong>Request Time:</strong> ${data.debug?.request_time || 'Unknown'}</p>
                            </div>
                        `);
                    } else {
                        resultsDiv.html(`
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times me-2"></i>API Test Failed!</h6>
                                <p>Error: ${data.error}</p>
                            </div>
                        `);
                    }
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Request Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                }
            });
            
            $('#testLatestLogs').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing latest 5 logs...</div>');
                
                try {
                    const response = await fetch('log_advanced_editor.php?limit=5');
                    const data = await response.json();
                    
                    if (data.success && data.logs.length > 0) {
                        let logsHtml = '<div class="alert alert-success"><h6>Latest 5 Logs:</h6><ul class="mb-0">';
                        data.logs.forEach((log, index) => {
                            logsHtml += `<li><strong>ID ${log.id}:</strong> ${log.action} - ${log.admin_username} (${log.created_at})</li>`;
                        });
                        logsHtml += '</ul></div>';
                        resultsDiv.html(logsHtml);
                    } else {
                        resultsDiv.html('<div class="alert alert-warning">No logs found</div>');
                    }
                } catch (error) {
                    resultsDiv.html(`<div class="alert alert-danger">Error: ${error.message}</div>`);
                }
            });
            
            // Auto-run API test
            setTimeout(() => {
                $('#testLogsAPI').click();
            }, 1000);
        });
    </script>
</body>
</html>
