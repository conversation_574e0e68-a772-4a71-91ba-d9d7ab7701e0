<?php
// Check all tables in database
 $zpanel->checkSession(true);

header('Content-Type: text/html; charset=UTF-8');

echo "<h2>Database Tables Check</h2>";

try {
    // Test database connection
    $conn = db_connect();
    
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // List all tables in database
    $tablesQuery = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME";
    $tablesResult = sqlsrv_query($conn, $tablesQuery);
    
    if ($tablesResult) {
        echo "<h3>All Tables in Database:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Table Name</th><th>Record Count</th><th>Status</th></tr>";
        
        $requiredTables = ['admin_logs', 'item_sends', 'items', 'notifications'];
        $foundTables = [];
        
        while ($table = sqlsrv_fetch_array($tablesResult, SQLSRV_FETCH_ASSOC)) {
            $tableName = $table['TABLE_NAME'];
            $foundTables[] = $tableName;
            
            // Count records in each table
            $countQuery = "SELECT COUNT(*) as count FROM [$tableName]";
            $countResult = sqlsrv_query($conn, $countQuery);
            $count = 0;
            
            if ($countResult) {
                $countRow = sqlsrv_fetch_array($countResult, SQLSRV_FETCH_ASSOC);
                $count = $countRow['count'];
            }
            
            $isRequired = in_array($tableName, $requiredTables);
            $status = $isRequired ? '✅ Required' : '📋 Other';
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($tableName) . "</strong></td>";
            echo "<td>" . number_format($count) . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for missing required tables
        echo "<h3>Required Tables Status:</h3>";
        foreach ($requiredTables as $required) {
            if (in_array($required, $foundTables)) {
                echo "<p style='color: green;'>✅ $required - EXISTS</p>";
            } else {
                echo "<p style='color: red;'>❌ $required - MISSING</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Failed to get table list</p>";
    }
    
    // Test specific queries for each required table
    echo "<h3>Table Access Tests:</h3>";
    
    $testTables = [
        'admin_logs' => "SELECT TOP 5 id, admin_username, action, created_at FROM admin_logs ORDER BY created_at DESC",
        'item_sends' => "SELECT TOP 5 id, player_username, item_name, status, created_at FROM item_sends ORDER BY created_at DESC",
        'items' => "SELECT TOP 5 id, name, category, type FROM items ORDER BY id",
        'notifications' => "SELECT TOP 5 id, title, type, target_user, created_by, created_at FROM notifications ORDER BY created_at DESC"
    ];
    
    foreach ($testTables as $tableName => $query) {
        echo "<h4>Testing $tableName:</h4>";
        
        $result = sqlsrv_query($conn, $query);
        if ($result) {
            $rows = [];
            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $rows[] = $row;
            }
            
            if (count($rows) > 0) {
                echo "<p style='color: green;'>✅ $tableName accessible - " . count($rows) . " sample records found</p>";
                
                // Show sample data
                echo "<table border='1' style='border-collapse: collapse; font-size: 12px;'>";
                $headers = array_keys($rows[0]);
                echo "<tr>";
                foreach ($headers as $header) {
                    echo "<th>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                
                foreach ($rows as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        if (is_object($value) && method_exists($value, 'format')) {
                            $value = $value->format('Y-m-d H:i:s');
                        }
                        echo "<td>" . htmlspecialchars(substr($value, 0, 30)) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p style='color: orange;'>⚠️ $tableName accessible but empty</p>";
            }
        } else {
            $errors = sqlsrv_errors();
            echo "<p style='color: red;'>❌ $tableName not accessible</p>";
            echo "<pre>" . print_r($errors, true) . "</pre>";
        }
    }
    
    // Test API endpoints
    echo "<h3>API Endpoint Tests:</h3>";
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    
    $apiTests = [
        'Advanced Editor Logs' => $baseUrl . '/log_advanced_editor.php?limit=5',
        'Advanced Editor Logs Viewer' => $baseUrl . '/advanced_editor_logs_viewer.php'
    ];
    
    foreach ($apiTests as $name => $url) {
        echo "<p><strong>$name:</strong> <a href='$url' target='_blank'>$url</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} finally {
    if (isset($conn)) {
        sqlsrv_close($conn);
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 4px; }
</style>
