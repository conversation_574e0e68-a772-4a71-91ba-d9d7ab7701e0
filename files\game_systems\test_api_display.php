<?php
// Test API Display Issues
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Display Issues</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-bug me-2"></i>Test API Display Issues</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Problem Identified</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6>🚨 API Showing Code as Text</h6>
                            <p>The <code>notifications_api.php</code> is displaying PHP code instead of executing it.</p>
                        </div>
                        
                        <h6>Possible Causes:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-server text-danger me-2"></i>
                                Web server not processing PHP files
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-route text-warning me-2"></i>
                                URL routing issues in admin panel
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-file-code text-info me-2"></i>
                                Content-Type header conflicts
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-cog text-secondary me-2"></i>
                                PHP configuration issues
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-tools me-2"></i>Solutions</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Created Alternative API:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-file text-success me-2"></i>
                                <code>api_notifications.php</code> - Simplified version
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-info me-2"></i>
                                Better error handling
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-bolt text-primary me-2"></i>
                                Immediate header setting
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-warning me-2"></i>
                                Simplified response format
                            </li>
                        </ul>
                        
                        <div class="d-grid gap-2 mt-3">
                            <button id="testOriginalAPI" class="btn btn-danger">
                                <i class="fas fa-bug me-2"></i>Test Original API
                            </button>
                            <button id="testNewAPI" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Test New API
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-comparison me-2"></i>API Comparison</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Original API (notifications_api.php):</h6>
                                <div class="bg-light p-3 rounded">
                                    <p><strong>URL:</strong> <code>notifications_api.php</code></p>
                                    <p><strong>Issue:</strong> Shows PHP code as text</p>
                                    <p><strong>Cause:</strong> Routing/server configuration</p>
                                    <button class="btn btn-sm btn-outline-danger" onclick="window.open('notifications_api.php', '_blank')">
                                        <i class="fas fa-external-link-alt me-1"></i>Test Direct
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ New API (api_notifications.php):</h6>
                                <div class="bg-light p-3 rounded">
                                    <p><strong>URL:</strong> <code>api_notifications.php</code></p>
                                    <p><strong>Status:</strong> Should work properly</p>
                                    <p><strong>Features:</strong> Simplified, robust</p>
                                    <button class="btn btn-sm btn-outline-success" onclick="window.open('api_notifications.php', '_blank')">
                                        <i class="fas fa-external-link-alt me-1"></i>Test Direct
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Click the test buttons above to check API responses
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Troubleshooting Steps</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔍 Diagnosis Steps:</h6>
                                <ol class="small">
                                    <li>Check if PHP is enabled on server</li>
                                    <li>Verify file permissions</li>
                                    <li>Test direct file access</li>
                                    <li>Check admin panel routing</li>
                                    <li>Review server error logs</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>🛠️ Solutions:</h6>
                                <ol class="small">
                                    <li>Use the new <code>api_notifications.php</code></li>
                                    <li>Access API directly (not through routing)</li>
                                    <li>Check web server configuration</li>
                                    <li>Verify PHP file associations</li>
                                    <li>Test with simple PHP file first</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <h6>💡 Recommendation:</h6>
                            <p class="mb-0">Use the new <code>api_notifications.php</code> file which is designed to work better with the admin panel system and has improved error handling.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-code me-2"></i>Server Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>PHP Information:</h6>
                                <div class="bg-dark text-light p-3 rounded small">
                                    <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                                    <strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                                    <strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?><br>
                                    <strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>File Information:</h6>
                                <div class="bg-dark text-light p-3 rounded small">
                                    <strong>Current File:</strong> <?php echo __FILE__; ?><br>
                                    <strong>Current Dir:</strong> <?php echo __DIR__; ?><br>
                                    <strong>Original API:</strong> <?php echo file_exists(__DIR__ . '/notifications_api.php') ? 'Exists' : 'Not Found'; ?><br>
                                    <strong>New API:</strong> <?php echo file_exists(__DIR__ . '/api_notifications.php') ? 'Exists' : 'Not Found'; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#testOriginalAPI').click(async function() {
            showResult('Testing Original API...', 'Please wait...', 'info');
            
            try {
                const response = await fetch('notifications_api.php');
                const text = await response.text();
                
                // Check if response looks like PHP code
                if (text.includes('<?php') || text.includes('function ') || text.includes('$conn')) {
                    showResult('Original API Failed', `
                        <p><strong>Issue:</strong> API is returning PHP code as text instead of executing it.</p>
                        <p><strong>Response Preview:</strong></p>
                        <pre class="bg-light p-2 rounded small">${text.substring(0, 200)}...</pre>
                        <p><strong>Solution:</strong> Use the new API instead.</p>
                    `, 'error');
                } else {
                    try {
                        const data = JSON.parse(text);
                        showResult('Original API Success', `
                            <p><strong>Status:</strong> API is working correctly!</p>
                            <p><strong>Response:</strong> Valid JSON received</p>
                            <pre class="bg-light p-2 rounded small">${JSON.stringify(data, null, 2)}</pre>
                        `, 'success');
                    } catch (e) {
                        showResult('Original API Error', `
                            <p><strong>Issue:</strong> Invalid JSON response</p>
                            <p><strong>Response:</strong></p>
                            <pre class="bg-light p-2 rounded small">${text}</pre>
                        `, 'error');
                    }
                }
            } catch (error) {
                showResult('Original API Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                    <p>Could not connect to the API endpoint.</p>
                `, 'error');
            }
        });

        $('#testNewAPI').click(async function() {
            showResult('Testing New API...', 'Please wait...', 'info');
            
            try {
                const response = await fetch('api_notifications.php');
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    showResult('New API Success', `
                        <p><strong>Status:</strong> New API is working correctly!</p>
                        <p><strong>Response:</strong></p>
                        <pre class="bg-light p-2 rounded small">${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } catch (e) {
                    if (text.includes('<?php')) {
                        showResult('New API Failed', `
                            <p><strong>Issue:</strong> New API also showing code as text</p>
                            <p><strong>Cause:</strong> Server configuration issue</p>
                            <p><strong>Response Preview:</strong></p>
                            <pre class="bg-light p-2 rounded small">${text.substring(0, 200)}...</pre>
                        `, 'error');
                    } else {
                        showResult('New API Error', `
                            <p><strong>Issue:</strong> Invalid JSON response</p>
                            <p><strong>Response:</strong></p>
                            <pre class="bg-light p-2 rounded small">${text}</pre>
                        `, 'error');
                    }
                }
            } catch (error) {
                showResult('New API Error', `
                    <p><strong>Network Error:</strong> ${error.message}</p>
                    <p>Could not connect to the API endpoint.</p>
                `, 'error');
            }
        });

        // Auto-test both APIs
        setTimeout(() => {
            $('#testOriginalAPI').click();
        }, 1000);
    </script>
</body>
</html>
