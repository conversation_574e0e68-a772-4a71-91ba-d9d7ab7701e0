<?php
// Test SweetAlert Animations
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SweetAlert Animations</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="bg-light">
    <div class="container py-4">
        <h1><i class="fas fa-magic me-2 text-primary"></i>Test SweetAlert Animations</h1>
        <p class="lead">ทดสอบ SweetAlert2 animations ใน Advanced Editor</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-play me-2"></i>Basic Animations</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="testSuccess" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Success Alert
                            </button>
                            
                            <button id="testError" class="btn btn-danger">
                                <i class="fas fa-times me-2"></i>Error Alert
                            </button>
                            
                            <button id="testWarning" class="btn btn-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>Warning Alert
                            </button>
                            
                            <button id="testInfo" class="btn btn-info">
                                <i class="fas fa-info-circle me-2"></i>Info Alert
                            </button>
                            
                            <button id="testQuestion" class="btn btn-secondary">
                                <i class="fas fa-question-circle me-2"></i>Question Alert
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-cogs me-2"></i>Advanced Features</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="testToast" class="btn btn-primary">
                                <i class="fas fa-bell me-2"></i>Toast Notification
                            </button>
                            
                            <button id="testLoading" class="btn btn-info">
                                <i class="fas fa-spinner me-2"></i>Loading Animation
                            </button>
                            
                            <button id="testConfirm" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Confirmation Dialog
                            </button>
                            
                            <button id="testInput" class="btn btn-success">
                                <i class="fas fa-edit me-2"></i>Input Dialog
                            </button>
                            
                            <button id="testCustom" class="btn btn-dark">
                                <i class="fas fa-star me-2"></i>Custom Animation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-code me-2"></i>Advanced Editor Style Tests</h5>
                    </div>
                    <div class="card-body">
                        <p>ทดสอบ SweetAlert styles ที่ใช้ใน Advanced Editor:</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button id="testPlayerList" class="btn btn-primary">
                                        <i class="fas fa-users me-2"></i>Player List Modal
                                    </button>
                                    
                                    <button id="testItemPreview" class="btn btn-success">
                                        <i class="fas fa-eye me-2"></i>Item Preview
                                    </button>
                                    
                                    <button id="testSendItem" class="btn btn-warning">
                                        <i class="fas fa-paper-plane me-2"></i>Send Item Process
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid gap-2">
                                    <button id="testAdvancedSearch" class="btn btn-info">
                                        <i class="fas fa-search me-2"></i>Advanced Search
                                    </button>
                                    
                                    <button id="testExportData" class="btn btn-secondary">
                                        <i class="fas fa-download me-2"></i>Export Data
                                    </button>
                                    
                                    <button id="testClearHistory" class="btn btn-danger">
                                        <i class="fas fa-trash-alt me-2"></i>Clear History
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Animation Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="animationStatus" class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>SweetAlert2 Status</h6>
                            <div id="swalStatus">Checking SweetAlert2 availability...</div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Common Issues</h6>
                            <ul class="mb-0">
                                <li><strong>No animations:</strong> SweetAlert2 library not loaded</li>
                                <li><strong>Broken styles:</strong> CSS conflicts or missing dependencies</li>
                                <li><strong>Console errors:</strong> JavaScript errors preventing execution</li>
                                <li><strong>Slow animations:</strong> Browser performance or CSS issues</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex">
                            <a href="advanced-editor.php" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>Test in Advanced Editor
                            </a>
                            <button id="checkSwalFeatures" class="btn btn-info">
                                <i class="fas fa-search me-2"></i>Check Features
                            </button>
                            <button id="testAllAnimations" class="btn btn-success">
                                <i class="fas fa-play-circle me-2"></i>Test All
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Check SweetAlert2 availability
        function checkSweetAlert() {
            const status = document.getElementById('swalStatus');
            
            if (typeof Swal !== 'undefined') {
                status.innerHTML = `
                    <div class="text-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>SweetAlert2 is loaded and ready!</strong><br>
                        <small>Version: ${Swal.version || 'Unknown'}</small>
                    </div>
                `;
                document.getElementById('animationStatus').className = 'alert alert-success';
            } else {
                status.innerHTML = `
                    <div class="text-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        <strong>SweetAlert2 is NOT loaded!</strong><br>
                        <small>Animations will not work without this library.</small>
                    </div>
                `;
                document.getElementById('animationStatus').className = 'alert alert-danger';
            }
        }

        // Basic alert tests
        $('#testSuccess').click(() => {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'This is a success animation test',
                showConfirmButton: true,
                timer: 3000
            });
        });

        $('#testError').click(() => {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'This is an error animation test',
                showConfirmButton: true
            });
        });

        $('#testWarning').click(() => {
            Swal.fire({
                icon: 'warning',
                title: 'Warning!',
                text: 'This is a warning animation test',
                showConfirmButton: true
            });
        });

        $('#testInfo').click(() => {
            Swal.fire({
                icon: 'info',
                title: 'Information',
                text: 'This is an info animation test',
                showConfirmButton: true
            });
        });

        $('#testQuestion').click(() => {
            Swal.fire({
                icon: 'question',
                title: 'Question?',
                text: 'This is a question animation test',
                showConfirmButton: true
            });
        });

        // Advanced feature tests
        $('#testToast').click(() => {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            });

            Toast.fire({
                icon: 'success',
                title: 'Toast notification test!'
            });
        });

        $('#testLoading').click(() => {
            Swal.fire({
                title: 'Loading...',
                text: 'Please wait while we process your request',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Auto close after 3 seconds
            setTimeout(() => {
                Swal.close();
                Swal.fire({
                    icon: 'success',
                    title: 'Complete!',
                    text: 'Loading animation test finished',
                    timer: 2000
                });
            }, 3000);
        });

        $('#testConfirm').click(() => {
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Deleted!',
                        text: 'Your file has been deleted.',
                        timer: 2000
                    });
                }
            });
        });

        $('#testInput').click(() => {
            Swal.fire({
                title: 'Enter your name',
                input: 'text',
                inputLabel: 'Your name',
                inputPlaceholder: 'Enter your name here...',
                showCancelButton: true,
                inputValidator: (value) => {
                    if (!value) {
                        return 'You need to write something!'
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: `Hello, ${result.value}!`,
                        text: 'Input dialog test completed',
                        timer: 2000
                    });
                }
            });
        });

        $('#testCustom').click(() => {
            Swal.fire({
                title: 'Custom Animation',
                text: 'This alert has custom styling and animation',
                icon: 'info',
                background: '#f0f0f0',
                color: '#333',
                confirmButtonColor: '#6c5ce7',
                showClass: {
                    popup: 'animate__animated animate__fadeInDown'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOutUp'
                }
            });
        });

        // Advanced Editor style tests
        $('#testPlayerList').click(() => {
            Swal.fire({
                title: 'Player List (Demo)',
                html: `
                    <div class="text-left">
                        <div class="mb-3">
                            <input type="text" class="form-control" placeholder="Search players..." id="playerSearch">
                        </div>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Username</th>
                                        <th>Level</th>
                                        <th>Class</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>Player1</td><td>150</td><td>Warrior</td><td><button class="btn btn-sm btn-primary">Select</button></td></tr>
                                    <tr><td>Player2</td><td>120</td><td>Wizard</td><td><button class="btn btn-sm btn-primary">Select</button></td></tr>
                                    <tr><td>Player3</td><td>95</td><td>Blader</td><td><button class="btn btn-sm btn-primary">Select</button></td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                `,
                width: '600px',
                showCancelButton: true,
                confirmButtonText: 'Close',
                cancelButtonText: 'Refresh'
            });
        });

        $('#testItemPreview').click(() => {
            Swal.fire({
                title: 'Item Preview (Demo)',
                html: `
                    <div class="text-left">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Item Information:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Name:</strong> Legendary Sword</li>
                                    <li><strong>ID:</strong> 12345</li>
                                    <li><strong>Type:</strong> Weapon</li>
                                    <li><strong>Level:</strong> 150</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Calculated Codes:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Item Code:</strong> 0x12345ABC</li>
                                    <li><strong>Options Code:</strong> 0x67890DEF</li>
                                    <li><strong>Hex Code:</strong> 12345ABC67890DEF</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `,
                width: '500px',
                confirmButtonText: 'Send Item',
                cancelButtonText: 'Close',
                showCancelButton: true
            });
        });

        $('#testSendItem').click(() => {
            // Show loading first
            Swal.fire({
                title: 'Sending Item...',
                text: 'Please wait while we send the item to the player',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Simulate sending process
            setTimeout(() => {
                Swal.fire({
                    icon: 'success',
                    title: 'Item Sent Successfully!',
                    html: `
                        <div class="text-left">
                            <p><strong>Item:</strong> Legendary Sword</p>
                            <p><strong>Player:</strong> TestPlayer</p>
                            <p><strong>Quantity:</strong> 1</p>
                            <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
                        </div>
                    `,
                    confirmButtonText: 'OK'
                });
            }, 2000);
        });

        $('#checkSwalFeatures').click(() => {
            const features = [];
            
            if (typeof Swal !== 'undefined') {
                features.push('✅ SweetAlert2 Core');
                features.push('✅ Basic Alerts (success, error, warning, info)');
                features.push('✅ Toast Notifications');
                features.push('✅ Loading Animations');
                features.push('✅ Confirmation Dialogs');
                features.push('✅ Input Dialogs');
                features.push('✅ Custom HTML Content');
                features.push('✅ Animation Classes');
                
                if (Swal.mixin) features.push('✅ Mixin Support');
                if (Swal.showLoading) features.push('✅ Loading Spinner');
                if (Swal.close) features.push('✅ Programmatic Close');
            } else {
                features.push('❌ SweetAlert2 Not Loaded');
            }

            Swal.fire({
                title: 'SweetAlert2 Features',
                html: `
                    <div class="text-left">
                        <ul class="list-unstyled">
                            ${features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                `,
                confirmButtonText: 'OK'
            });
        });

        $('#testAllAnimations').click(async () => {
            const tests = [
                { type: 'success', title: 'Success Test' },
                { type: 'error', title: 'Error Test' },
                { type: 'warning', title: 'Warning Test' },
                { type: 'info', title: 'Info Test' }
            ];

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                await Swal.fire({
                    icon: test.type,
                    title: test.title,
                    text: `Animation test ${i + 1} of ${tests.length}`,
                    timer: 1500,
                    showConfirmButton: false
                });
            }

            Swal.fire({
                icon: 'success',
                title: 'All Tests Complete!',
                text: 'All animation tests have been completed successfully',
                confirmButtonText: 'Great!'
            });
        });

        // Initialize
        $(document).ready(() => {
            checkSweetAlert();
            
            // Show welcome message
            setTimeout(() => {
                if (typeof Swal !== 'undefined') {
                    const Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true
                    });

                    Toast.fire({
                        icon: 'info',
                        title: 'SweetAlert2 Animation Tester Ready!'
                    });
                }
            }, 1000);
        });
    </script>
</body>
</html>
