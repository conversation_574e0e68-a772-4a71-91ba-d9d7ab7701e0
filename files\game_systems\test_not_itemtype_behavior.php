<?php
// Test NOT Item Type Behavior
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test NOT Item Type Behavior</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-lock me-2"></i>Test NOT Item Type Behavior</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>NOT Item Type Features</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Special Behavior for "NOT":</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-lock text-warning me-2"></i>
                                <strong>Slot 1, 2, 3:</strong> Locked to "NOT" only
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-lock text-warning me-2"></i>
                                <strong>Craft Option:</strong> Locked to "NOT" only
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-ban text-secondary me-2"></i>
                                <strong>No Other Options:</strong> Dropdowns disabled
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-palette text-info me-2"></i>
                                <strong>Visual Indication:</strong> Grayed out appearance
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">🔄 Auto-Reset Features:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-sync text-primary me-2"></i>
                                Auto-triggers real-time calculation
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-eye text-success me-2"></i>
                                Console logging for changes
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-unlock text-warning me-2"></i>
                                Re-enables when switching to other types
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test NOT Behavior</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบพฤติกรรมพิเศษของ Item Type "NOT"</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="advanced-editor.php" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                            </a>
                            
                            <button id="simulateNOTBehavior" class="btn btn-success">
                                <i class="fas fa-lock me-2"></i>Simulate NOT Behavior
                            </button>
                            
                            <button id="testItemTypeSwitch" class="btn btn-info">
                                <i class="fas fa-exchange-alt me-2"></i>Test Type Switching
                            </button>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Implementation Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔧 Advanced Editor Changes:</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Special handling for "NOT" item type
if (itemType === 'NOT') {
    ['slot1', 'slot2', 'slot3', 'craftOption'].forEach(slotId => {
        const select = document.getElementById(slotId);
        
        // Only show "NOT" option and disable
        select.innerHTML = '<option value="NOT">NOT</option>';
        select.value = 'NOT';
        select.disabled = true;
        select.style.backgroundColor = '#f8f9fa';
        select.style.color = '#6c757d';
    });
    
    addToConsole('🔒 Item Type "NOT" selected - All slots locked to "NOT"', 'warning');
    return;
}</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>⚙️ ItemManager Changes:</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Get available slot options for item type
getSlotOptions(itemType) {
    // Special handling for "NOT" item type
    if (itemType === 'NOT') {
        return []; // No additional options
    }
    
    const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
    return itemData.map(option => Object.values(option)[0]);
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-comparison me-2"></i>Behavior Comparison</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔓 Normal Item Types (Weapon, Helm, etc.):</h6>
                                <div class="bg-light p-3 rounded">
                                    <h6 class="small text-primary">Slot Options Available:</h6>
                                    <ul class="small mb-2">
                                        <li>NOT (default)</li>
                                        <li>Multiple slot options based on item type</li>
                                        <li>Dropdowns enabled and interactive</li>
                                        <li>Normal appearance</li>
                                    </ul>
                                    
                                    <h6 class="small text-primary">User Interaction:</h6>
                                    <ul class="small mb-0">
                                        <li>Can select different slot options</li>
                                        <li>Can change craft options</li>
                                        <li>Real-time calculation updates</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>🔒 "NOT" Item Type:</h6>
                                <div class="bg-light p-3 rounded">
                                    <h6 class="small text-warning">Slot Options Restricted:</h6>
                                    <ul class="small mb-2">
                                        <li>Only "NOT" option available</li>
                                        <li>No additional slot options</li>
                                        <li>All dropdowns disabled</li>
                                        <li>Grayed out appearance</li>
                                    </ul>
                                    
                                    <h6 class="small text-warning">User Interaction:</h6>
                                    <ul class="small mb-0">
                                        <li>Cannot change slot values</li>
                                        <li>Cannot change craft options</li>
                                        <li>Options Code always 0</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Benefits of NOT Item Type Behavior:</h6>
                            <ul class="mb-0">
                                <li>✅ <strong>Prevents Confusion:</strong> Clear that no options are available</li>
                                <li>✅ <strong>Consistent Behavior:</strong> Always results in Options Code 0</li>
                                <li>✅ <strong>Visual Clarity:</strong> Disabled appearance shows restriction</li>
                                <li>✅ <strong>Error Prevention:</strong> Cannot accidentally set invalid options</li>
                                <li>✅ <strong>Performance:</strong> No unnecessary calculations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>How to Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>Testing Steps:</h6>
                            <ol class="mb-0">
                                <li><strong>Open Advanced Editor:</strong> Click "Open Advanced Editor" above</li>
                                <li><strong>Select Item Type "NOT":</strong> Change Item Type dropdown to "NOT"</li>
                                <li><strong>Check Slot Behavior:</strong> Verify Slot 1, 2, 3 are disabled and show only "NOT"</li>
                                <li><strong>Check Craft Option:</strong> Verify Craft Option is disabled and shows only "NOT"</li>
                                <li><strong>Check Visual Appearance:</strong> Verify dropdowns are grayed out</li>
                                <li><strong>Test Type Switching:</strong> Change to another type and back to "NOT"</li>
                                <li><strong>Check Console:</strong> Look for lock/unlock messages</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Expected Results:</h6>
                            <ul class="mb-0">
                                <li>🔒 All slot dropdowns disabled when Item Type is "NOT"</li>
                                <li>🎨 Grayed out appearance for disabled dropdowns</li>
                                <li>📝 Console message: "Item Type 'NOT' selected - All slots locked to 'NOT'"</li>
                                <li>🔓 Dropdowns re-enabled when switching to other item types</li>
                                <li>⚡ Real-time calculation still works</li>
                                <li>🎯 Options Code always 0 for "NOT" item type</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#simulateNOTBehavior').click(function() {
            showResult('NOT Item Type Behavior Simulation', `
                <p>✅ When Item Type is set to "NOT", the following happens:</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-warning">🔒 Locked Elements:</h6>
                        <ul class="small mb-0">
                            <li>Slot 1: <code>disabled = true</code></li>
                            <li>Slot 2: <code>disabled = true</code></li>
                            <li>Slot 3: <code>disabled = true</code></li>
                            <li>Craft Option: <code>disabled = true</code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">🎨 Visual Changes:</h6>
                        <ul class="small mb-0">
                            <li>Background: <code>#f8f9fa</code> (gray)</li>
                            <li>Text Color: <code>#6c757d</code> (muted)</li>
                            <li>Only "NOT" option visible</li>
                            <li>Cannot interact with dropdowns</li>
                        </ul>
                    </div>
                </div>
            `, 'success');
        });

        $('#testItemTypeSwitch').click(function() {
            showResult('Item Type Switching Test', `
                <p>🔄 Testing behavior when switching between item types:</p>
                <div class="bg-light p-3 rounded">
                    <h6 class="small">Switching Sequence:</h6>
                    <ol class="small mb-2">
                        <li><strong>Start:</strong> Any item type (e.g., "Weapon")</li>
                        <li><strong>Switch to "NOT":</strong> All slots lock to "NOT"</li>
                        <li><strong>Switch back to "Weapon":</strong> Slots unlock and populate with weapon options</li>
                        <li><strong>Switch to "Helm":</strong> Slots populate with helm options</li>
                        <li><strong>Switch to "NOT" again:</strong> All slots lock to "NOT" again</li>
                    </ol>
                    
                    <h6 class="small">Expected Console Messages:</h6>
                    <div class="font-monospace small">
                        🔄 Item Type changed to: NOT<br>
                        🔒 Item Type "NOT" selected - All slots locked to "NOT"<br>
                        🔄 Item Type changed to: Weapon<br>
                        🔄 Updated slot options for Weapon (X options)
                    </div>
                </div>
            `, 'info');
        });

        // Show welcome message
        setTimeout(() => {
            showResult('NOT Item Type System Ready', `
                <p>The special behavior for "NOT" item type has been implemented:</p>
                <ul class="mb-0">
                    <li>🔒 Automatically locks all slots to "NOT" when selected</li>
                    <li>🎨 Visual indication with disabled, grayed-out appearance</li>
                    <li>🔄 Automatically unlocks when switching to other item types</li>
                    <li>📝 Console logging for all changes</li>
                    <li>⚡ Integrated with real-time calculation system</li>
                </ul>
            `, 'success');
        }, 1000);
    </script>
</body>
</html>
