<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Slot Code Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Slot Code Loading</h1>
    
    <div id="testResults"></div>
    
    <button onclick="testSlotCodeLoading()">Test Slot Code Loading</button>
    <button onclick="testItemManager()">Test Item Manager</button>
    
    <script>
        async function testSlotCodeLoading() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">Testing slot code loading...</div>';
            
            try {
                // Test 1: Load JSON file
                const response = await fetch('files/game_systems/import/optioncode_structure.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // Test 2: Validate structure
                const expectedTypes = ['Weapon', 'Helm', 'Suit', 'Gloves', 'Boots', 'Bike', 'Amulet'];
                const loadedTypes = Object.keys(data);
                
                let results = '<div class="success">✅ JSON file loaded successfully</div>';
                results += `<div class="info">Loaded types: ${loadedTypes.join(', ')}</div>`;
                
                // Test 3: Check each type
                for (const type of expectedTypes) {
                    if (data[type]) {
                        results += `<div class="success">✅ ${type}: ${data[type].length} options</div>`;
                        
                        // Show first few options
                        const firstOptions = data[type].slice(0, 5).map(opt => {
                            const key = Object.keys(opt)[0];
                            const value = Object.values(opt)[0];
                            return `${key}: ${value}`;
                        }).join(', ');
                        results += `<div class="info">First options: ${firstOptions}</div>`;
                    } else {
                        results += `<div class="error">❌ ${type}: Missing</div>`;
                    }
                }
                
                // Test 4: Show full structure (limited)
                results += '<h3>Sample Data Structure:</h3>';
                results += '<pre>' + JSON.stringify({
                    Weapon: data.Weapon.slice(0, 3),
                    Helm: data.Helm.slice(0, 3)
                }, null, 2) + '</pre>';
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('Test failed:', error);
            }
        }
        
        async function testItemManager() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">Testing Item Manager...</div>';
            
            try {
                // Create a simple test ItemManager
                class TestItemManager {
                    constructor() {
                        this.itemMapName = {};
                    }
                    
                    async loadItemMapName() {
                        try {
                            const response = await fetch('files/game_systems/import/optioncode_structure.json');
                            if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            
                            const data = await response.json();
                            this.itemMapName = data;
                            return true;
                        } catch (error) {
                            console.error('Error loading slot code data:', error);
                            return false;
                        }
                    }
                    
                    getSlotOptions(itemType) {
                        const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
                        return itemData.map(option => Object.values(option)[0]);
                    }
                    
                    getHexBySlotOption(itemType, optionName) {
                        const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
                        const option = itemData.find(opt => Object.values(opt)[0] === optionName);
                        return option ? Object.keys(option)[0] : '0';
                    }
                }
                
                const testManager = new TestItemManager();
                const loadSuccess = await testManager.loadItemMapName();
                
                let results = '';
                if (loadSuccess) {
                    results += '<div class="success">✅ ItemManager loaded slot code data successfully</div>';
                    
                    // Test slot options
                    const weaponOptions = testManager.getSlotOptions('Weapon');
                    results += `<div class="info">Weapon options (${weaponOptions.length}): ${weaponOptions.slice(0, 10).join(', ')}...</div>`;
                    
                    // Test hex lookup
                    const atkHex = testManager.getHexBySlotOption('Weapon', 'ATK+1');
                    results += `<div class="info">ATK+1 hex code: ${atkHex}</div>`;
                    
                    const critHex = testManager.getHexBySlotOption('Weapon', 'CRIT+1');
                    results += `<div class="info">CRIT+1 hex code: ${critHex}</div>`;
                    
                } else {
                    results += '<div class="error">❌ ItemManager failed to load slot code data</div>';
                }
                
                resultsDiv.innerHTML = results;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                console.error('ItemManager test failed:', error);
            }
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            testSlotCodeLoading();
        });
    </script>
</body>
</html>
