<?php 
 $zpanel->checkSession(true);
?>
 
    <style>
        .status-pending { color: #ffc107; }
        .status-sent { color: #198754; }
        .status-failed { color: #dc3545; }
        .status-processing { color: #0dcaf0; }
        
        .history-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        
        .history-card.success { border-left-color: #198754; }
        .history-card.pending { border-left-color: #ffc107; }
        .history-card.failed { border-left-color: #dc3545; }
        
        .history-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .btn-refresh {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        
        .btn-refresh:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
        
        .item-code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.875em;
        }

        .bg-purple {
            background-color: #6f42c1 !important;
        }
    </style>

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-history text-primary me-2"></i>
                        Item Send History - ประวัติการส่งไอเทม
                    </h1>
                    <div>
                        <a href="?url=game_systems/advanced-editor" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> กลับไป Advanced Editor
                        </a>
                        <button id="refreshHistoryBtn" class="btn btn-refresh me-2">
                            <i class="fas fa-sync-alt me-1"></i> รีเฟรช
                        </button>
                        <button id="exportHistoryBtn" class="btn btn-outline-primary me-2">
                            <i class="fas fa-download me-1"></i> ส่งออก
                        </button>
                        <button id="clearHistoryBtn" class="btn btn-outline-danger">
                            <i class="fas fa-trash-alt me-1"></i> ล้างประวัติ
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-paper-plane fa-2x mb-2"></i>
                        <h4 id="totalSent">-</h4>
                        <small>Total Sent</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 id="successfulSent" class="text-success">-</h4>
                        <small>Successful</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                        <h4 id="pendingSent" class="text-warning">-</h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                        <h4 id="failedSent" class="text-danger">-</h4>
                        <small>Failed</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>ตัวกรอง</h5>
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select id="filterStatus" class="form-select">
                        <option value="">ทั้งหมด</option>
                        <option value="pending">Pending</option>
                        <option value="sent_to_inventory">Sent to Inventory</option>
                        <option value="sent_to_mail">Sent to Mail</option>
                        <option value="sent_to_event_inventory">Sent to event Inventory</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Send Method</label>
                    <select id="filterMethod" class="form-select">
                        <option value="">ทั้งหมด</option>
                        <option value="inventory">Inventory</option>
                        <option value="mail">Mail</option>
                        <option value="warehouse">Warehouse</option>
                        <option value="event_inventory">Event Inventory</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">User ID</label>
                    <input type="text" id="filterAdmin" class="form-control" placeholder="ID ผู้ใช้งาน">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Player Username</label>
                    <input type="text" id="filterPlayer" class="form-control" placeholder="ชื่อผู้เล่น">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <label class="form-label">จำนวนแสดงผล</label>
                    <select id="filterLimit" class="form-select">
                        <option value="50">50</option>
                        <option value="100" selected>100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <button id="applyFilters" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> ค้นหา
                    </button>
                    <button id="clearFilters" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-times me-1"></i> ล้างตัวกรอง
                    </button>
                </div>
            </div>
        </div>

        <!-- History Table -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>ประวัติการส่งไอเทม</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table id="historyTable" class="table table-striped table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>เวลา</th>
                                <th>User ID</th>
                                <th>Player</th>
                                <th>Item Name</th>
                                <th>Quantity</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <!-- History will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="historyLoadingIndicator" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">กำลังโหลดประวัติ...</p>
        </div>
 

    <!-- History Detail Modal -->
    <div class="modal fade" id="historyDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดการส่งไอเทม</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="historyDetailContent">
                    <!-- History details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

 
    <script>
        let historyData = [];
        let dataTable;

        // Initialize page
        $(document).ready(function() {
            initializeDataTable();
            loadHistory();
            setupEventListeners();
        });

        function initializeDataTable() {
            dataTable = $('#historyTable').DataTable({
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true,
                language: {
                    "decimal": "",
                    "emptyTable": "ไม่มีข้อมูลในตาราง",
                    "info": "แสดง _START_ ถึง _END_ จาก _TOTAL_ รายการ",
                    "infoEmpty": "แสดง 0 ถึง 0 จาก 0 รายการ",
                    "infoFiltered": "(กรองจาก _MAX_ รายการทั้งหมด)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "แสดง _MENU_ รายการ",
                    "loadingRecords": "กำลังโหลด...",
                    "processing": "กำลังประมวลผล...",
                    "search": "ค้นหา:",
                    "zeroRecords": "ไม่พบข้อมูลที่ตรงกัน",
                    "paginate": {
                        "first": "หน้าแรก",
                        "last": "หน้าสุดท้าย",
                        "next": "ถัดไป",
                        "previous": "ก่อนหน้า"
                    },
                    "aria": {
                        "sortAscending": ": เปิดใช้งานการเรียงลำดับจากน้อยไปมาก",
                        "sortDescending": ": เปิดใช้งานการเรียงลำดับจากมากไปน้อย"
                    }
                }
            });
        }

        function setupEventListeners() {
            $('#refreshHistoryBtn').click(loadHistory);
            $('#applyFilters').click(loadHistory);
            $('#clearFilters').click(clearFilters);
            $('#exportHistoryBtn').click(exportHistory);
            $('#clearHistoryBtn').click(clearHistory);
        }

        async function loadHistory() {
            try {
                showLoading(true);
                
                const filters = {
                    limit: $('#filterLimit').val() || 100,
                    status: $('#filterStatus').val(),
                    send_method: $('#filterMethod').val(),
                    admin_username: $('#filterAdmin').val(),
                    player_username: $('#filterPlayer').val()
                };

                // Remove empty filters
                Object.keys(filters).forEach(key => {
                    if (!filters[key]) delete filters[key];
                });

                // Debug: Log filters being sent
                console.log('Sending filters:', filters);
                console.log('Query string:', new URLSearchParams(filters).toString());

                // Try to load from database first
                const response = await fetch(`files/game_systems/get_item_history.php?${new URLSearchParams(filters)}`);
                let data;
                
                if (response.ok) {
                    data = await response.json();
                } else {
                    // Fallback to localStorage
                    data = { success: true, history: getLocalStorageHistory() };
                }
                
                if (data.success) {
                    historyData = data.history || [];
                    console.log('Received history data:', historyData);
                    console.log('Total records:', historyData.length);
                    displayHistory(historyData);
                    updateStatistics(historyData);
                } else {
                    console.error('Error loading history:', data.error);
                    // Load from localStorage as fallback
                    historyData = getLocalStorageHistory();
                    displayHistory(historyData);
                    updateStatistics(historyData);
                }
            } catch (error) {
                console.error('Error:', error);
                // Load from localStorage as fallback
                historyData = getLocalStorageHistory();
                displayHistory(historyData);
                updateStatistics(historyData);
            } finally {
                showLoading(false);
            }
        }

        function getLocalStorageHistory() {
            try {
                return JSON.parse(localStorage.getItem('itemSendHistory') || '[]');
            } catch (error) {
                console.error('Error loading from localStorage:', error);
                return [];
            }
        }

        function displayHistory(history) {
            dataTable.clear();

            console.log('Displaying history:', history.length, 'items');

            history.forEach((item, index) => {
                const status = item.status || item.dbStatus;
                const statusBadge = getStatusBadge(status);
                const timeFormatted = new Date(item.timestamp || item.created_at).toLocaleString('th-TH');

                // Debug first few items
                if (index < 3) {
                    console.log(`Item ${index}:`, {
                        id: item.sendId || item.id,
                        status: status,
                        adminUsername: item.adminUsername,
                        playerUsername: item.playerUsername
                    });
                }

                const sendMethod = item.sendMethod || item.send_method || '-';
                const methodDisplay = getMethodDisplay(sendMethod);

                dataTable.row.add([
                    item.sendId || item.id || '-',
                    timeFormatted,
                    item.adminUsername || '<?php echo $_SESSION['userLogin'] ?? 'Unknown'; ?>',
                    item.playerUsername || '-',
                    item.itemName || 'Custom Item',
                    item.quantity || 1,
                    methodDisplay,
                    statusBadge,
                    `<button class="btn btn-sm btn-outline-primary" onclick="showHistoryDetail(${item.sendId || item.id || 0})">
                        <i class="fas fa-eye"></i>
                    </button>`
                ]);
            });

            dataTable.draw();
            console.log('DataTable updated with', history.length, 'rows');
        }

        function getStatusBadge(status) {
            const badges = {
                'pending': '<span class="badge bg-warning">Pending</span>',
                'sent_to_inventory': '<span class="badge bg-success">Sent to Inventory</span>',
                'sent_to_mail': '<span class="badge bg-info">Sent to Mail</span>',
                'sent_to_warehouse': '<span class="badge bg-primary">Sent to Warehouse</span>',
                'sent_to_event_inventory': '<span class="badge bg-purple">Sent to Event Inventory</span>',
                'failed': '<span class="badge bg-danger">Failed</span>',
                'success': '<span class="badge bg-success">Success</span>',
                'completed': '<span class="badge bg-success">Completed</span>',
                'processing': '<span class="badge bg-info">Processing</span>'
            };
            return badges[status] || '<span class="badge bg-secondary">' + (status || 'Unknown') + '</span>';
        }

        function getMethodDisplay(method) {
            const methods = {
                'inventory': '<span class="badge bg-success"><i class="fas fa-box me-1"></i>Inventory</span>',
                'mail': '<span class="badge bg-info"><i class="fas fa-envelope me-1"></i>Mail</span>',
                'warehouse': '<span class="badge bg-primary"><i class="fas fa-warehouse me-1"></i>Warehouse</span>',
                'event_inventory': '<span class="badge bg-purple"><i class="fas fa-calendar-star me-1"></i>Event Inventory</span>',
                'notification': '<span class="badge bg-warning"><i class="fas fa-bell me-1"></i>Notification</span>'
            };
            return methods[method] || '<span class="badge bg-secondary">' + (method || 'Unknown') + '</span>';
        }

        function updateStatistics(history) {
            const stats = {
                total: history.length,
                successful: history.filter(item => 
                    (item.status || item.dbStatus || '').includes('sent')).length,
                pending: history.filter(item => 
                    (item.status || item.dbStatus) === 'pending').length,
                failed: history.filter(item => 
                    (item.status || item.dbStatus) === 'failed').length
            };

            $('#totalSent').text(stats.total.toLocaleString());
            $('#successfulSent').text(stats.successful.toLocaleString());
            $('#pendingSent').text(stats.pending.toLocaleString());
            $('#failedSent').text(stats.failed.toLocaleString());
        }

        function showHistoryDetail(itemId) {
            const item = historyData.find(h => (h.sendId || h.id) == itemId);
            if (!item) return;

            const detailHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Send ID:</strong> ${item.sendId || item.id || '-'}<br>
                        <strong>Admin:</strong> ${item.adminUsername || '-'}<br>
                        <strong>Player:</strong> ${item.playerUsername || '-'}<br>
                        <strong>Item Name:</strong> ${item.itemName || 'Custom Item'}<br>
                        <strong>Quantity:</strong> ${item.quantity || 1}<br>
                        <strong>Send Method:</strong> ${item.sendMethod || '-'}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>Item ID:</strong> ${item.itemId || '-'}<br>
                        <strong>Item Code:</strong> <span class="item-code">${item.itemCode || '-'}</span><br>
                        <strong>Options Code:</strong> <span class="item-code">${item.optionsCode || '-'}</span><br>
                        <strong>Status:</strong> ${getStatusBadge(item.status || item.dbStatus)}<br>
                        <strong>Timestamp:</strong> ${new Date(item.timestamp || item.created_at).toLocaleString('th-TH')}<br>
                    </div>
                </div>
                <hr>
                <div>
                    <strong>Additional Details:</strong><br>
                    <div class="bg-light p-3 rounded">
                        <small>
                            Upgrade: +${item.upgrade || 0}, Extreme: ${item.extreme || 0}, Divine: ${item.divine || 0}<br>
                            Binding: ${item.binding || 'none'}, Duration: ${item.duration || 0} days<br>
                            Slots: ${item.slot1 || 'NOT'}, ${item.slot2 || 'NOT'}, ${item.slot3 || 'NOT'}
                        </small>
                    </div>
                </div>
            `;

            $('#historyDetailContent').html(detailHtml);
            $('#historyDetailModal').modal('show');
        }

        function clearFilters() {
            $('#filterStatus').val('');
            $('#filterMethod').val('');
            $('#filterAdmin').val('');
            $('#filterPlayer').val('');
            $('#filterLimit').val('100');
            loadHistory();
        }

        function exportHistory() {
            if (historyData.length === 0) {
                Swal.fire('แจ้งเตือน', 'ไม่มีข้อมูลสำหรับส่งออก', 'warning');
                return;
            }

            const csvContent = convertToCSV(historyData);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `item_send_history_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function convertToCSV(data) {
            const headers = ['ID', 'Timestamp', 'User ID', 'Player', 'Item Name', 'Quantity', 'Method', 'Status', 'Item Code', 'Options Code'];
            const csvRows = [headers.join(',')];

            data.forEach(item => {
                const row = [
                    item.sendId || item.id || '',
                    item.timestamp || item.created_at || '',
                    item.adminUsername || '',
                    item.playerUsername || '',
                    `"${(item.itemName || '').replace(/"/g, '""')}"`,
                    item.quantity || '',
                    item.sendMethod || '',
                    item.status || item.dbStatus || '',
                    item.itemCode || '',
                    item.optionsCode || ''
                ];
                csvRows.push(row.join(','));
            });

            return csvRows.join('\n');
        }

        async function clearHistory() {
            const result = await Swal.fire({
                title: 'ยืนยันการลบประวัติ',
                text: 'คุณต้องการลบประวัติการส่งไอเทมทั้งหมดหรือไม่?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'ลบทั้งหมด',
                cancelButtonText: 'ยกเลิก'
            });

            if (result.isConfirmed) {
                localStorage.removeItem('itemSendHistory');
                historyData = [];
                displayHistory(historyData);
                updateStatistics(historyData);
                
                Swal.fire('สำเร็จ', 'ลบประวัติเรียบร้อยแล้ว', 'success');
            }
        }

        function showLoading(show) {
            if (show) {
                $('#historyLoadingIndicator').show();
                $('#historyTable').hide();
            } else {
                $('#historyLoadingIndicator').hide();
                $('#historyTable').show();
            }
        }
    </script>
