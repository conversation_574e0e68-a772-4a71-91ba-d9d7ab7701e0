<?php
// Test Element Warnings Fix
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Element Warnings Fix</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-shield-alt me-2"></i>Test Element Warnings Fix</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>Element Safety Fixes Applied</h5>
                    </div>
                    <div class="card-body">
                        <h6>✅ Fixed Elements:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                <code>copyOptionsCodeBtn</code> - Safe access
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                <code>applyOptionsCodeBtn</code> - Safe access
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                <code>clearOptionsCodeBtn</code> - Safe access
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-shield text-success me-2"></i>
                                <code>optionsCode</code> input - Safe access
                            </li>
                        </ul>
                        
                        <h6 class="mt-3">🛡️ Safety Pattern Applied:</h6>
                        <div class="bg-light p-3 rounded">
                            <pre class="small mb-0">// Before (Unsafe)
document.getElementById('elementId').addEventListener(...)

// After (Safe)
const element = document.getElementById('elementId');
if (element) {
    element.addEventListener(...)
} else {
    console.warn('⚠️ elementId not found');
}</pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Warning Fixes</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Purpose</h6>
                            <p>ทดสอบว่าไม่มี element warnings ใน console</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="advanced-editor.php" class="btn btn-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open Advanced Editor
                            </a>
                            
                            <button id="checkConsoleWarnings" class="btn btn-success">
                                <i class="fas fa-search me-2"></i>Check Console Instructions
                            </button>
                            
                            <button id="showSafetyPattern" class="btn btn-info">
                                <i class="fas fa-shield-alt me-2"></i>Show Safety Pattern
                            </button>
                        </div>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-code me-2"></i>Safety Implementation</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">❌ Before (Unsafe Pattern):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Direct access - can cause errors
document.getElementById('copyOptionsCodeBtn').addEventListener('click', function() {
    // Code here...
});

// Problems:
// - Throws error if element doesn't exist
// - No graceful handling
// - Console warnings/errors</pre>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">✅ After (Safe Pattern):</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre class="small mb-0">// Safe access with null checking
const copyOptionsCodeBtn = document.getElementById('copyOptionsCodeBtn');
if (copyOptionsCodeBtn) {
    copyOptionsCodeBtn.addEventListener('click', function() {
        // Code here...
    });
} else {
    console.warn('⚠️ copyOptionsCodeBtn element not found');
}

// Benefits:
// - No errors if element missing
// - Graceful degradation
// - Clear warning messages</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-list me-2"></i>Elements Protected</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🔘 Button Elements:</h6>
                                <div class="bg-light p-3 rounded">
                                    <ul class="small mb-0">
                                        <li><code>copyOptionsCodeBtn</code> - Copy options code</li>
                                        <li><code>applyOptionsCodeBtn</code> - Apply options code</li>
                                        <li><code>clearOptionsCodeBtn</code> - Clear options code</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📝 Input Elements:</h6>
                                <div class="bg-light p-3 rounded">
                                    <ul class="small mb-0">
                                        <li><code>optionsCode</code> - Options code input field</li>
                                        <li>Event listeners: input, focus, keypress</li>
                                        <li>Safe access in all event handlers</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6>🎯 Expected Results After Fix:</h6>
                            <ul class="mb-0">
                                <li>✅ No "element not found" errors in console</li>
                                <li>✅ Graceful handling of missing elements</li>
                                <li>✅ Clear warning messages for debugging</li>
                                <li>✅ Advanced Editor loads without JavaScript errors</li>
                                <li>✅ All existing functionality preserved</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-clipboard-check me-2"></i>Verification Steps</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>How to Verify the Fix:</h6>
                            <ol class="mb-0">
                                <li><strong>Open Advanced Editor:</strong> Click "Open Advanced Editor" above</li>
                                <li><strong>Open Browser Console:</strong> Press F12 → Console tab</li>
                                <li><strong>Check for Warnings:</strong> Look for any "element not found" warnings</li>
                                <li><strong>Test Functionality:</strong> Try using all buttons and inputs</li>
                                <li><strong>Verify Clean Console:</strong> Should see no JavaScript errors</li>
                            </ol>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Console Commands for Testing:</h6>
                            <div class="bg-dark text-light p-3 rounded font-monospace small">
                                // Test element existence<br>
                                console.log('copyOptionsCodeBtn:', document.getElementById('copyOptionsCodeBtn'));<br>
                                console.log('applyOptionsCodeBtn:', document.getElementById('applyOptionsCodeBtn'));<br>
                                console.log('clearOptionsCodeBtn:', document.getElementById('clearOptionsCodeBtn'));<br>
                                console.log('optionsCode:', document.getElementById('optionsCode'));<br><br>
                                
                                // Check for null elements<br>
                                const elements = ['copyOptionsCodeBtn', 'applyOptionsCodeBtn', 'clearOptionsCodeBtn', 'optionsCode'];<br>
                                elements.forEach(id => {<br>
                                &nbsp;&nbsp;const el = document.getElementById(id);<br>
                                &nbsp;&nbsp;console.log(`${id}: ${el ? 'Found' : 'Missing'}`);<br>
                                });
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6>Expected Console Output:</h6>
                            <ul class="mb-0">
                                <li>🔍 <strong>If elements exist:</strong> No warnings, all functionality works</li>
                                <li>⚠️ <strong>If elements missing:</strong> Clear warning messages instead of errors</li>
                                <li>✅ <strong>Overall result:</strong> Clean console, no JavaScript errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function showResult(title, content, type = 'info') {
            const alertClass = type === 'error' ? 'danger' : type;
            $('#testResults').html(`
                <div class="alert alert-${alertClass}">
                    <h6><i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'} me-2"></i>${title}</h6>
                    <div>${content}</div>
                </div>
            `);
        }

        $('#checkConsoleWarnings').click(function() {
            showResult('Console Warning Check Instructions', `
                <p>🔍 To check if the element warnings are fixed:</p>
                <ol class="small mb-0">
                    <li><strong>Open Advanced Editor</strong> in a new tab</li>
                    <li><strong>Open Browser Console</strong> (F12 → Console)</li>
                    <li><strong>Look for warnings</strong> like "⚠️ Advanced Editor button not found"</li>
                    <li><strong>Check for errors</strong> related to getElementById</li>
                    <li><strong>Test all buttons</strong> to ensure they work properly</li>
                </ol>
                <div class="mt-2 p-2 bg-success rounded text-white">
                    <small><strong>Expected:</strong> Clean console with no element access errors</small>
                </div>
            `, 'info');
        });

        $('#showSafetyPattern').click(function() {
            showResult('Element Safety Pattern', `
                <p>🛡️ The safety pattern applied to prevent element access errors:</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="small text-danger">❌ Unsafe:</h6>
                        <pre class="small bg-light p-2 rounded">document.getElementById('btn').addEventListener(...);</pre>
                    </div>
                    <div class="col-md-6">
                        <h6 class="small text-success">✅ Safe:</h6>
                        <pre class="small bg-light p-2 rounded">const btn = document.getElementById('btn');
if (btn) {
    btn.addEventListener(...);
} else {
    console.warn('⚠️ btn not found');
}</pre>
                    </div>
                </div>
                <div class="mt-2 p-2 bg-info rounded text-white">
                    <small><strong>Result:</strong> Graceful handling of missing elements with clear warnings</small>
                </div>
            `, 'success');
        });

        // Show initial guidance
        setTimeout(() => {
            showResult('Element Safety Fix Applied', `
                <p>✅ Applied safe element access pattern to prevent console warnings:</p>
                <ul class="small mb-0">
                    <li>🔘 <strong>Button Elements:</strong> copyOptionsCodeBtn, applyOptionsCodeBtn, clearOptionsCodeBtn</li>
                    <li>📝 <strong>Input Elements:</strong> optionsCode with all event listeners</li>
                    <li>🛡️ <strong>Safety Pattern:</strong> Null checking before addEventListener</li>
                    <li>⚠️ <strong>Warning Messages:</strong> Clear console warnings for missing elements</li>
                </ul>
                <div class="mt-2 p-2 bg-success rounded text-white">
                    <small><strong>Result:</strong> No more "Advanced Editor button not found" warnings!</small>
                </div>
            `, 'success');
        }, 1000);
    </script>
</body>
</html>
