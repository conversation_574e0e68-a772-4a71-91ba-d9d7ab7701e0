<?php
// Test admin_logs display
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Logs Display</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-database me-2"></i>Test Admin Logs Display</h2>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-table me-2"></i>Admin Logs Table Structure</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6>Table: [dbo].[admin_logs]</h6>
                            <ul class="mb-0">
                                <li><strong>id</strong> - int IDENTITY(1,1) NOT NULL</li>
                                <li><strong>admin_username</strong> - nvarchar(50) NOT NULL</li>
                                <li><strong>action</strong> - nvarchar(100) NOT NULL</li>
                                <li><strong>target_player</strong> - nvarchar(50) NULL</li>
                                <li><strong>details</strong> - nvarchar(max) NULL (JSON format)</li>
                                <li><strong>ip_address</strong> - nvarchar(45) NULL</li>
                                <li><strong>user_agent</strong> - nvarchar(max) NULL</li>
                                <li><strong>created_at</strong> - datetime NOT NULL</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-list me-2"></i>Recent Admin Logs</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $conn = db_connect();
                            if ($conn) {
                                // Get recent admin_logs
                                $query = "SELECT TOP 10 
                                    id, admin_username, action, target_player, details,
                                    ip_address, user_agent, created_at
                                    FROM admin_logs 
                                    ORDER BY created_at DESC";
                                
                                $result = sqlsrv_query($conn, $query);
                                
                                if ($result && sqlsrv_has_rows($result)) {
                                    echo '<div class="table-responsive">';
                                    echo '<table class="table table-striped table-hover">';
                                    echo '<thead class="table-dark">';
                                    echo '<tr>';
                                    echo '<th>ID</th>';
                                    echo '<th>User ID</th>';
                                    echo '<th>Action</th>';
                                    echo '<th>Target Player</th>';
                                    echo '<th>Details Preview</th>';
                                    echo '<th>IP Address</th>';
                                    echo '<th>Created At</th>';
                                    echo '<th>Actions</th>';
                                    echo '</tr>';
                                    echo '</thead>';
                                    echo '<tbody>';
                                    
                                    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                        $createdAt = is_object($row['created_at']) ? 
                                            $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'];
                                        
                                        $detailsPreview = $row['details'] ? 
                                            (strlen($row['details']) > 50 ? substr($row['details'], 0, 50) . '...' : $row['details']) : '-';
                                        
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                                        echo '<td><code>' . htmlspecialchars($row['admin_username']) . '</code></td>';
                                        echo '<td>' . htmlspecialchars($row['action']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['target_player'] ?: '-') . '</td>';
                                        echo '<td><small>' . htmlspecialchars($detailsPreview) . '</small></td>';
                                        echo '<td>' . htmlspecialchars($row['ip_address'] ?: '-') . '</td>';
                                        echo '<td>' . htmlspecialchars($createdAt) . '</td>';
                                        echo '<td>';
                                        echo '<button class="btn btn-sm btn-outline-primary" onclick="showDetails(' . $row['id'] . ')">View</button>';
                                        echo '</td>';
                                        echo '</tr>';
                                    }
                                    
                                    echo '</tbody>';
                                    echo '</table>';
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-warning">No admin logs found</div>';
                                }
                                
                                sqlsrv_close($conn);
                            } else {
                                echo '<div class="alert alert-danger">Database connection failed</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-example me-2"></i>Example Data</h5>
                    </div>
                    <div class="card-body">
                        <h6>Sample INSERT:</h6>
                        <pre class="bg-light p-3 rounded small">INSERT INTO [dbo].[admin_logs] 
([admin_username], [action], [target_player], [details], [ip_address], [user_agent], [created_at]) 
VALUES 
(N'Admin', N'Send Item', N'mata1', 
N'{"action":"send_item","item_id":1,"item_code":1,"options_code":"","quantity":1,"duration":31,"method":"inventory","send_id":null}', 
N'::1', 
N'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 
N'2025-07-27 23:41:02.000')</pre>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-link me-2"></i>Related Pages</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="advanced_editor_logs_viewer.php" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>Advanced Editor Logs Viewer
                            </a>
                            <a href="log_advanced_editor.php" class="btn btn-success">
                                <i class="fas fa-api me-2"></i>Logs API
                            </a>
                            <a href="item_send_history.php" class="btn btn-info">
                                <i class="fas fa-history me-2"></i>Item Send History
                            </a>
                            <a href="advanced-editor.php" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Advanced Editor
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test API</h5>
                    </div>
                    <div class="card-body">
                        <button id="testAPI" class="btn btn-primary me-2">
                            <i class="fas fa-play me-2"></i>Test Logs API
                        </button>
                        <button id="clearResults" class="btn btn-secondary">
                            <i class="fas fa-trash me-2"></i>Clear Results
                        </button>
                        
                        <div id="apiResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Store logs data for detail view
        let logsData = [];
        
        function showDetails(logId) {
            // In a real implementation, you would fetch the full log details
            Swal.fire({
                title: 'Log Detail',
                html: `<p>Log ID: ${logId}</p><p>This would show full log details including parsed JSON from details field.</p>`,
                width: '600px',
                showCloseButton: true,
                showConfirmButton: false
            });
        }
        
        $(document).ready(function() {
            $('#testAPI').click(async function() {
                const resultsDiv = $('#apiResults');
                resultsDiv.html('<div class="alert alert-info">Testing logs API...</div>');
                
                try {
                    const response = await fetch('files/game_systems/log_advanced_editor.php?limit=5');
                    const data = await response.json();
                    
                    if (data.success) {
                        logsData = data.logs;
                        
                        let html = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>API Test Successful!</h6>
                                <p><strong>Total logs:</strong> ${data.count}</p>
                            </div>
                        `;
                        
                        if (data.logs && data.logs.length > 0) {
                            html += '<h6>Sample API Response:</h6>';
                            html += '<pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">';
                            html += JSON.stringify(data.logs.slice(0, 2), null, 2);
                            html += '</pre>';
                        }
                        
                        resultsDiv.html(html);
                    } else {
                        resultsDiv.html(`
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times me-2"></i>API Test Failed!</h6>
                                <p>Error: ${data.error || 'Unknown error'}</p>
                            </div>
                        `);
                    }
                    
                } catch (error) {
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Request Failed!</h6>
                            <p>Error: ${error.message}</p>
                        </div>
                    `);
                }
            });
            
            $('#clearResults').click(function() {
                $('#apiResults').html('');
            });
        });
    </script>
</body>
</html>
