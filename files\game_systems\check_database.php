<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    echo json_encode(['success' => false, 'message' => 'ไม่มีการ login']);
    exit();
}

$conn = db_connect();

try {
    // ตรวจสอบว่าตาราง WEB_cabal_forcecodes มีอยู่หรือไม่
    $checkTableSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_cabal_forcecodes'";
    $stmt = sqlsrv_query($conn, $checkTableSql);
    
    if (!$stmt) {
        echo "❌ ไม่สามารถตรวจสอบตารางได้: " . print_r(sqlsrv_errors(), true);
        exit();
    }
    
    $result = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "✅ ตาราง WEB_cabal_forcecodes มีอยู่แล้ว<br>";
        
        // ตรวจสอบจำนวนข้อมูล
        $countSql = "SELECT COUNT(*) as count FROM WEB_cabal_forcecodes";
        $countStmt = sqlsrv_query($conn, $countSql);
        
        if ($countStmt) {
            $countResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
            echo "📊 จำนวนข้อมูลในตาราง: " . $countResult['count'] . " รายการ<br>";
            
            if ($countResult['count'] > 0) {
                // แสดงข้อมูลตัวอย่าง
                $sampleSql = "SELECT TOP 5 * FROM WEB_cabal_forcecodes ORDER BY ForceCode";
                $sampleStmt = sqlsrv_query($conn, $sampleSql);
                
                if ($sampleStmt) {
                    echo "<h4>ข้อมูลตัวอย่าง:</h4>";
                    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                    echo "<tr><th>ForceCode</th><th>OptionScroll</th><th>OptionScrollName</th><th>ForceCodeName</th></tr>";
                    
                    while ($row = sqlsrv_fetch_array($sampleStmt, SQLSRV_FETCH_ASSOC)) {
                        echo "<tr>";
                        echo "<td>" . $row['ForceCode'] . "</td>";
                        echo "<td>" . $row['OptionScroll'] . "</td>";
                        echo "<td>" . $row['OptionScrollName'] . "</td>";
                        echo "<td>" . ($row['ForceCodeName'] ?? 'NULL') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "⚠️ ตารางว่างเปล่า - ต้องเพิ่มข้อมูล<br>";
                echo "<p>กรุณารันไฟล์ <strong>setup_forcecodes_table.sql</strong> เพื่อเพิ่มข้อมูลตัวอย่าง</p>";
            }
        }
        
    } else {
        echo "❌ ไม่พบตาราง WEB_cabal_forcecodes<br>";
        echo "<p>กรุณารันไฟล์ <strong>setup_forcecodes_table.sql</strong> เพื่อสร้างตารางและข้อมูล</p>";
        echo "<h4>SQL สำหรับสร้างตาราง:</h4>";
        echo "<pre>";
        echo "CREATE TABLE [dbo].[WEB_cabal_forcecodes] (\n";
        echo "    [ForceCode] int NOT NULL,\n";
        echo "    [OptionScroll] int NOT NULL,\n";
        echo "    [OptionScrollName] nvarchar(1000) COLLATE Thai_CI_AS NOT NULL,\n";
        echo "    [ForceCodeName] nvarchar(1000) COLLATE Thai_CI_AS NULL\n";
        echo ")";
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage();
}

sqlsrv_close($conn);
?>
