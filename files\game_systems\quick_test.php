<?php
session_start();
require_once("../../_app/dbinfo.inc.php");

// ตรวจสอบการ login
if (!isset($_SESSION['userLogin'])) {
    header("Location: ../../index.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - Option Code Tools</title>
    <link href="../../assets/vendor/bootstrap/css/bootstrap.css" rel="stylesheet">
    <link href="../../assets/vendor/font-awesome/css/all.min.css" rel="stylesheet">
    <style>
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="fas fa-vial"></i> Quick Test - Option Code Tools</h2>
        <p class="text-muted">ทดสอบเบื้องต้นก่อนใช้งานระบบ</p>

        <!-- ทดสอบไฟล์ XML -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-file-code"></i> ทดสอบไฟล์ optioncode.xml</h5>
            </div>
            <div class="card-body">
                <?php
                $xmlFile = 'optioncode.xml';
                if (file_exists($xmlFile)) {
                    $xmlContent = file_get_contents($xmlFile);
                    $lines = explode("\n", $xmlContent);
                    $typeCount = 0;
                    $codeCount = 0;
                    
                    foreach ($lines as $line) {
                        if (strpos($line, 'type=') !== false) $typeCount++;
                        if (strpos($line, 'code=') !== false && strpos($line, 'code=""') === false) $codeCount++;
                    }
                    
                    echo '<div class="test-result success">';
                    echo '<i class="fas fa-check"></i> ✅ ไฟล์ XML พบแล้ว<br>';
                    echo "📊 พบประเภทไอเท็ม: {$typeCount} ประเภท<br>";
                    echo "🔢 พบรหัส: {$codeCount} รหัส<br>";
                    echo "📏 ขนาดไฟล์: " . number_format(strlen($xmlContent)) . " ตัวอักษร";
                    echo '</div>';
                } else {
                    echo '<div class="test-result error">';
                    echo '<i class="fas fa-times"></i> ❌ ไม่พบไฟล์ optioncode.xml';
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <!-- ทดสอบฐานข้อมูล -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-database"></i> ทดสอบฐานข้อมูล</h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    $conn = db_connect();
                    
                    // ตรวจสอบตาราง
                    $checkTableSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WEB_cabal_forcecodes'";
                    $stmt = sqlsrv_query($conn, $checkTableSql);
                    
                    if ($stmt) {
                        $result = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
                        
                        if ($result['count'] > 0) {
                            // ตรวจสอบข้อมูล
                            $countSql = "SELECT COUNT(*) as count FROM WEB_cabal_forcecodes";
                            $countStmt = sqlsrv_query($conn, $countSql);
                            
                            if ($countStmt) {
                                $countResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
                                
                                echo '<div class="test-result success">';
                                echo '<i class="fas fa-check"></i> ✅ เชื่อมต่อฐานข้อมูลสำเร็จ<br>';
                                echo "📋 ตาราง WEB_cabal_forcecodes มีอยู่<br>";
                                echo "📊 จำนวนข้อมูล: {$countResult['count']} รายการ";
                                echo '</div>';
                                
                                if ($countResult['count'] == 0) {
                                    echo '<div class="test-result warning">';
                                    echo '<i class="fas fa-exclamation-triangle"></i> ⚠️ ตารางว่างเปล่า - ต้องเพิ่มข้อมูล<br>';
                                    echo 'กรุณารันไฟล์ <strong>setup_forcecodes_table.sql</strong>';
                                    echo '</div>';
                                }
                            }
                        } else {
                            echo '<div class="test-result error">';
                            echo '<i class="fas fa-times"></i> ❌ ไม่พบตาราง WEB_cabal_forcecodes<br>';
                            echo 'กรุณารันไฟล์ <strong>setup_forcecodes_table.sql</strong>';
                            echo '</div>';
                        }
                    } else {
                        echo '<div class="test-result error">';
                        echo '<i class="fas fa-times"></i> ❌ ไม่สามารถตรวจสอบตารางได้';
                        echo '</div>';
                    }
                    
                    sqlsrv_close($conn);
                    
                } catch (Exception $e) {
                    echo '<div class="test-result error">';
                    echo '<i class="fas fa-times"></i> ❌ เกิดข้อผิดพลาด: ' . $e->getMessage();
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <!-- ทดสอบ API -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> ทดสอบ API</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testAPI()">ทดสอบ API</button>
                <div id="apiResult" class="mt-3"></div>
            </div>
        </div>

        <!-- ลิงก์ไปยังเครื่องมือ -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> เครื่องมือ</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="optioncode_manager.php" class="btn btn-success btn-block">
                            <i class="fas fa-cogs"></i> Option Code Manager
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="test_optioncode_tools.php" class="btn btn-warning btn-block">
                            <i class="fas fa-flask"></i> Full System Test
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="optioncode_tools_index.php" class="btn btn-info btn-block">
                            <i class="fas fa-home"></i> Tools Index
                        </a>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6">
                        <a href="check_database.php" class="btn btn-secondary btn-block" target="_blank">
                            <i class="fas fa-database"></i> Check Database
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="setup_forcecodes_table.sql" class="btn btn-dark btn-block" download>
                            <i class="fas fa-download"></i> Download SQL Setup
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/vendor/jquery/jquery.js"></script>
    <script src="../../assets/vendor/bootstrap/js/bootstrap.js"></script>
    <script>
        function testAPI() {
            $('#apiResult').html('<i class="fas fa-spinner fa-spin"></i> กำลังทดสอบ...');
            
            $.ajax({
                url: 'class_module/optioncode_api.php',
                method: 'POST',
                data: { action: 'load_data' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#apiResult').html(`
                            <div class="test-result success">
                                <i class="fas fa-check"></i> ✅ API ทำงานสำเร็จ<br>
                                📊 ประเภทไอเท็ม: ${response.stats.totalTypes}<br>
                                🔢 รหัสทั้งหมด: ${response.stats.totalCodes}<br>
                                ✅ เชื่อมโยงแล้ว: ${response.stats.mappedCodes}<br>
                                ❌ ยังไม่เชื่อมโยง: ${response.stats.unmappedCodes}
                            </div>
                        `);
                    } else {
                        $('#apiResult').html(`
                            <div class="test-result error">
                                <i class="fas fa-times"></i> ❌ API Error: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    $('#apiResult').html(`
                        <div class="test-result error">
                            <i class="fas fa-times"></i> ❌ ไม่สามารถเรียก API ได้: ${error}
                        </div>
                    `);
                }
            });
        }
    </script>
</body>
</html>
