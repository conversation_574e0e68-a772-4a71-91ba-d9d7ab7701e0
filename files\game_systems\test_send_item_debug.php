<?php
// Test send_item.php functionality
session_start();
require_once '../../_app/dbinfo.inc.php';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Send Item Debug</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <h2><i class="fas fa-bug me-2"></i>Test Send Item Debug</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Critical Issues Found</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6>🚨 Major Problem Identified:</h6>
                            <p><strong>Missing Database Connection</strong> in send_item.php</p>
                            <p>The script was using <code>$conn</code> without creating the connection!</p>
                        </div>
                        
                        <h6>✅ Fixed Issues:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added <code>$conn = db_connect()</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added <code>sqlsrv_begin_transaction($conn)</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-edit text-info me-2"></i>
                                Fixed path in Advanced Editor: <code>send_item.php</code>
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-plus text-success me-2"></i>
                                Added session_start() for userLogin
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-test-tube me-2"></i>Test Send Item</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Data</h6>
                            <p>Session User: <code><?php echo $_SESSION['userLogin'] ?? 'NOT SET'; ?></code></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Player Username</label>
                            <input type="text" id="testPlayer" class="form-control" value="testplayer" placeholder="Enter player username">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Item Code</label>
                            <input type="text" id="testItemCode" class="form-control" value="1" placeholder="Item code">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Options Code</label>
                            <input type="text" id="testOptionsCode" class="form-control" value="0" placeholder="Options code">
                        </div>
                        
                        <button id="testSendItem" class="btn btn-primary me-2">
                            <i class="fas fa-paper-plane me-2"></i>Test Send Item
                        </button>
                        
                        <button id="checkLogs" class="btn btn-success">
                            <i class="fas fa-list me-2"></i>Check Admin Logs
                        </button>
                        
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-database me-2"></i>Recent Admin Logs</h5>
                    </div>
                    <div class="card-body">
                        <div id="adminLogs">
                            <?php
                            try {
                                $conn = db_connect();
                                if ($conn) {
                                    $query = "SELECT TOP 10 id, admin_username, action, target_player, details, ip_address, created_at 
                                             FROM admin_logs 
                                             ORDER BY created_at DESC";
                                    $result = sqlsrv_query($conn, $query);
                                    
                                    if ($result && sqlsrv_has_rows($result)) {
                                        echo '<div class="table-responsive">';
                                        echo '<table class="table table-striped table-sm">';
                                        echo '<thead class="table-dark">';
                                        echo '<tr><th>ID</th><th>User ID</th><th>Action</th><th>Target</th><th>Details</th><th>IP</th><th>Created At</th></tr>';
                                        echo '</thead><tbody>';
                                        
                                        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                                            $createdAt = is_object($row['created_at']) ? 
                                                $row['created_at']->format('Y-m-d H:i:s') : $row['created_at'];
                                            $details = $row['details'] ? 
                                                (strlen($row['details']) > 30 ? substr($row['details'], 0, 30) . '...' : $row['details']) : '-';
                                            
                                            echo '<tr>';
                                            echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                                            echo '<td><code>' . htmlspecialchars($row['admin_username']) . '</code></td>';
                                            echo '<td>' . htmlspecialchars($row['action']) . '</td>';
                                            echo '<td>' . htmlspecialchars($row['target_player'] ?: '-') . '</td>';
                                            echo '<td><small>' . htmlspecialchars($details) . '</small></td>';
                                            echo '<td>' . htmlspecialchars($row['ip_address'] ?: '-') . '</td>';
                                            echo '<td>' . htmlspecialchars($createdAt) . '</td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody></table>';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-warning">No admin logs found</div>';
                                    }
                                    
                                    sqlsrv_close($conn);
                                } else {
                                    echo '<div class="alert alert-danger">Database connection failed</div>';
                                }
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Debug Checklist</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ Fixed Issues:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">Database connection added</li>
                                    <li class="list-group-item">Transaction management added</li>
                                    <li class="list-group-item">Session handling fixed</li>
                                    <li class="list-group-item">Path corrections made</li>
                                    <li class="list-group-item">ItemType "NOT" support added</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🔍 Debug Steps:</h6>
                                <ol class="list-group list-group-numbered">
                                    <li class="list-group-item">Check PHP error log for debug messages</li>
                                    <li class="list-group-item">Test send item with button above</li>
                                    <li class="list-group-item">Verify admin_logs table updates</li>
                                    <li class="list-group-item">Check browser console for errors</li>
                                    <li class="list-group-item">Test in Advanced Editor</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6>Expected Debug Messages in PHP Error Log:</h6>
                            <div class="font-monospace small">
                                Database connected and transaction started<br>
                                Send Item API called<br>
                                Session userLogin: [value]<br>
                                Final admin_username: [value]<br>
                                Preparing admin_logs insert with data: [JSON]<br>
                                Admin log inserted successfully for user: [user], action: Send Item, target: [player]
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#testSendItem').click(async function() {
                const resultsDiv = $('#testResults');
                resultsDiv.html('<div class="alert alert-info">Testing send item...</div>');
                
                const testData = {
                    playerUsername: $('#testPlayer').val(),
                    itemCode: $('#testItemCode').val(),
                    optionsCode: $('#testOptionsCode').val(),
                    quantity: 1,
                    duration: 31,
                    sendMethod: 'inventory',
                    adminUsername: '<?php echo $_SESSION['userLogin'] ?? 'TestAdmin'; ?>'
                };
                
                try {
                    console.log('Sending test data:', testData);
                    
                    const response = await fetch('send_item.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(testData)
                    });
                    
                    console.log('Response status:', response.status);
                    const data = await response.json();
                    console.log('Response data:', data);
                    
                    if (data.success) {
                        resultsDiv.html(`
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-2"></i>Test Successful!</h6>
                                <p><strong>Message:</strong> ${data.message}</p>
                                <p><strong>Send ID:</strong> ${data.send_id || 'N/A'}</p>
                                <p class="mb-0">Check admin_logs table for new entry!</p>
                            </div>
                        `);
                    } else {
                        resultsDiv.html(`
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times me-2"></i>Test Failed!</h6>
                                <p><strong>Error:</strong> ${data.error}</p>
                            </div>
                        `);
                    }
                    
                } catch (error) {
                    console.error('Test error:', error);
                    resultsDiv.html(`
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Request Failed!</h6>
                            <p><strong>Error:</strong> ${error.message}</p>
                        </div>
                    `);
                }
            });
            
            $('#checkLogs').click(function() {
                location.reload();
            });
        });
    </script>
</body>
</html>
